import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/admin_statistics_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/notifications_provider.dart';
import '../widgets/logout_loading_screen.dart';
import 'admin_statistics_screen.dart';
import 'admin_users_management_screen.dart';
import 'admin_posts_management_screen.dart';
import 'admin_settings_screen.dart';
import 'admin_user_actions_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل الإحصائيات عند فتح لوحة التحكم
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AdminStatisticsProvider>().loadStatistics();

      // الاستماع لتغييرات حالة المصادقة لإلغاء الاستماعات عند تسجيل الخروج
      final authProvider = context.read<AuthProvider>();
      final notificationsProvider = context.read<NotificationsProvider>();

      authProvider.addListener(() {
        if (!authProvider.isSignedIn) {
          // تم تسجيل الخروج، إلغاء جميع الاستماعات
          notificationsProvider.setCurrentUser(null);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // إذا كان في حالة تسجيل خروج، عرض شاشة التحميل
        if (authProvider.isLoggingOut) {
          return LogoutLoadingScreen(
            title: 'جاري تسجيل الخروج من لوحة التحكم...',
            subtitle: 'شكراً لك على إدارة التطبيق',
            duration: const Duration(seconds: 3),
            gradientColors: [
              Colors.indigo.shade500,
              Colors.indigo.shade700,
              Colors.indigo.shade900,
            ],
            onComplete: () {
              if (mounted) {
                Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
              }
            },
          );
        }

        return _buildDashboard();
      },
    );
  }

  Widget _buildDashboard() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'لوحة تحكم المدير',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.indigo.shade700,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              _showLogoutDialog();
            },
            icon: const Icon(Icons.logout_rounded),
            tooltip: 'تسجيل الخروج',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترحيب
            _buildWelcomeCard(),
            const SizedBox(height: 24),
            
            // إحصائيات سريعة
            _buildQuickStats(),
            const SizedBox(height: 24),
            
            // قائمة الإدارة
            _buildManagementGrid(),
            const SizedBox(height: 24),
            
            // إجراءات سريعة
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  // بطاقة الترحيب
  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.indigo.shade600,
              Colors.indigo.shade800,
            ],
          ),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.admin_panel_settings_rounded,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'مرحباً بك في لوحة التحكم',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'إدارة شاملة لتطبيق المخططات الذهنية',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // إحصائيات سريعة
  Widget _buildQuickStats() {
    return Consumer<AdminStatisticsProvider>(
      builder: (context, provider, child) {
        final stats = provider.statistics;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'المستخدمين',
                    stats.totalUsers.toString(),
                    Icons.people_rounded,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'المخططات',
                    stats.totalMindMaps.toString(),
                    Icons.account_tree_rounded,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'المنشورات',
                    stats.totalPosts.toString(),
                    Icons.post_add_rounded,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'التعليقات',
                    stats.totalComments.toString(),
                    Icons.comment_rounded,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // شبكة الإدارة
  Widget _buildManagementGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إدارة التطبيق',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.1,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            _buildManagementCard(
              'الإحصائيات التفصيلية',
              Icons.analytics_rounded,
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AdminStatisticsScreen(),
                ),
              ),
            ),
            _buildManagementCard(
              'إدارة المستخدمين',
              Icons.people_rounded,
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AdminUsersManagementScreen(),
                ),
              ),
            ),
            _buildManagementCard(
              'إدارة المنشورات',
              Icons.post_add_rounded,
              Colors.orange,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AdminPostsManagementScreen(),
                ),
              ),
            ),
            _buildManagementCard(
              'إعدادات النظام',
              Icons.settings_rounded,
              Colors.purple,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AdminSettingsScreen(),
                ),
              ),
            ),
            _buildManagementCard(
              'إجراءات المستخدمين',
              Icons.gavel_rounded,
              Colors.red,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AdminUserActionsScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بطاقة إدارة
  Widget _buildManagementCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // إجراءات سريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إجراءات سريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                ListTile(
                  leading: Icon(Icons.refresh_rounded, color: Colors.blue.shade600),
                  title: const Text('تحديث الإحصائيات'),
                  subtitle: const Text('تحديث جميع البيانات والإحصائيات'),
                  trailing: const Icon(Icons.arrow_forward_ios_rounded),
                  onTap: () {
                    context.read<AdminStatisticsProvider>().refreshStatistics();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('جاري تحديث الإحصائيات...'),
                        backgroundColor: Colors.blue,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // حوار تسجيل الخروج
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج من لوحة التحكم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return ElevatedButton(
                onPressed: (authProvider.isLoading || authProvider.isLoggingOut) ? null : () async {
                  Navigator.pop(context); // إغلاق الحوار

                  // بدء عملية تسجيل الخروج
                  await authProvider.signOut();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: (authProvider.isLoading || authProvider.isLoggingOut)
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Text('تسجيل الخروج'),
              );
            },
          ),
        ],
      ),
    );
  }
}
