import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../screens/banned_user_screen.dart';

class BanMonitorService {
  static StreamSubscription<DatabaseEvent>? _banSubscription;
  static bool _isMonitoring = false;
  static Function(UserModel)? _onUserBanned;

  /// بدء مراقبة حالة الحظر للمستخدم الحالي
  static void startMonitoring(String userId, {Function(UserModel)? onUserBanned}) {
    if (_isMonitoring) {
      stopMonitoring();
    }

    _isMonitoring = true;
    _onUserBanned = onUserBanned;
    final database = FirebaseDatabase.instance.ref();

    // الاستماع لتغييرات في بيانات المستخدم
    _banSubscription = database
        .child('users/$userId')
        .onValue
        .listen(
          (DatabaseEvent event) async {
            try {
              if (event.snapshot.exists) {
                final userData = Map<String, dynamic>.from(
                  event.snapshot.value as Map<dynamic, dynamic>
                );

                final user = UserModel.fromMap(userData);

                // التحقق من الحظر
                if (user.isBanned == true) {
                  print('🚫 تم اكتشاف حظر المستخدم - عرض شاشة الحظر');

                  // استدعاء callback لعرض شاشة الحظر
                  if (_onUserBanned != null) {
                    _onUserBanned!(user);
                  } else {
                    // fallback: تسجيل خروج مباشر
                    await _forceLogout();
                  }
                }
              }
            } catch (e) {
              print('خطأ في معالجة بيانات المستخدم: $e');
            }
          },
          onError: (error) {
            print('خطأ في مراقبة الحظر: $error');
            // في حالة خطأ الصلاحيات، أوقف المراقبة
            if (error.toString().contains('permission-denied')) {
              print('⚠️ لا توجد صلاحيات للوصول لبيانات المستخدم - إيقاف المراقبة');
              stopMonitoring();
            }
          },
        );
  }

  /// إيقاف مراقبة حالة الحظر
  static void stopMonitoring() {
    _banSubscription?.cancel();
    _banSubscription = null;
    _isMonitoring = false;
  }

  /// إجبار تسجيل الخروج
  static Future<void> _forceLogout() async {
    try {
      // تسجيل خروج من Firebase Auth
      await FirebaseAuth.instance.signOut();
      
      // إيقاف المراقبة
      stopMonitoring();
      
      print('✅ تم تسجيل خروج المستخدم المحظور بنجاح');
    } catch (e) {
      print('خطأ في تسجيل الخروج الإجباري: $e');
    }
  }

  /// التحقق من حالة الحظر مرة واحدة
  static Future<bool> checkBanStatus(String userId) async {
    try {
      final database = FirebaseDatabase.instance.ref();
      final snapshot = await database.child('users/$userId').get();
      
      if (snapshot.exists) {
        final userData = Map<String, dynamic>.from(
          snapshot.value as Map<dynamic, dynamic>
        );
        final user = UserModel.fromMap(userData);
        return user.isBanned == true;
      }
      
      return false;
    } catch (e) {
      print('خطأ في التحقق من حالة الحظر: $e');
      return false;
    }
  }

  /// التحقق من حالة المراقبة
  static bool get isMonitoring => _isMonitoring;
}
