import 'package:flutter/material.dart';
import '../models/mind_map_template.dart';

class TemplatePreviewWidget extends StatelessWidget {
  final MindMapTemplate template;
  final double width;
  final double height;

  const TemplatePreviewWidget({
    super.key,
    required this.template,
    this.width = 300,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CustomPaint(
          painter: TemplatePreviewPainter(template),
          size: Size(width, height),
        ),
      ),
    );
  }
}

class TemplatePreviewPainter extends CustomPainter {
  final MindMapTemplate template;

  TemplatePreviewPainter(this.template);

  @override
  void paint(Canvas canvas, Size size) {
    // حساب نطاق المواضع في القالب
    if (template.nodes.isEmpty) return;

    double minX = template.nodes.first.position.dx;
    double maxX = template.nodes.first.position.dx;
    double minY = template.nodes.first.position.dy;
    double maxY = template.nodes.first.position.dy;

    for (final node in template.nodes) {
      minX = minX < node.position.dx ? minX : node.position.dx;
      maxX = maxX > node.position.dx ? maxX : node.position.dx;
      minY = minY < node.position.dy ? minY : node.position.dy;
      maxY = maxY > node.position.dy ? maxY : node.position.dy;
    }

    // إضافة هامش للعقد
    const nodeMargin = 100.0;
    minX -= nodeMargin;
    maxX += nodeMargin;
    minY -= nodeMargin;
    maxY += nodeMargin;

    final templateWidth = maxX - minX;
    final templateHeight = maxY - minY;

    // حساب معامل التحجيم
    final scaleX = size.width / templateWidth;
    final scaleY = size.height / templateHeight;
    final scale = (scaleX < scaleY ? scaleX : scaleY) * 0.8; // 0.8 للهامش

    // حساب الإزاحة للتوسيط
    final offsetX = (size.width - templateWidth * scale) / 2 - minX * scale;
    final offsetY = (size.height - templateHeight * scale) / 2 - minY * scale;

    // رسم الروابط أولاً
    _drawConnections(canvas, scale, offsetX, offsetY);

    // رسم العقد
    _drawNodes(canvas, scale, offsetX, offsetY);
  }

  void _drawConnections(Canvas canvas, double scale, double offsetX, double offsetY) {
    final paint = Paint()
      ..strokeWidth = 1.5 * scale
      ..style = PaintingStyle.stroke;

    for (final connection in template.connections) {
      final fromNode = template.nodes.firstWhere((n) => n.id == connection.fromNodeId);
      final toNode = template.nodes.firstWhere((n) => n.id == connection.toNodeId);

      final fromX = fromNode.position.dx * scale + offsetX;
      final fromY = fromNode.position.dy * scale + offsetY;
      final toX = toNode.position.dx * scale + offsetX;
      final toY = toNode.position.dy * scale + offsetY;

      paint.color = connection.color.withOpacity(0.7);

      // رسم خط منحني بسيط
      final path = Path();
      path.moveTo(fromX, fromY);
      
      final controlX = (fromX + toX) / 2;
      final controlY = (fromY + toY) / 2 - 20 * scale;
      
      path.quadraticBezierTo(controlX, controlY, toX, toY);
      canvas.drawPath(path, paint);
    }
  }

  void _drawNodes(Canvas canvas, double scale, double offsetX, double offsetY) {
    for (final node in template.nodes) {
      final x = node.position.dx * scale + offsetX;
      final y = node.position.dy * scale + offsetY;
      final nodeWidth = 60 * scale;
      final nodeHeight = 30 * scale;

      // رسم العقدة
      final paint = Paint()
        ..color = node.color.withOpacity(0.8)
        ..style = PaintingStyle.fill;

      final rect = RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(x, y),
          width: nodeWidth,
          height: nodeHeight,
        ),
        Radius.circular(8 * scale),
      );

      canvas.drawRRect(rect, paint);

      // رسم حدود العقدة
      final borderPaint = Paint()
        ..color = node.color.withOpacity(0.9)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1 * scale;

      canvas.drawRRect(rect, borderPaint);

      // رسم النص (مبسط)
      if (scale > 0.3) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: node.title.length > 8 ? '${node.title.substring(0, 8)}...' : node.title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 8 * scale,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        );

        textPainter.layout(maxWidth: nodeWidth - 8 * scale);
        
        final textOffset = Offset(
          x - textPainter.width / 2,
          y - textPainter.height / 2,
        );

        textPainter.paint(canvas, textOffset);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! TemplatePreviewPainter || oldDelegate.template != template;
  }
}
