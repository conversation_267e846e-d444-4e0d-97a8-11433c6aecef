import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';

class AdminHelper {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;

  // قائمة بريد إلكتروني للمدراء (يمكن تخصيصها حسب الحاجة)
  static const List<String> adminEmails = [
    '<EMAIL>',
    '<EMAIL>', // للاختبار
    '<EMAIL>', // حساب مدير للاختبار
    // أضف بريدك الإلكتروني هنا لتصبح مدير
  ];

  // التحقق من كون البريد الإلكتروني مدير
  static bool isAdminEmail(String email) {
    return adminEmails.contains(email.toLowerCase());
  }

  // تعيين مستخدم كمدير في قاعدة البيانات
  static Future<bool> makeUserAdmin(String userId, String email) async {
    try {
      if (!isAdminEmail(email)) {
        debugPrint('❌ البريد الإلكتروني $email غير مدرج في قائمة المدراء');
        return false;
      }

      final userRef = _database.ref('users/$userId');
      await userRef.update({
        'isAdmin': true,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ تم تعيين المستخدم $email كمدير بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تعيين المستخدم كمدير: $e');
      return false;
    }
  }

  // إزالة صلاحيات المدير من مستخدم
  static Future<bool> removeAdminPrivileges(String userId) async {
    try {
      final userRef = _database.ref('users/$userId');
      await userRef.update({
        'isAdmin': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ تم إزالة صلاحيات المدير بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إزالة صلاحيات المدير: $e');
      return false;
    }
  }

  // التحقق من جميع المستخدمين وتعيين المدراء تلقائياً
  static Future<void> checkAndSetAdmins() async {
    try {
      final usersRef = _database.ref('users');
      final snapshot = await usersRef.get();

      if (!snapshot.exists) {
        debugPrint('📝 لا توجد بيانات مستخدمين');
        return;
      }

      int adminCount = 0;
      for (final child in snapshot.children) {
        final userData = Map<String, dynamic>.from(child.value as Map);
        final email = userData['email']?.toString() ?? '';
        final userId = child.key!;
        final isCurrentlyAdmin = userData['isAdmin'] ?? false;

        if (isAdminEmail(email) && !isCurrentlyAdmin) {
          await makeUserAdmin(userId, email);
          adminCount++;
        }
      }

      debugPrint('✅ تم فحص المستخدمين وتعيين $adminCount مدير جديد');
    } catch (e) {
      debugPrint('❌ خطأ في فحص وتعيين المدراء: $e');
    }
  }

  // الحصول على قائمة جميع المدراء
  static Future<List<Map<String, dynamic>>> getAllAdmins() async {
    try {
      final usersRef = _database.ref('users');
      final snapshot = await usersRef.get();

      if (!snapshot.exists) {
        return [];
      }

      final admins = <Map<String, dynamic>>[];
      for (final child in snapshot.children) {
        final userData = Map<String, dynamic>.from(child.value as Map);
        final isAdmin = userData['isAdmin'] ?? false;

        if (isAdmin) {
          userData['uid'] = child.key;
          admins.add(userData);
        }
      }

      return admins;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على قائمة المدراء: $e');
      return [];
    }
  }
}
