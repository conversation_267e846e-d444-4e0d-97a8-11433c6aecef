import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';
import 'user_profile_screen.dart';

class UsersSearchScreen extends StatefulWidget {
  const UsersSearchScreen({super.key});

  @override
  State<UsersSearchScreen> createState() => _UsersSearchScreenState();
}

class _UsersSearchScreenState extends State<UsersSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<UserModel> _searchResults = [];
  bool _isLoading = false;
  String _selectedUniversity = '';
  List<String> _availableUniversities = [];
  Map<String, int> _universityUserCounts = {};
  bool _isLoadingUniversities = false;
  String _selectedMajor = '';
  List<String> _availableMajors = [];
  Map<String, int> _majorUserCounts = {};
  bool _isLoadingMajors = false;

  @override
  void initState() {
    super.initState();

    // تعيين context في AuthProvider للإشعارات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().setContext(context);
    });

    _loadInitialUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialUsers() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _isLoadingUniversities = true;
        _isLoadingMajors = true;
      });
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.userModel;

    print('🔄 تحميل المستخدمين الأولي...');
    print('👤 المستخدم الحالي: ${currentUser?.email}');
    print('🏫 جامعة المستخدم: ${currentUser?.university}');

    // تحميل جميع المستخدمين أولاً
    final users = await authProvider.searchUsers(
      limit: 50,
    );

    print('📊 تم تحميل ${users.length} مستخدم');

    // استخراج الجامعات والتخصصات المتاحة من المستخدمين
    _extractAvailableUniversities(users);
    _extractAvailableMajors(users);

    if (mounted) {
      setState(() {
        _searchResults = users;
        _selectedUniversity = ''; // ابدأ بجميع الجامعات
        _selectedMajor = ''; // ابدأ بجميع التخصصات
        _isLoading = false;
        _isLoadingUniversities = false;
        _isLoadingMajors = false;
      });
    }
  }

  // استخراج الجامعات المتاحة من قائمة المستخدمين
  void _extractAvailableUniversities(List<UserModel> users) {
    final universitiesSet = <String>{};
    final universityCounts = <String, int>{};

    for (final user in users) {
      if (user.university.isNotEmpty) {
        universitiesSet.add(user.university);
        universityCounts[user.university] = (universityCounts[user.university] ?? 0) + 1;
      }
    }

    _availableUniversities = universitiesSet.toList()..sort();
    _universityUserCounts = universityCounts;
    print('🏫 الجامعات المتاحة: $_availableUniversities');
    print('📊 أعداد المستخدمين: $_universityUserCounts');
  }

  // استخراج التخصصات المتاحة من قائمة المستخدمين
  void _extractAvailableMajors(List<UserModel> users) {
    final majorsSet = <String>{};
    final majorCounts = <String, int>{};

    for (final user in users) {
      if (user.major.isNotEmpty) {
        majorsSet.add(user.major);
        majorCounts[user.major] = (majorCounts[user.major] ?? 0) + 1;
      }
    }

    _availableMajors = majorsSet.toList()..sort();
    _majorUserCounts = majorCounts;
    print('🎓 التخصصات المتاحة: $_availableMajors');
    print('📊 أعداد المستخدمين حسب التخصص: $_majorUserCounts');
  }

  // بناء عنصر الجامعة في القائمة المنسدلة
  Widget _buildUniversityItem({
    required IconData icon,
    required String name,
    required int count,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: color,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنصر التخصص في القائمة المنسدلة
  Widget _buildMajorItem({
    required IconData icon,
    required String name,
    required int count,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: color,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _searchUsers() async {
    if (mounted) {
      setState(() => _isLoading = true);
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final searchQuery = _searchController.text.trim();

    print('🔍 بحث جديد...');
    print('📝 النص: "$searchQuery"');
    print('🏫 الجامعة: "$_selectedUniversity"');
    print('🎓 التخصص: "$_selectedMajor"');

    var users = await authProvider.searchUsers(
      query: searchQuery.isEmpty ? null : searchQuery,
      university: _selectedUniversity.isEmpty ? null : _selectedUniversity,
      limit: 50,
    );

    // فلترة حسب التخصص محلياً
    if (_selectedMajor.isNotEmpty) {
      users = users.where((user) => user.major == _selectedMajor).toList();
    }

    print('✅ نتائج البحث: ${users.length} مستخدم');

    // تحديث قوائم الجامعات والتخصصات المتاحة إذا لم يكن هناك فلاتر محددة
    if (_selectedUniversity.isEmpty && _selectedMajor.isEmpty) {
      _extractAvailableUniversities(users);
      _extractAvailableMajors(users);
    }

    if (mounted) {
      setState(() {
        _searchResults = users;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'البحث عن المستخدمين',
          style: TextStyle(
            fontSize: MediaQuery.of(context).size.width < 600 ? 18 : MediaQuery.of(context).size.width < 1024 ? 20 : 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        toolbarHeight: MediaQuery.of(context).size.width < 600 ? kToolbarHeight : MediaQuery.of(context).size.width < 1024 ? kToolbarHeight + 8 : kToolbarHeight + 16,
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: EdgeInsets.all(MediaQuery.of(context).size.width <= 375 ? 6 : MediaQuery.of(context).size.width < 600 ? 8 : MediaQuery.of(context).size.width < 1024 ? 10 : 12),
            decoration: BoxDecoration(
              color: Colors.blue.shade700,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(MediaQuery.of(context).size.width < 600 ? 20 : 24),
                bottomRight: Radius.circular(MediaQuery.of(context).size.width < 600 ? 20 : 24),
              ),
            ),
            child: Column(
              children: [
                // حقل البحث
                TextField(
                  controller: _searchController,
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width <= 375 ? 13 : MediaQuery.of(context).size.width < 600 ? 14 : MediaQuery.of(context).size.width < 1024 ? 15 : 16,
                  ),
                  decoration: InputDecoration(
                    hintText: 'ابحث بالاسم، الإيميل، الجامعة، أو التخصص...',
                    hintStyle: TextStyle(
                      fontSize: MediaQuery.of(context).size.width < 600 ? 14 : MediaQuery.of(context).size.width < 1024 ? 15 : 16,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      size: MediaQuery.of(context).size.width <= 375 ? 18 : MediaQuery.of(context).size.width < 600 ? 20 : MediaQuery.of(context).size.width < 1024 ? 22 : 24,
                    ),
                    suffixIcon: IconButton(
                      icon: Icon(
                        Icons.clear,
                        size: MediaQuery.of(context).size.width < 600 ? 20 : MediaQuery.of(context).size.width < 1024 ? 22 : 24,
                      ),
                      onPressed: () {
                        _searchController.clear();
                        _searchUsers();
                      },
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width <= 375 ? 12 : MediaQuery.of(context).size.width < 600 ? 14 : MediaQuery.of(context).size.width < 1024 ? 16 : 18,
                      vertical: MediaQuery.of(context).size.width <= 375 ? 8 : MediaQuery.of(context).size.width < 600 ? 10 : MediaQuery.of(context).size.width < 1024 ? 12 : 14,
                    ),
                  ),
                  onSubmitted: (_) => _searchUsers(),
                ),
                SizedBox(height: MediaQuery.of(context).size.width <= 375 ? 4 : MediaQuery.of(context).size.width < 600 ? 6 : MediaQuery.of(context).size.width < 1024 ? 8 : 10),
                
                // فلتر الجامعة
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(MediaQuery.of(context).size.width <= 375 ? 3 : MediaQuery.of(context).size.width < 600 ? 4 : MediaQuery.of(context).size.width < 1024 ? 5 : 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width <= 375 ? 4 : MediaQuery.of(context).size.width < 600 ? 6 : MediaQuery.of(context).size.width < 1024 ? 8 : 10),
                      ),
                      child: Icon(
                        Icons.school,
                        color: Colors.white,
                        size: MediaQuery.of(context).size.width <= 375 ? 14 : MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 18 : 20,
                      ),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width <= 375 ? 8 : MediaQuery.of(context).size.width < 600 ? 10 : MediaQuery.of(context).size.width < 1024 ? 12 : 14),
                    Text(
                      'الجامعة:',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: MediaQuery.of(context).size.width <= 375 ? 11 : MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16,
                      ),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: MediaQuery.of(context).size.width <= 375 ? 8 : MediaQuery.of(context).size.width < 600 ? 10 : MediaQuery.of(context).size.width < 1024 ? 12 : 14,
                          vertical: MediaQuery.of(context).size.width <= 375 ? 1 : MediaQuery.of(context).size.width < 600 ? 2 : MediaQuery.of(context).size.width < 1024 ? 3 : 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: MediaQuery.of(context).size.width < 600 ? 4 : MediaQuery.of(context).size.width < 1024 ? 6 : 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: DropdownButtonHideUnderline(
                          child: _isLoadingUniversities
                              ? Row(
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'جاري تحميل الجامعات...',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                )
                              : DropdownButton<String>(
                                  value: _selectedUniversity.isEmpty ? null : _selectedUniversity,
                                  hint: Row(
                                    children: [
                                      Icon(Icons.public, color: Colors.grey.shade600, size: 18),
                                      const SizedBox(width: 8),
                                      Text(
                                        'جميع الجامعات',
                                        style: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                  isExpanded: true,
                                  icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade600),
                                  items: [
                                    DropdownMenuItem<String>(
                                      value: '',
                                      child: _buildUniversityItem(
                                        icon: Icons.public,
                                        name: 'جميع الجامعات',
                                        count: _searchResults.length,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    ..._availableUniversities.map((university) {
                                      final count = _universityUserCounts[university] ?? 0;
                                      return DropdownMenuItem<String>(
                                        value: university,
                                        child: _buildUniversityItem(
                                          icon: Icons.school,
                                          name: university,
                                          count: count,
                                          color: Colors.green,
                                        ),
                                      );
                                    }),
                                  ],
                                  onChanged: (value) {
                                    if (mounted) {
                                      setState(() {
                                        _selectedUniversity = value ?? '';
                                      });
                                      _searchUsers();
                                    }
                                  },
                                ),
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: MediaQuery.of(context).size.width <= 375 ? 4 : MediaQuery.of(context).size.width < 600 ? 6 : MediaQuery.of(context).size.width < 1024 ? 8 : 10),

                // فلتر التخصص
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(MediaQuery.of(context).size.width <= 375 ? 3 : MediaQuery.of(context).size.width < 600 ? 4 : MediaQuery.of(context).size.width < 1024 ? 5 : 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width <= 375 ? 4 : MediaQuery.of(context).size.width < 600 ? 6 : MediaQuery.of(context).size.width < 1024 ? 8 : 10),
                      ),
                      child: Icon(
                        Icons.work,
                        color: Colors.white,
                        size: MediaQuery.of(context).size.width <= 375 ? 14 : MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 18 : 20,
                      ),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width <= 375 ? 6 : MediaQuery.of(context).size.width < 600 ? 8 : MediaQuery.of(context).size.width < 1024 ? 10 : 12),
                    Text(
                      'التخصص:',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: MediaQuery.of(context).size.width <= 375 ? 11 : MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: MediaQuery.of(context).size.width <= 375 ? 8 : MediaQuery.of(context).size.width < 600 ? 10 : MediaQuery.of(context).size.width < 1024 ? 12 : 14,
                          vertical: MediaQuery.of(context).size.width <= 375 ? 1 : MediaQuery.of(context).size.width < 600 ? 2 : MediaQuery.of(context).size.width < 1024 ? 3 : 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: DropdownButtonHideUnderline(
                          child: _isLoadingMajors
                              ? Row(
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'جاري تحميل التخصصات...',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                )
                              : DropdownButton<String>(
                                  value: _selectedMajor.isEmpty ? null : _selectedMajor,
                                  hint: Row(
                                    children: [
                                      Icon(Icons.work_outline, color: Colors.grey.shade600, size: 18),
                                      const SizedBox(width: 8),
                                      Text(
                                        'جميع التخصصات',
                                        style: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                  isExpanded: true,
                                  icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade600),
                                  items: [
                                    DropdownMenuItem<String>(
                                      value: '',
                                      child: _buildMajorItem(
                                        icon: Icons.work_outline,
                                        name: 'جميع التخصصات',
                                        count: _searchResults.length,
                                        color: Colors.purple,
                                      ),
                                    ),
                                    ..._availableMajors.map((major) {
                                      final count = _majorUserCounts[major] ?? 0;
                                      return DropdownMenuItem<String>(
                                        value: major,
                                        child: _buildMajorItem(
                                          icon: Icons.work,
                                          name: major,
                                          count: count,
                                          color: Colors.orange,
                                        ),
                                      );
                                    }),
                                  ],
                                  onChanged: (value) {
                                    if (mounted) {
                                      setState(() {
                                        _selectedMajor = value ?? '';
                                      });
                                      _searchUsers();
                                    }
                                  },
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // النتائج
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _searchResults.isEmpty
                    ? RefreshIndicator(
                        onRefresh: _refreshSearchResults,
                        color: Colors.blue.shade600,
                        backgroundColor: Colors.white,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: MediaQuery.of(context).size.height * 0.6,
                            child: const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.people_outline, size: 64, color: Colors.grey),
                                  SizedBox(height: 16),
                                  Text(
                                    'لا توجد نتائج',
                                    style: TextStyle(fontSize: 18, color: Colors.grey),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'جرب البحث بكلمات مختلفة أو اسحب للتحديث',
                                    style: TextStyle(color: Colors.grey),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshSearchResults,
                        color: Colors.blue.shade600,
                        backgroundColor: Colors.white,
                        child: ListView.builder(
                          padding: EdgeInsets.all(MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 20 : 24),
                          itemCount: _searchResults.length,
                          itemBuilder: (context, index) {
                            final user = _searchResults[index];
                            return _buildUserCard(user);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.userModel;
        final isFollowing = currentUser?.isFollowing(user.uid) ?? false;
        
        return Card(
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16,
          ),
          elevation: MediaQuery.of(context).size.width < 600 ? 4 : MediaQuery.of(context).size.width < 1024 ? 5 : 6,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 14 : 16),
          ),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => UserProfileScreen(user: user),
                ),
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 18 : 20),
              child: Row(
                children: [
                  // صورة المستخدم مع مؤشر حالة النشاط
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: MediaQuery.of(context).size.width <= 375 ? 25 : MediaQuery.of(context).size.width < 600 ? 30 : MediaQuery.of(context).size.width < 1024 ? 35 : 40,
                        backgroundColor: Colors.blue.shade100,
                        child: Text(
                          _getAuthorInitial(user.fullName),
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width < 600 ? 24 : MediaQuery.of(context).size.width < 1024 ? 26 : 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                      // مؤشر حالة النشاط (حسب إعدادات الخصوصية)
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          final currentUserId = authProvider.user?.uid;
                          final canViewStatus = user.canViewOnlineStatus(currentUserId);

                          if (!canViewStatus) return const SizedBox.shrink();

                          return Positioned(
                            bottom: 2,
                            right: 2,
                            child: Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: user.isActive ? Colors.green : Colors.grey,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  SizedBox(width: MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 18 : 20),

                  // معلومات المستخدم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.fullName,
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 17 : 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.width < 600 ? 4 : MediaQuery.of(context).size.width < 1024 ? 5 : 6),
                        Text(
                          user.university,
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width < 600 ? 14 : MediaQuery.of(context).size.width < 1024 ? 15 : 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          user.major,
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width < 600 ? 14 : MediaQuery.of(context).size.width < 1024 ? 15 : 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        
                        // إحصائيات المتابعة (حسب إعدادات الخصوصية)
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            final currentUserId = authProvider.user?.uid;
                            final canViewFollowers = user.canViewFollowers(currentUserId);
                            final canViewFollowing = user.canViewFollowing(currentUserId);

                            return Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children: [
                                // عرض المتابعين فقط إذا كان مسموحاً
                                if (canViewFollowers)
                                  _buildStatChip('${user.followersCount} متابع', Icons.people),
                                // عرض المتابَعين فقط إذا كان مسموحاً
                                if (canViewFollowing)
                                  _buildStatChip('${user.followingCount} متابَع', Icons.person_add),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  
                  // زر المتابعة (إذا كان يسمح بالمتابعة أو متابع بالفعل)
                  if ((user.allowFollowing ?? true) || isFollowing)
                    ElevatedButton(
                      onPressed: authProvider.isUserLoading(user.uid)
                          ? null
                          : (isFollowing || (user.allowFollowing ?? true))
                              ? () => _toggleFollowUser(user, isFollowing, authProvider)
                              : null, // تعطيل الزر إذا كان لا يسمح بالمتابعة وليس متابعاً
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isFollowing ? Colors.grey.shade300 : Colors.blue.shade700,
                        foregroundColor: isFollowing ? Colors.black87 : Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width < 600 ? 20 : MediaQuery.of(context).size.width < 1024 ? 22 : 24),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: MediaQuery.of(context).size.width < 600 ? 16 : MediaQuery.of(context).size.width < 1024 ? 18 : 20,
                          vertical: MediaQuery.of(context).size.width < 600 ? 8 : MediaQuery.of(context).size.width < 1024 ? 9 : 10,
                        ),
                      ),
                      child: authProvider.isUserLoading(user.uid)
                          ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  isFollowing ? Colors.black54 : Colors.white,
                                ),
                              ),
                            )
                          : Text(
                              isFollowing
                                  ? 'إلغاء المتابعة'
                                  : (user.allowFollowing ?? true)
                                      ? 'متابعة'
                                      : 'لا يسمح بالمتابعة',
                              style: TextStyle(
                                fontSize: MediaQuery.of(context).size.width < 600 ? 12 : MediaQuery.of(context).size.width < 1024 ? 13 : 14,
                              ),
                            ),
                    )
                  else
                    // رسالة عدم السماح بالمتابعة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade400),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.lock, size: 14, color: Colors.grey.shade600),
                          const SizedBox(width: 4),
                          Text(
                            'لا يسمح بالمتابعة',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.blue.shade700),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }

  // ==================== دوال التجاوب مع أحجام الشاشات ====================

  /// حساب عرض الشاشة وتحديد نوع الجهاز
  double get screenWidth => MediaQuery.of(context).size.width;
  double get screenHeight => MediaQuery.of(context).size.height;

  /// تحديد نوع الجهاز حسب عرض الشاشة
  DeviceType get deviceType {
    if (screenWidth < 600) {
      return DeviceType.mobile;
    } else if (screenWidth < 1024) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// حساب padding للحاوية الرئيسية
  EdgeInsets get mainPadding {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(24);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 24);
    }
  }

  /// حساب spacing بين العناصر
  double get elementSpacing {
    switch (deviceType) {
      case DeviceType.mobile:
        return 12;
      case DeviceType.tablet:
        return 16;
      case DeviceType.desktop:
        return 20;
    }
  }

  /// حساب حجم الخط للعناوين
  double get titleFontSize {
    switch (deviceType) {
      case DeviceType.mobile:
        return 16;
      case DeviceType.tablet:
        return 18;
      case DeviceType.desktop:
        return 20;
    }
  }

  /// حساب حجم الخط للنصوص العادية
  double get bodyFontSize {
    switch (deviceType) {
      case DeviceType.mobile:
        return 14;
      case DeviceType.tablet:
        return 15;
      case DeviceType.desktop:
        return 16;
    }
  }

  /// حساب حجم الأيقونات
  double get iconSize {
    switch (deviceType) {
      case DeviceType.mobile:
        return 20;
      case DeviceType.tablet:
        return 22;
      case DeviceType.desktop:
        return 24;
    }
  }

  /// حساب ارتفاع حقول الإدخال
  double get inputHeight {
    switch (deviceType) {
      case DeviceType.mobile:
        return 48;
      case DeviceType.tablet:
        return 52;
      case DeviceType.desktop:
        return 56;
    }
  }

  /// حساب padding للحقول
  EdgeInsets get inputPadding {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 10);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
    }
  }

  /// حساب border radius للعناصر
  double get borderRadius {
    switch (deviceType) {
      case DeviceType.mobile:
        return 8;
      case DeviceType.tablet:
        return 10;
      case DeviceType.desktop:
        return 12;
    }
  }

  /// حساب عدد الأعمدة للشبكة (إذا كانت مطلوبة)
  int get gridColumns {
    switch (deviceType) {
      case DeviceType.mobile:
        return 1;
      case DeviceType.tablet:
        return 2;
      case DeviceType.desktop:
        return 3;
    }
  }

  /// حساب الحد الأقصى لعرض المحتوى
  double get maxContentWidth {
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth;
      case DeviceType.tablet:
        return 800;
      case DeviceType.desktop:
        return 1200;
    }
  }

  /// حساب padding للبطاقات
  EdgeInsets get cardPadding {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(12);
      case DeviceType.tablet:
        return const EdgeInsets.all(16);
      case DeviceType.desktop:
        return const EdgeInsets.all(20);
    }
  }

  /// حساب margin للبطاقات
  EdgeInsets get cardMargin {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
    }
  }

  /// حساب ارتفاع الصور الشخصية
  double get avatarSize {
    switch (deviceType) {
      case DeviceType.mobile:
        return 50;
      case DeviceType.tablet:
        return 60;
      case DeviceType.desktop:
        return 70;
    }
  }

  /// حساب ارتفاع الأزرار
  double get buttonHeight {
    switch (deviceType) {
      case DeviceType.mobile:
        return 40;
      case DeviceType.tablet:
        return 44;
      case DeviceType.desktop:
        return 48;
    }
  }

  /// حساب padding للأزرار
  EdgeInsets get buttonPadding {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 10);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
    }
  }

  /// تحديد ما إذا كان يجب عرض العناصر في صف أو عمود
  bool get shouldUseRow => deviceType != DeviceType.mobile;

  /// تحديد ما إذا كان يجب عرض الشريط الجانبي
  bool get shouldShowSidebar => deviceType == DeviceType.desktop;

  /// حساب عرض الشريط الجانبي
  double get sidebarWidth {
    switch (deviceType) {
      case DeviceType.mobile:
        return 0;
      case DeviceType.tablet:
        return 250;
      case DeviceType.desktop:
        return 300;
    }
  }

  /// حساب spacing للشبكة
  double get gridSpacing {
    switch (deviceType) {
      case DeviceType.mobile:
        return 8;
      case DeviceType.tablet:
        return 12;
      case DeviceType.desktop:
        return 16;
    }
  }

  /// حساب الحد الأدنى لارتفاع العناصر
  double get minItemHeight {
    switch (deviceType) {
      case DeviceType.mobile:
        return 80;
      case DeviceType.tablet:
        return 90;
      case DeviceType.desktop:
        return 100;
    }
  }

  /// تحديد اتجاه التخطيط للفلاتر
  Axis get filtersDirection {
    return deviceType == DeviceType.mobile ? Axis.vertical : Axis.horizontal;
  }

  /// حساب عرض الفلاتر
  double get filterWidth {
    if (deviceType == DeviceType.mobile) {
      return screenWidth - 32; // full width minus padding
    } else {
      return (screenWidth - 64) / 2; // half width for tablet/desktop
    }
  }

  /// حساب ارتفاع AppBar
  double get appBarHeight {
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight;
      case DeviceType.tablet:
        return kToolbarHeight + 8;
      case DeviceType.desktop:
        return kToolbarHeight + 16;
    }
  }

  /// تحديد ما إذا كان يجب استخدام تخطيط مضغوط
  bool get useCompactLayout => deviceType == DeviceType.mobile;

  /// حساب عدد العناصر المرئية في الصفحة
  int get itemsPerPage {
    switch (deviceType) {
      case DeviceType.mobile:
        return 10;
      case DeviceType.tablet:
        return 15;
      case DeviceType.desktop:
        return 20;
    }
  }

  // ==================== تحسينات دقيقة للهواتف المختلفة ====================

  /// تحديد إذا كان الهاتف صغير (مثل iPhone 7)
  bool get isSmallPhone => screenWidth <= 375;

  /// تحديد إذا كان الهاتف كبير (مثل iPhone 15)
  bool get isLargePhone => screenWidth > 375 && screenWidth < 600;

  /// حساب padding محسن للهواتف المختلفة
  EdgeInsets get optimizedMobilePadding {
    if (isSmallPhone) {
      return const EdgeInsets.all(12); // أصغر للهواتف الصغيرة
    } else if (isLargePhone) {
      return const EdgeInsets.all(16); // عادي للهواتف الكبيرة
    } else {
      return mainPadding; // القيم الافتراضية
    }
  }

  /// حساب حجم الخط المحسن للهواتف
  double get optimizedMobileFontSize {
    if (isSmallPhone) {
      return 13; // أصغر قليلاً للهواتف الصغيرة
    } else if (isLargePhone) {
      return 14; // عادي للهواتف الكبيرة
    } else {
      return bodyFontSize; // القيم الافتراضية
    }
  }

  /// حساب حجم الأيقونة المحسن للهواتف
  double get optimizedMobileIconSize {
    if (isSmallPhone) {
      return 18; // أصغر للهواتف الصغيرة
    } else if (isLargePhone) {
      return 20; // عادي للهواتف الكبيرة
    } else {
      return iconSize; // القيم الافتراضية
    }
  }

  /// حساب نصف قطر الصورة المحسن للهواتف
  double get optimizedMobileAvatarRadius {
    if (isSmallPhone) {
      return 25; // أصغر للهواتف الصغيرة
    } else if (isLargePhone) {
      return 30; // عادي للهواتف الكبيرة
    } else {
      return avatarSize / 2; // القيم الافتراضية
    }
  }

  /// حساب المسافات المحسنة للهواتف
  double get optimizedMobileSpacing {
    if (isSmallPhone) {
      return 10; // أصغر للهواتف الصغيرة
    } else if (isLargePhone) {
      return 12; // عادي للهواتف الكبيرة
    } else {
      return elementSpacing; // القيم الافتراضية
    }
  }

  /// حساب ارتفاع البطاقة المحسن للهواتف
  double get optimizedMobileCardHeight {
    if (isSmallPhone) {
      return 85; // أقل ارتفاع للهواتف الصغيرة
    } else if (isLargePhone) {
      return 95; // ارتفاع عادي للهواتف الكبيرة
    } else {
      return minItemHeight; // القيم الافتراضية
    }
  }

  // إعادة تحميل نتائج البحث
  Future<void> _refreshSearchResults() async {
    try {
      // إعادة تحميل البيانات الأولية
      await _loadInitialUsers();

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.refresh, color: Colors.white),
                SizedBox(width: 8),
                Text('تم تحديث النتائج بنجاح'),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // إظهار رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 8),
                Text('فشل في تحديث النتائج'),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('❌ خطأ في إعادة تحميل نتائج البحث: $e');
    }
  }

  // دالة متابعة/إلغاء متابعة المستخدم مع مربعات الحوار
  Future<void> _toggleFollowUser(UserModel user, bool isFollowing, AuthProvider authProvider) async {
    // حفظ المعلومات قبل العملية غير المتزامنة
    final userName = user.fullName;
    final navigator = Navigator.of(context);

    bool success;
    if (isFollowing) {
      success = await authProvider.unfollowUser(user.uid);
    } else {
      // التحقق من تقييد المتابعة
      final currentUser = authProvider.userModel;
      if (currentUser != null) {
        final restriction = await RestrictionService.checkFollowingRestriction(currentUser.uid);
        if (restriction != null && mounted) {
          await RestrictionDialog.show(context, restriction, 'متابعة المستخدمين');
          return;
        }
      }
      success = await authProvider.followUser(user.uid);
    }

    if (!mounted) return;

    if (success) {
      if (isFollowing) {
        _showSuccessDialog(
          navigator,
          title: 'تم بنجاح!',
          message: 'تم إلغاء متابعة $userName',
          icon: Icons.check_circle,
          color: Colors.blue,
        );
      } else {
        _showSuccessDialog(
          navigator,
          title: 'تم بنجاح!',
          message: 'تم متابعة $userName بنجاح',
          icon: Icons.check_circle,
          color: Colors.green,
        );
      }
    } else {
      final errorMessage = authProvider.errorMessage ?? 'فشل في العملية';
      _showErrorDialog(
        navigator,
        title: isFollowing ? 'خطأ في إلغاء المتابعة' : 'خطأ في المتابعة',
        message: errorMessage,
      );
    }
  }

  // عرض حوار النجاح
  void _showSuccessDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
    required IconData icon,
    required Color color,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض حوار الخطأ
  void _showErrorDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 36,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// تعداد أنواع الأجهزة
enum DeviceType {
  mobile,   // الهواتف الذكية (< 600px)
  tablet,   // الأجهزة اللوحية (600px - 1024px)
  desktop,  // أجهزة سطح المكتب (> 1024px)
}
