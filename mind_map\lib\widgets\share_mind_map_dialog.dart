import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/mind_map.dart';
import '../models/post.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/mind_map_provider.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';
import 'post_audience_selector.dart';

class ShareMindMapDialog extends StatefulWidget {
  final MindMap mindMap;

  const ShareMindMapDialog({
    super.key,
    required this.mindMap,
  });

  @override
  State<ShareMindMapDialog> createState() => _ShareMindMapDialogState();
}

class _ShareMindMapDialogState extends State<ShareMindMapDialog> {
  final _descriptionController = TextEditingController();
  bool _isSharing = false;
  PostAudience _selectedAudience = PostAudience.everyone;
  int _audienceCount = 0;
  bool _isLoadingCount = false;

  @override
  void initState() {
    super.initState();
    _calculateAudienceCount();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  // حساب عدد المستخدمين الذين يمكنهم رؤية المنشور
  Future<void> _calculateAudienceCount() async {
    setState(() {
      _isLoadingCount = true;
    });

    try {
      final authProvider = context.read<AuthProvider>();
      final currentUser = authProvider.userModel;

      if (currentUser == null) {
        setState(() {
          _audienceCount = 0;
          _isLoadingCount = false;
        });
        return;
      }

      int count = 0;

      switch (_selectedAudience) {
        case PostAudience.everyone:
          // جميع المستخدمين المسجلين
          count = await _getTotalUsersCount();
          break;

        case PostAudience.followersOnly:
          // المتابعين فقط
          count = currentUser.followersCount;
          break;

        case PostAudience.mutualFollowers:
          // المتابعين المتبادلين
          count = await _getMutualFollowersCount(currentUser);
          break;
      }

      setState(() {
        _audienceCount = count;
        _isLoadingCount = false;
      });
    } catch (e) {
      setState(() {
        _audienceCount = 0;
        _isLoadingCount = false;
      });
    }
  }

  // الحصول على العدد الإجمالي للمستخدمين
  Future<int> _getTotalUsersCount() async {
    try {
      final authProvider = context.read<AuthProvider>();
      final users = await authProvider.searchUsers(
        query: '',
        university: null,
        limit: 1000, // حد أعلى للحصول على عدد تقريبي
      );
      return users.length;
    } catch (e) {
      return 0;
    }
  }

  // الحصول على عدد المتابعين المتبادلين
  Future<int> _getMutualFollowersCount(dynamic currentUser) async {
    try {
      int mutualCount = 0;

      // التحقق من كل متابع إذا كان المستخدم يتابعه أيضاً
      for (String followerId in currentUser.followers) {
        if (currentUser.following.contains(followerId)) {
          mutualCount++;
        }
      }

      return mutualCount;
    } catch (e) {
      return 0;
    }
  }

  // الحصول على نص عداد الجمهور
  String _getAudienceCountText() {
    if (_audienceCount == 0) {
      return 'لا يوجد مستخدمون يمكنهم رؤية هذا المنشور';
    } else if (_audienceCount == 1) {
      return 'مستخدم واحد يمكنه رؤية هذا المنشور';
    } else if (_audienceCount == 2) {
      return 'مستخدمان يمكنهما رؤية هذا المنشور';
    } else if (_audienceCount <= 10) {
      return '$_audienceCount مستخدمين يمكنهم رؤية هذا المنشور';
    } else if (_audienceCount <= 100) {
      return '$_audienceCount مستخدماً يمكنهم رؤية هذا المنشور';
    } else {
      return '$_audienceCount مستخدماً يمكنهم رؤية هذا المنشور';
    }
  }

  Future<void> _sharePost() async {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final mindMapProvider = context.read<MindMapProvider>();

    final user = authProvider.userModel;
    if (user == null) {
      _showErrorSnackBar('يجب تسجيل الدخول أولاً');
      return;
    }

    // التحقق من تقييد النشر
    final restriction = await RestrictionService.checkPostingRestriction(user.uid);
    if (restriction != null && mounted) {
      await RestrictionDialog.show(context, restriction, 'النشر');
      return;
    }

    setState(() {
      _isSharing = true;
    });

    try {
      final post = await postsProvider.createPost(
        mindMap: widget.mindMap,
        author: user,
        description: _descriptionController.text.trim(),
        audience: _selectedAudience,
      );

      // تحديث حالة النشر في المخطط
      widget.mindMap.markAsPublished(post.id);

      // حفظ التحديث في قاعدة البيانات
      await mindMapProvider.updateMindMap(widget.mindMap);

      if (mounted) {
        Navigator.of(context).pop();
        _showSuccessSnackBar('تم مشاركة المخطط بنجاح!');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في مشاركة المخطط: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSharing = false;
        });
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _showAudienceSelector() async {
    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.userModel;
    final mutualCount = await _getMutualFollowersCount(currentUser);

    if (!mounted) return;

    final selectedAudience = await showPostAudienceSelector(
      context: context,
      initialAudience: _selectedAudience,
      getTotalUsersCount: _getTotalUsersCount,
      followersCount: currentUser?.followersCount ?? 0,
      mutualFollowersCount: mutualCount,
    );

    if (selectedAudience != null && mounted) {
      setState(() {
        _selectedAudience = selectedAudience;
      });
      // إعادة حساب عدد الجمهور عند تغيير النوع
      _calculateAudienceCount();
    }
  }

  // حساب عرض الحوار حسب حجم الشاشة
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) {
      return 600; // شاشات كبيرة
    } else if (screenWidth > 768) {
      return screenWidth * 0.7; // أجهزة لوحية
    } else {
      return screenWidth * 0.9; // هواتف
    }
  }

  // حساب الحد الأقصى لعرض الحوار
  double _getDialogMaxWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.95;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: _getDialogWidth(context),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: _getDialogMaxWidth(context),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.blue.shade700,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.share,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'مشاركة المخطط كمنشور',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [

            // معلومات المخطط
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.account_tree,
                        color: Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          widget.mindMap.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  
                  if (widget.mindMap.subject.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.subject,
                          color: Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.mindMap.subject,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                  
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.analytics,
                        color: Colors.green,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${widget.mindMap.nodeCount} عقدة',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // اختيار جمهور المنشور
            const Text(
              'جمهور المنشور',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: _showAudienceSelector,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _selectedAudience.color,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _selectedAudience.icon,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        _selectedAudience.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey.shade600,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // وصف المنشور
            const Text(
              'وصف المنشور (اختياري)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              maxLines: 4,
              maxLength: 500,
              decoration: InputDecoration(
                hintText: 'اكتب وصفاً للمخطط أو شارك أفكارك حوله...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.blue, width: 2),
                ),
              ),
            ),

            // عداد الجمهور
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.blue.shade200,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.visibility,
                    size: 18,
                    color: Colors.blue.shade700,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _isLoadingCount
                        ? Row(
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade700),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'جاري حساب عدد المشاهدين...',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            _getAudienceCountText(),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ],
              ),
            ),

                  ],
                ),
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isSharing ? null : () => Navigator.of(context).pop(),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _isSharing ? null : _sharePost,
                    icon: _isSharing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.share),
                    label: Text(_isSharing ? 'جاري المشاركة...' : 'مشاركة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade700,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
