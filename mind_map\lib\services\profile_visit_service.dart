import 'package:firebase_database/firebase_database.dart';

/// خدمة تسجيل زيارات الملف الشخصي
class ProfileVisitService {
  static final DatabaseReference _database = FirebaseDatabase.instance.ref();

  /// تسجيل زيارة ملف شخصي
  /// [profileOwnerId] معرف صاحب الملف الشخصي
  /// [visitorId] معرف الزائر
  static Future<void> recordProfileVisit({
    required String profileOwnerId,
    required String visitorId,
  }) async {
    try {
      // لا نسجل الزيارة إذا كان المستخدم يزور ملفه الشخصي
      if (profileOwnerId == visitorId) {
        return;
      }

      // الحصول على البيانات الحالية للمستخدم
      final userRef = _database.child('users').child(profileOwnerId);
      final snapshot = await userRef.get();

      if (snapshot.exists) {
        final userData = Map<String, dynamic>.from(snapshot.value as Map);
        
        // الحصول على قائمة الزوار الحالية
        List<String> currentVisitors = [];
        if (userData['profileVisitors'] != null) {
          if (userData['profileVisitors'] is List) {
            currentVisitors = List<String>.from(userData['profileVisitors']);
          } else if (userData['profileVisitors'] is Map) {
            currentVisitors = (userData['profileVisitors'] as Map).values
                .map((e) => e.toString())
                .toList();
          }
        }

        // إضافة الزائر إذا لم يكن موجوداً
        if (!currentVisitors.contains(visitorId)) {
          currentVisitors.add(visitorId);
          
          // تحديث قائمة الزوار في Firebase
          await userRef.child('profileVisitors').set(currentVisitors);
          
          print('✅ تم تسجيل زيارة الملف الشخصي: $visitorId زار $profileOwnerId');
        } else {
          print('ℹ️ الزائر $visitorId زار الملف الشخصي $profileOwnerId من قبل');
        }
      }
    } catch (e) {
      print('❌ خطأ في تسجيل زيارة الملف الشخصي: $e');
    }
  }

  /// الحصول على قائمة زوار الملف الشخصي
  /// [profileOwnerId] معرف صاحب الملف الشخصي
  static Future<List<String>> getProfileVisitors(String profileOwnerId) async {
    try {
      final userRef = _database.child('users').child(profileOwnerId);
      final snapshot = await userRef.child('profileVisitors').get();

      if (snapshot.exists) {
        if (snapshot.value is List) {
          return List<String>.from(snapshot.value as List);
        } else if (snapshot.value is Map) {
          return (snapshot.value as Map).values
              .map((e) => e.toString())
              .toList();
        }
      }
      
      return [];
    } catch (e) {
      print('❌ خطأ في جلب زوار الملف الشخصي: $e');
      return [];
    }
  }

  /// الحصول على عدد زوار الملف الشخصي
  /// [profileOwnerId] معرف صاحب الملف الشخصي
  static Future<int> getProfileVisitorsCount(String profileOwnerId) async {
    try {
      final visitors = await getProfileVisitors(profileOwnerId);
      print('🔍 عدد زوار الملف الشخصي لـ $profileOwnerId: ${visitors.length}');
      print('🔍 قائمة الزوار: $visitors');
      return visitors.length;
    } catch (e) {
      print('❌ خطأ في جلب عدد زوار الملف الشخصي: $e');
      return 0;
    }
  }

  /// مسح قائمة زوار الملف الشخصي (للمستخدم نفسه فقط)
  /// [profileOwnerId] معرف صاحب الملف الشخصي
  static Future<void> clearProfileVisitors(String profileOwnerId) async {
    try {
      final userRef = _database.child('users').child(profileOwnerId);
      await userRef.child('profileVisitors').set([]);
      
      print('✅ تم مسح قائمة زوار الملف الشخصي لـ $profileOwnerId');
    } catch (e) {
      print('❌ خطأ في مسح قائمة زوار الملف الشخصي: $e');
    }
  }

  /// إزالة زائر معين من قائمة الزوار
  /// [profileOwnerId] معرف صاحب الملف الشخصي
  /// [visitorId] معرف الزائر المراد إزالته
  static Future<void> removeProfileVisitor({
    required String profileOwnerId,
    required String visitorId,
  }) async {
    try {
      final userRef = _database.child('users').child(profileOwnerId);
      final snapshot = await userRef.get();

      if (snapshot.exists) {
        final userData = Map<String, dynamic>.from(snapshot.value as Map);
        
        // الحصول على قائمة الزوار الحالية
        List<String> currentVisitors = [];
        if (userData['profileVisitors'] != null) {
          if (userData['profileVisitors'] is List) {
            currentVisitors = List<String>.from(userData['profileVisitors']);
          } else if (userData['profileVisitors'] is Map) {
            currentVisitors = (userData['profileVisitors'] as Map).values
                .map((e) => e.toString())
                .toList();
          }
        }

        // إزالة الزائر من القائمة
        if (currentVisitors.contains(visitorId)) {
          currentVisitors.remove(visitorId);
          
          // تحديث قائمة الزوار في Firebase
          await userRef.child('profileVisitors').set(currentVisitors);
          
          print('✅ تم إزالة الزائر $visitorId من قائمة زوار $profileOwnerId');
        }
      }
    } catch (e) {
      print('❌ خطأ في إزالة الزائر من قائمة الزوار: $e');
    }
  }
}
