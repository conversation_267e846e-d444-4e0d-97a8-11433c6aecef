import 'package:flutter/material.dart';
import '../models/admin_statistics.dart';

// ملف يحتوي على باقي أقسام شاشة الإحصائيات الإدارية
class AdminStatisticsSections {
  
  // قسم إحصائيات المخططات الذهنية
  static Widget buildMindMapStatisticsSection(AdminStatistics stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_tree_rounded,
                  color: Colors.green.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات المخططات الذهنية',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // المخططات المنشأة حديثاً
            _buildSubSection(
              'المخططات المنشأة حديثاً',
              [
                _buildInfoRow('اليوم', stats.mindMapsCreatedToday.toString()),
                _buildInfoRow('هذا الأسبوع', stats.mindMapsCreatedThisWeek.toString()),
                _buildInfoRow('هذا الشهر', stats.mindMapsCreatedThisMonth.toString()),
              ],
            ),
            const SizedBox(height: 16),
            
            // أنواع المخططات
            _buildSubSection(
              'أنواع المخططات',
              [
                _buildInfoRow('المفضلة', stats.favoriteMindMaps.toString()),
                _buildInfoRow('المنشورة', stats.publishedMindMaps.toString()),
                _buildInfoRow('من القوالب', stats.templatedMindMaps.toString()),
              ],
            ),
            
            // توزيع حسب المادة
            if (stats.mindMapsBySubject.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildDistributionSection('توزيع المخططات حسب المادة', stats.mindMapsBySubject),
            ],
          ],
        ),
      ),
    );
  }

  // قسم إحصائيات المنشورات
  static Widget buildPostStatisticsSection(AdminStatistics stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.post_add_rounded,
                  color: Colors.orange.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات المنشورات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // المنشورات المنشأة حديثاً
            _buildSubSection(
              'المنشورات المنشأة حديثاً',
              [
                _buildInfoRow('اليوم', stats.postsCreatedToday.toString()),
                _buildInfoRow('هذا الأسبوع', stats.postsCreatedThisWeek.toString()),
                _buildInfoRow('هذا الشهر', stats.postsCreatedThisMonth.toString()),
              ],
            ),
            const SizedBox(height: 16),
            
            // التفاعل مع المنشورات
            _buildSubSection(
              'التفاعل مع المنشورات',
              [
                _buildInfoRow('إجمالي التعليقات', stats.totalComments.toString()),
                _buildInfoRow('إجمالي ردود الفعل', stats.totalReactions.toString()),
                _buildInfoRow('متوسط التعليقات لكل منشور', 
                  stats.totalPosts > 0 
                    ? (stats.totalComments / stats.totalPosts).toStringAsFixed(1)
                    : '0'),
                _buildInfoRow('متوسط ردود الفعل لكل منشور', 
                  stats.totalPosts > 0 
                    ? (stats.totalReactions / stats.totalPosts).toStringAsFixed(1)
                    : '0'),
              ],
            ),
            
            // توزيع حسب المادة
            if (stats.postsBySubject.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildDistributionSection('توزيع المنشورات حسب المادة', stats.postsBySubject),
            ],
          ],
        ),
      ),
    );
  }

  // قسم إحصائيات طلبات التعديل
  static Widget buildEditRequestStatisticsSection(AdminStatistics stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.edit_note_rounded,
                  color: Colors.purple.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات طلبات التعديل',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // طلبات التعديل حسب الحالة
            _buildSubSection(
              'طلبات التعديل حسب الحالة',
              [
                _buildInfoRow('قيد الانتظار', stats.pendingEditRequests.toString()),
                _buildInfoRow('مقبولة', stats.approvedEditRequests.toString()),
                _buildInfoRow('مرفوضة', stats.rejectedEditRequests.toString()),
              ],
            ),
            const SizedBox(height: 16),
            
            // طلبات التعديل الحديثة
            _buildSubSection(
              'طلبات التعديل الحديثة',
              [
                _buildInfoRow('اليوم', stats.editRequestsToday.toString()),
                _buildInfoRow('هذا الأسبوع', stats.editRequestsThisWeek.toString()),
              ],
            ),
            const SizedBox(height: 16),
            
            // معدلات الموافقة
            _buildSubSection(
              'معدلات الموافقة',
              [
                _buildInfoRow('معدل الموافقة', 
                  stats.totalEditRequests > 0 
                    ? '${((stats.approvedEditRequests / stats.totalEditRequests) * 100).toStringAsFixed(1)}%'
                    : '0%'),
                _buildInfoRow('معدل الرفض', 
                  stats.totalEditRequests > 0 
                    ? '${((stats.rejectedEditRequests / stats.totalEditRequests) * 100).toStringAsFixed(1)}%'
                    : '0%'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // قسم إحصائيات القوالب
  static Widget buildTemplateStatisticsSection(AdminStatistics stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.library_books_rounded,
                  color: Colors.teal.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات القوالب',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // أنواع القوالب
            _buildSubSection(
              'أنواع القوالب',
              [
                _buildInfoRow('القوالب المحددة مسبقاً', stats.predefinedTemplates.toString()),
                _buildInfoRow('القوالب المخصصة', stats.customTemplates.toString()),
                _buildInfoRow('إجمالي القوالب', stats.totalTemplates.toString()),
              ],
            ),
            
            // توزيع حسب الفئة
            if (stats.templatesByCategory.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildDistributionSection('توزيع القوالب حسب الفئة', stats.templatesByCategory),
            ],
          ],
        ),
      ),
    );
  }

  // قسم فرعي
  static Widget _buildSubSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  // صف معلومات
  static Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // قسم التوزيع
  static Widget _buildDistributionSection(String title, Map<String, int> data) {
    final sortedEntries = data.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            children: sortedEntries.take(5).map((entry) {
              return _buildInfoRow(entry.key, entry.value.toString());
            }).toList(),
          ),
        ),
      ],
    );
  }
}
