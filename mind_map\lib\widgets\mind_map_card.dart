import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/mind_map.dart';
import '../providers/mind_map_provider.dart';
import 'mind_map_preview_painter.dart';
import 'share_mind_map_dialog.dart';

class MindMapCard extends StatelessWidget {
  final MindMap mindMap;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const MindMapCard({
    super.key,
    required this.mindMap,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: SizedB<PERSON>(
          height: 280, // تحديد ارتفاع ثابت للبطاقة
          child: Stack(
          children: [
            Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // معاينة مصغرة للمخطط
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // رسم تخطيطي مبسط للعقد
                  Positioned.fill(
                    child: CustomPaint(
                      painter: MindMapPreviewPainter(mindMap: mindMap),
                    ),
                  ),
                  // عدد العقد في الزاوية
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${mindMap.nodeCount}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // مؤشرات التعليقات والمفضلة في الزاوية اليسرى
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // مؤشر التعليقات
                        if (mindMap.hasComments)
                          Container(
                            margin: const EdgeInsets.only(left: 4),
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 3,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.comment,
                                  size: 10,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${mindMap.commentCount}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        // مؤشر المفضلة
                        if (mindMap.isFavorite)
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 3,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.favorite,
                              size: 10,
                              color: Colors.white,
                            ),
                          ),
                      ],
                    ),
                  ),


                ],
              ),
            ),

            // محتوى البطاقة
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                  // Header with title and menu
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          mindMap.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      PopupMenuButton<String>(
                        onSelected: (value) {
                          switch (value) {
                            case 'edit':
                              onEdit();
                              break;
                            case 'share':
                              if (mindMap.canBePublished) {
                                _showShareDialog(context);
                              }
                              break;
                            case 'favorite':
                              context.read<MindMapProvider>().toggleMindMapFavorite(mindMap.id);
                              break;
                            case 'delete':
                              onDelete();
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: ListTile(
                              leading: Icon(Icons.edit),
                              title: Text('تحرير'),
                            ),
                          ),
                          PopupMenuItem(
                            value: 'share',
                            enabled: mindMap.canBePublished,
                            child: ListTile(
                              leading: Icon(
                                Icons.share,
                                color: mindMap.canBePublished ? Colors.blue : Colors.grey,
                              ),
                              title: Text(
                                _getShareButtonText(mindMap),
                                style: TextStyle(
                                  color: mindMap.canBePublished ? Colors.black : Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          PopupMenuItem(
                            value: 'favorite',
                            child: ListTile(
                              leading: Icon(
                                mindMap.isFavorite ? Icons.favorite : Icons.favorite_border,
                                color: mindMap.isFavorite ? Colors.red : null,
                              ),
                              title: Text(mindMap.isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'),
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: ListTile(
                              leading: Icon(Icons.delete),
                              title: Text('حذف'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 6),

                  // Subject badge with comments count and publication status
                  Row(
                    children: [
                      // Subject badge
                      if (mindMap.subject.isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            mindMap.subject,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),
                      ],

                      // Publication status badge
                      if (mindMap.isPublished || (mindMap.isFromTemplate && !mindMap.hasBeenModifiedFromTemplate)) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(mindMap).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: _getStatusColor(mindMap).withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getStatusIcon(mindMap),
                                size: 12,
                                color: _getStatusColor(mindMap),
                              ),
                              const SizedBox(width: 2),
                              Text(
                                _getStatusText(mindMap),
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                  color: _getStatusColor(mindMap),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 6),
                      ],
                    ],
                  ),

                  const SizedBox(height: 6),

                  // Comments count badge
                  if (mindMap.hasComments)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: Colors.orange.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.comment,
                            size: 12,
                            color: Colors.orange.shade700,
                          ),
                          const SizedBox(width: 3),
                          Text(
                            '${mindMap.commentCount}',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Description
                  if (mindMap.description.isNotEmpty) ...[
                    const SizedBox(height: 6),
                    Text(
                      mindMap.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],

                  const SizedBox(height: 8),

                  // Footer with stats and date
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.account_tree,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${mindMap.nodeCount} عقدة',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(mindMap.updatedAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Tags
                  if (mindMap.tags.isNotEmpty) ...[
                    const SizedBox(height: 6),
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: mindMap.tags.take(3).map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            tag,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.secondary,
                              fontSize: 10,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),
            ),
),
),
],
),
            // أيقونة المفضلة التفاعلية في الزاوية اليسرى السفلى
            Positioned(
              bottom: 8,
              left: 8,
              child: GestureDetector(
                onTap: () {
                  context.read<MindMapProvider>().toggleMindMapFavorite(mindMap.id);
                },
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: mindMap.isFavorite
                        ? Colors.red.withValues(alpha: 0.9)
                        : Colors.grey.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Icon(
                    mindMap.isFavorite ? Icons.favorite : Icons.favorite_border,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  // عرض حوار المشاركة
  void _showShareDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ShareMindMapDialog(mindMap: mindMap),
    );
  }

  // الحصول على لون حالة المخطط
  Color _getStatusColor(MindMap mindMap) {
    if (mindMap.isFromTemplate && !mindMap.hasBeenModifiedFromTemplate) {
      return Colors.purple; // قالب غير معدل
    }
    if (mindMap.hasModificationsAfterPublish) {
      return Colors.orange; // معدل بعد النشر
    }
    return Colors.green; // منشور
  }

  // الحصول على أيقونة حالة المخطط
  IconData _getStatusIcon(MindMap mindMap) {
    if (mindMap.isFromTemplate && !mindMap.hasBeenModifiedFromTemplate) {
      return Icons.content_copy; // قالب غير معدل
    }
    if (mindMap.hasModificationsAfterPublish) {
      return Icons.edit; // معدل بعد النشر
    }
    return Icons.public; // منشور
  }

  // الحصول على نص حالة المخطط
  String _getStatusText(MindMap mindMap) {
    if (mindMap.isFromTemplate && !mindMap.hasBeenModifiedFromTemplate) {
      return 'قالب'; // قالب غير معدل
    }
    if (mindMap.hasModificationsAfterPublish) {
      return 'معدل'; // معدل بعد النشر
    }
    return 'منشور'; // منشور
  }

  // الحصول على نص زر المشاركة
  String _getShareButtonText(MindMap mindMap) {
    if (mindMap.isFromTemplate && !mindMap.hasBeenModifiedFromTemplate) {
      return 'يجب تعديل القالب أولاً';
    }
    if (mindMap.isPublished && mindMap.lastModifiedAfterPublish == null) {
      return 'تم النشر مسبقاً';
    }
    return 'مشاركة كمنشور';
  }
}
