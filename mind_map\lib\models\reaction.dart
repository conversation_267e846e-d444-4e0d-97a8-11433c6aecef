enum ReactionType {
  like('👍', 'إعجاب'),
  love('❤️', 'حب'),
  support('💪', 'دعم'),
  excellent('🌟', 'ممتاز'),
  good('👌', 'جيد'),
  veryGood('🔥', 'جيد جداً');

  const ReactionType(this.emoji, this.label);
  
  final String emoji;
  final String label;
}

class Reaction {
  final String userId;
  final ReactionType type;
  final DateTime createdAt;

  Reaction({
    required this.userId,
    required this.type,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'type': type.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Reaction.fromJson(Map<String, dynamic> json) {
    return Reaction(
      userId: json['userId'] ?? '',
      type: ReactionType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => ReactionType.like,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
    );
  }
}

class ReactionSummary {
  final Map<ReactionType, int> counts;
  final Map<ReactionType, List<String>> userIds;

  ReactionSummary({
    Map<ReactionType, int>? counts,
    Map<ReactionType, List<String>>? userIds,
  }) : counts = counts ?? {},
       userIds = userIds ?? {};

  int get totalCount => counts.values.fold(0, (sum, count) => sum + count);

  bool hasUserReacted(String userId) {
    return userIds.values.any((users) => users.contains(userId));
  }

  ReactionType? getUserReaction(String userId) {
    for (final entry in userIds.entries) {
      if (entry.value.contains(userId)) {
        return entry.key;
      }
    }
    return null;
  }

  ReactionSummary addReaction(String userId, ReactionType type) {
    final newCounts = Map<ReactionType, int>.from(counts);
    final newUserIds = Map<ReactionType, List<String>>.from(userIds);

    // إزالة أي رد فعل سابق للمستخدم
    for (final reactionType in ReactionType.values) {
      newUserIds[reactionType]?.remove(userId);
      if (newUserIds[reactionType]?.isEmpty == true) {
        newCounts.remove(reactionType);
        newUserIds.remove(reactionType);
      } else if (newUserIds[reactionType] != null) {
        newCounts[reactionType] = newUserIds[reactionType]!.length;
      }
    }

    // إضافة رد الفعل الجديد
    newUserIds[type] = (newUserIds[type] ?? [])..add(userId);
    newCounts[type] = newUserIds[type]!.length;

    return ReactionSummary(counts: newCounts, userIds: newUserIds);
  }

  ReactionSummary removeReaction(String userId) {
    final newCounts = Map<ReactionType, int>.from(counts);
    final newUserIds = Map<ReactionType, List<String>>.from(userIds);

    for (final reactionType in ReactionType.values) {
      newUserIds[reactionType]?.remove(userId);
      if (newUserIds[reactionType]?.isEmpty == true) {
        newCounts.remove(reactionType);
        newUserIds.remove(reactionType);
      } else if (newUserIds[reactionType] != null) {
        newCounts[reactionType] = newUserIds[reactionType]!.length;
      }
    }

    return ReactionSummary(counts: newCounts, userIds: newUserIds);
  }

  Map<String, dynamic> toJson() {
    return {
      'counts': counts.map((key, value) => MapEntry(key.name, value)),
      'userIds': userIds.map((key, value) => MapEntry(key.name, value)),
    };
  }

  factory ReactionSummary.fromJson(Map<String, dynamic> json) {
    final countsJson = json['counts'] as Map<String, dynamic>? ?? {};
    final userIdsJson = json['userIds'] as Map<String, dynamic>? ?? {};

    final counts = <ReactionType, int>{};
    final userIds = <ReactionType, List<String>>{};

    for (final type in ReactionType.values) {
      if (countsJson.containsKey(type.name)) {
        counts[type] = countsJson[type.name] ?? 0;
      }
      if (userIdsJson.containsKey(type.name)) {
        userIds[type] = List<String>.from(userIdsJson[type.name] ?? []);
      }
    }

    return ReactionSummary(counts: counts, userIds: userIds);
  }

  static ReactionSummary fromReactions(List<Reaction> reactions) {
    final counts = <ReactionType, int>{};
    final userIds = <ReactionType, List<String>>{};

    for (final reaction in reactions) {
      counts[reaction.type] = (counts[reaction.type] ?? 0) + 1;
      userIds[reaction.type] = (userIds[reaction.type] ?? [])..add(reaction.userId);
    }

    return ReactionSummary(counts: counts, userIds: userIds);
  }
}
