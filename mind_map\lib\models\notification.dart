import 'package:uuid/uuid.dart';
import 'reaction.dart';

// أنواع الإشعارات
enum NotificationType {
  follow,        // متابعة جديدة
  newPost,       // منشور جديد من متابع
  like,          // إعجاب بمنشور
  comment,       // تعليق على منشور
  reply,         // رد على تعليق
  reaction,      // رد فعل على منشور أو تعليق
  reportResolved, // تم حل البلاغ
  restriction,   // تقييد من الإدارة
  unrestriction, // إلغاء تقييد
  warning,       // تحذير من الإدارة
  adminMessage,  // رسالة من الإدارة
}

// نموذج الإشعار
class AppNotification {
  final String id;
  final String userId; // المستخدم الذي سيستقبل الإشعار
  final NotificationType type;
  final String title;
  final String message;
  final String? fromUserId; // المستخدم الذي تسبب في الإشعار
  final String? fromUserName;
  final String? fromUserAvatar;
  final String? relatedPostId; // المنشور المرتبط (إن وجد)
  final String? relatedCommentId; // التعليق المرتبط (إن وجد)
  final Map<String, dynamic>? additionalData; // بيانات إضافية
  final DateTime createdAt;
  final bool isRead;

  AppNotification({
    String? id,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    this.fromUserId,
    this.fromUserName,
    this.fromUserAvatar,
    this.relatedPostId,
    this.relatedCommentId,
    this.additionalData,
    DateTime? createdAt,
    this.isRead = false,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  // إنشاء نسخة محدثة
  AppNotification copyWith({
    String? userId,
    NotificationType? type,
    String? title,
    String? message,
    String? fromUserId,
    String? fromUserName,
    String? fromUserAvatar,
    String? relatedPostId,
    String? relatedCommentId,
    Map<String, dynamic>? additionalData,
    DateTime? createdAt,
    bool? isRead,
  }) {
    return AppNotification(
      id: id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      fromUserId: fromUserId ?? this.fromUserId,
      fromUserName: fromUserName ?? this.fromUserName,
      fromUserAvatar: fromUserAvatar ?? this.fromUserAvatar,
      relatedPostId: relatedPostId ?? this.relatedPostId,
      relatedCommentId: relatedCommentId ?? this.relatedCommentId,
      additionalData: additionalData ?? this.additionalData,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.name,
      'title': title,
      'message': message,
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'fromUserAvatar': fromUserAvatar,
      'relatedPostId': relatedPostId,
      'relatedCommentId': relatedCommentId,
      'additionalData': additionalData,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
    };
  }

  // إنشاء من JSON
  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'],
      userId: json['userId'],
      type: NotificationType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => NotificationType.follow,
      ),
      title: json['title'],
      message: json['message'],
      fromUserId: json['fromUserId'],
      fromUserName: json['fromUserName'],
      fromUserAvatar: json['fromUserAvatar'],
      relatedPostId: json['relatedPostId'],
      relatedCommentId: json['relatedCommentId'],
      additionalData: json['additionalData'] != null
          ? Map<String, dynamic>.from(json['additionalData'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      isRead: json['isRead'] ?? false,
    );
  }

  // الحصول على أيقونة الإشعار
  String get iconName {
    switch (type) {
      case NotificationType.follow:
        return 'person_add';
      case NotificationType.newPost:
        return 'post_add';
      case NotificationType.like:
        return 'favorite';
      case NotificationType.comment:
        return 'comment';
      case NotificationType.reply:
        return 'reply';
      case NotificationType.reaction:
        return 'emoji_emotions';
      case NotificationType.reportResolved:
        return 'check_circle';
      case NotificationType.restriction:
        return 'block';
      case NotificationType.unrestriction:
        return 'check_circle';
      case NotificationType.warning:
        return 'warning';
      case NotificationType.adminMessage:
        return 'admin_panel_settings';
    }
  }

  // الحصول على لون الإشعار
  String get colorHex {
    switch (type) {
      case NotificationType.follow:
        return '#2196F3'; // أزرق
      case NotificationType.newPost:
        return '#4CAF50'; // أخضر
      case NotificationType.like:
        return '#F44336'; // أحمر
      case NotificationType.comment:
        return '#FF9800'; // برتقالي
      case NotificationType.reply:
        return '#9C27B0'; // بنفسجي
      case NotificationType.reaction:
        return '#FFEB3B'; // أصفر
      case NotificationType.reportResolved:
        return '#4CAF50'; // أخضر
      case NotificationType.restriction:
        return '#FFC107'; // أصفر
      case NotificationType.unrestriction:
        return '#4CAF50'; // أخضر
      case NotificationType.warning:
        return '#FFC107'; // أصفر
      case NotificationType.adminMessage:
        return '#9C27B0'; // بنفسجي
    }
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, type: $type, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// دوال مساعدة لإنشاء إشعارات مختلفة
class NotificationFactory {
  static AppNotification createFollowNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    String? fromUserAvatar,
  }) {
    return AppNotification(
      userId: userId,
      type: NotificationType.follow,
      title: 'متابع جديد',
      message: '$fromUserName بدأ بمتابعتك',
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
    );
  }

  static AppNotification createNewPostNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    required String postTitle,
    String? fromUserAvatar,
  }) {
    return AppNotification(
      userId: userId,
      type: NotificationType.newPost,
      title: 'منشور جديد',
      message: '$fromUserName نشر مخططاً جديداً: $postTitle',
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      relatedPostId: postId,
    );
  }

  static AppNotification createLikeNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    required String postTitle,
    String? fromUserAvatar,
  }) {
    return AppNotification(
      userId: userId,
      type: NotificationType.like,
      title: 'إعجاب جديد',
      message: '$fromUserName أعجب بمنشورك: $postTitle',
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      relatedPostId: postId,
    );
  }

  static AppNotification createCommentNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    required String postTitle,
    required String commentId,
    String? fromUserAvatar,
  }) {
    return AppNotification(
      userId: userId,
      type: NotificationType.comment,
      title: 'تعليق جديد',
      message: '$fromUserName علق على منشورك: $postTitle',
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      relatedPostId: postId,
      relatedCommentId: commentId,
    );
  }

  static AppNotification createReplyNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    required String commentId,
    String? fromUserAvatar,
  }) {
    return AppNotification(
      userId: userId,
      type: NotificationType.reply,
      title: 'رد جديد',
      message: '$fromUserName رد على تعليقك',
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      relatedPostId: postId,
      relatedCommentId: commentId,
    );
  }

  static AppNotification createReactionNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    required String postTitle,
    required ReactionType reactionType,
    String? fromUserAvatar,
  }) {
    return AppNotification(
      userId: userId,
      type: NotificationType.reaction,
      title: 'تفاعل جديد ${reactionType.emoji}',
      message: '$fromUserName تفاعل ${reactionType.emoji} مع منشورك: $postTitle',
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      relatedPostId: postId,
    );
  }
}
