// اختبارات تطبيق المخطط الذهني
//
// هذا ملف اختبار أساسي لتطبيق المخطط الذهني للطلاب الجامعيين

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:mind_map/main.dart';
import 'package:mind_map/providers/mind_map_provider.dart';

void main() {
  testWidgets('Mind Map App smoke test', (WidgetTester tester) async {
    // بناء التطبيق وتشغيل إطار
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (context) => MindMapProvider(),
        child: const MindMapApp(),
      ),
    );

    // التحقق من وجود العناصر الأساسية في الشاشة الرئيسية
    expect(find.text('المخططات الذهنية'), findsOneWidget);
    expect(find.text('المواد الدراسية'), findsOneWidget);

    // التحقق من وجود أزرار الإضافة
    expect(find.byIcon(Icons.add), findsWidgets);

    // التحقق من وجود شريط البحث
    expect(find.byIcon(Icons.search), findsOneWidget);
  });

  testWidgets('Navigation between tabs test', (WidgetTester tester) async {
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (context) => MindMapProvider(),
        child: const MindMapApp(),
      ),
    );

    // النقر على تبويب المواد الدراسية
    await tester.tap(find.text('المواد الدراسية'));
    await tester.pump();

    // التحقق من التنقل الصحيح
    expect(find.text('المواد الدراسية'), findsOneWidget);
  });
}
