import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../providers/mind_map_provider.dart';
import '../models/subject.dart';

class CreateSubjectDialog extends StatefulWidget {
  const CreateSubjectDialog({super.key});

  @override
  State<CreateSubjectDialog> createState() => _CreateSubjectDialogState();
}

class _CreateSubjectDialogState extends State<CreateSubjectDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _professorController = TextEditingController();
  Color _selectedColor = const Color(0xFF2196F3);
  int _semester = 1;
  double _creditHours = 3.0;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _professorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة مادة دراسية جديدة'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Name field
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المادة *',
                    hintText: 'مثال: الرياضيات المتقدمة',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم المادة';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Description field
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'وصف مختصر للمادة',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                
                const SizedBox(height: 16),
                
                // Professor field
                TextFormField(
                  controller: _professorController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الأستاذ (اختياري)',
                    hintText: 'د. أحمد محمد',
                    border: OutlineInputBorder(),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Semester and credit hours row
                Row(
                  children: [
                    // Semester
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        value: _semester,
                        decoration: const InputDecoration(
                          labelText: 'الفصل الدراسي',
                          border: OutlineInputBorder(),
                        ),
                        items: List.generate(8, (index) {
                          return DropdownMenuItem<int>(
                            value: index + 1,
                            child: Text('الفصل ${index + 1}'),
                          );
                        }),
                        onChanged: (value) {
                          setState(() {
                            _semester = value!;
                          });
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Credit hours
                    Expanded(
                      child: DropdownButtonFormField<double>(
                        value: _creditHours,
                        decoration: const InputDecoration(
                          labelText: 'الساعات المعتمدة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem<double>(
                            value: 1.0,
                            child: Text('ساعة واحدة'),
                          ),
                          DropdownMenuItem<double>(
                            value: 1.5,
                            child: Text('ساعة ونصف'),
                          ),
                          DropdownMenuItem<double>(
                            value: 2.0,
                            child: Text('ساعتان'),
                          ),
                          DropdownMenuItem<double>(
                            value: 2.5,
                            child: Text('ساعتان ونصف'),
                          ),
                          DropdownMenuItem<double>(
                            value: 3.0,
                            child: Text('3 ساعات'),
                          ),
                          DropdownMenuItem<double>(
                            value: 3.5,
                            child: Text('3 ساعات ونصف'),
                          ),
                          DropdownMenuItem<double>(
                            value: 4.0,
                            child: Text('4 ساعات'),
                          ),
                          DropdownMenuItem<double>(
                            value: 4.5,
                            child: Text('4 ساعات ونصف'),
                          ),
                          DropdownMenuItem<double>(
                            value: 5.0,
                            child: Text('5 ساعات'),
                          ),
                          DropdownMenuItem<double>(
                            value: 5.5,
                            child: Text('5 ساعات ونصف'),
                          ),
                          DropdownMenuItem<double>(
                            value: 6.0,
                            child: Text('6 ساعات'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _creditHours = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Color picker
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'لون المادة',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    
                    // Color preview and picker button
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: _selectedColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: _showColorPicker,
                          child: const Text('اختيار اللون'),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Predefined colors
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: SubjectColors.defaultColors.map((color) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedColor = color;
                            });
                          },
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: _selectedColor == color
                                  ? Border.all(
                                      color: Theme.of(context).colorScheme.primary,
                                      width: 3,
                                    )
                                  : null,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _createSubject,
          child: const Text('إضافة'),
        ),
      ],
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار اللون'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _selectedColor,
            onColorChanged: (color) {
              setState(() {
                _selectedColor = color;
              });
            },
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  void _createSubject() {
    if (_formKey.currentState!.validate()) {
      final provider = context.read<MindMapProvider>();
      
      final subject = Subject(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        professor: _professorController.text.trim().isEmpty 
            ? null 
            : _professorController.text.trim(),
        color: _selectedColor,
        semester: _semester,
        creditHours: _creditHours,
      );
      
      provider.addSubject(subject);
      
      Navigator.pop(context);
      
      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة المادة "${subject.name}" بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
