import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../providers/auth_provider.dart';
import '../models/mind_map_template.dart';
import '../models/mind_map.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';
import '../screens/template_selection_screen.dart';

class CreateMindMapDialog extends StatefulWidget {
  const CreateMindMapDialog({super.key});

  @override
  State<CreateMindMapDialog> createState() => _CreateMindMapDialogState();
}

class _CreateMindMapDialogState extends State<CreateMindMapDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  String? _selectedSubject;
  final List<String> _tags = [];
  final _tagController = TextEditingController();
  MindMapTemplate? _selectedTemplate;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, provider, child) {
        return AlertDialog(
          title: const Text('إنشاء مخطط ذهني جديد'),
          content: SizedBox(
            width: double.maxFinite,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title field
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'عنوان المخطط *',
                        hintText: 'أدخل عنوان المخطط الذهني',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال عنوان المخطط';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),

                    // Template selection section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.dashboard_customize, color: Colors.grey[600]),
                              const SizedBox(width: 8),
                              Text(
                                'نوع المخطط',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildCreationOption(
                                  icon: Icons.add_circle_outline,
                                  title: 'مخطط فارغ',
                                  subtitle: 'ابدأ من الصفر',
                                  isSelected: _selectedTemplate == null,
                                  onTap: () {
                                    setState(() {
                                      _selectedTemplate = null;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildCreationOption(
                                  icon: Icons.library_books,
                                  title: 'من قالب',
                                  subtitle: _selectedTemplate?.name ?? 'اختر قالب جاهز',
                                  isSelected: _selectedTemplate != null,
                                  onTap: _selectTemplate,
                                ),
                              ),
                            ],
                          ),
                          if (_selectedTemplate != null) ...[
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: _selectedTemplate!.category.color.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: _selectedTemplate!.category.color.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    _selectedTemplate!.category.icon,
                                    color: _selectedTemplate!.category.color,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _selectedTemplate!.name,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: _selectedTemplate!.category.color,
                                          ),
                                        ),
                                        Text(
                                          _selectedTemplate!.category.displayName,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedTemplate = null;
                                      });
                                    },
                                    child: const Text('إزالة'),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Description field
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'الوصف (اختياري)',
                        hintText: 'وصف مختصر للمخطط الذهني',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16),
                    
                    // Subject dropdown
                    DropdownButtonFormField<String>(
                      value: _selectedSubject,
                      decoration: const InputDecoration(
                        labelText: 'المادة الدراسية',
                        border: OutlineInputBorder(),
                      ),
                      hint: const Text('اختر المادة الدراسية'),
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('بدون مادة'),
                        ),
                        ...provider.subjects.map((subject) {
                          return DropdownMenuItem<String>(
                            value: subject.name,
                            child: Row(
                              children: [
                                Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: subject.color,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(subject.name),
                              ],
                            ),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedSubject = value;
                        });
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Tags section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'العلامات (Tags)',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 8),
                        
                        // Add tag field
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _tagController,
                                decoration: const InputDecoration(
                                  hintText: 'أضف علامة',
                                  border: OutlineInputBorder(),
                                ),
                                onSubmitted: _addTag,
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () => _addTag(_tagController.text),
                              icon: const Icon(Icons.add),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Tags display
                        if (_tags.isNotEmpty)
                          Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children: _tags.map((tag) {
                              return Chip(
                                label: Text(tag),
                                deleteIcon: const Icon(Icons.close, size: 18),
                                onDeleted: () {
                                  setState(() {
                                    _tags.remove(tag);
                                  });
                                },
                              );
                            }).toList(),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: _createMindMap,
              child: const Text('إنشاء'),
            ),
          ],
        );
      },
    );
  }

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isNotEmpty && !_tags.contains(trimmedTag)) {
      setState(() {
        _tags.add(trimmedTag);
        _tagController.clear();
      });
    }
  }

  Widget _buildCreationOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.blue : Colors.grey[600],
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.blue : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _selectTemplate() async {
    final template = await Navigator.push<MindMapTemplate>(
      context,
      MaterialPageRoute(
        builder: (context) => const TemplateSelectionScreen(),
      ),
    );

    if (template != null) {
      setState(() {
        _selectedTemplate = template;
        // تحديث العنوان والوصف من القالب إذا كانا فارغين
        if (_titleController.text.trim().isEmpty) {
          _titleController.text = template.name;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _descriptionController.text = template.description;
        }
      });
    }
  }

  void _createMindMap() async {
    if (_formKey.currentState!.validate()) {
      // التحقق من تقييد إنشاء المخططات
      final authProvider = context.read<AuthProvider>();
      final user = authProvider.userModel;

      if (user != null) {
        final restriction = await RestrictionService.checkCreatingMindMapsRestriction(user.uid);
        if (restriction != null && mounted) {
          await RestrictionDialog.show(context, restriction, 'إنشاء المخططات الذهنية');
          return;
        }
      }

      if (!mounted) return;

      final provider = context.read<MindMapProvider>();

      late final MindMap mindMap;

      if (_selectedTemplate != null) {
        // إنشاء من قالب
        mindMap = _selectedTemplate!.createMindMap(
          customTitle: _titleController.text.trim(),
          customSubject: _selectedSubject ?? '',
        );
        // تحديث الوصف
        mindMap.description = _descriptionController.text.trim();
      } else {
        // إنشاء مخطط فارغ
        mindMap = provider.createNewMindMap(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          subject: _selectedSubject ?? '',
        );
      }

      // إضافة العلامات
      for (String tag in _tags) {
        mindMap.addTag(tag);
      }

      provider.addMindMap(mindMap);

      if (mounted) {
        Navigator.pop(context);

        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء المخطط الذهني "${mindMap.title}" بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
