import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/mind_map.dart';

class EditMindMapDialog extends StatefulWidget {
  final MindMap mindMap;

  const EditMindMapDialog({
    super.key,
    required this.mindMap,
  });

  @override
  State<EditMindMapDialog> createState() => _EditMindMapDialogState();
}

class _EditMindMapDialogState extends State<EditMindMapDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  String? _selectedSubject;
  late final List<String> _tags;
  final _tagController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.mindMap.title);
    _descriptionController = TextEditingController(text: widget.mindMap.description);
    _selectedSubject = widget.mindMap.subject.isEmpty ? null : widget.mindMap.subject;
    _tags = List.from(widget.mindMap.tags);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  // حساب عرض الحوار حسب حجم الشاشة
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) {
      return 600; // شاشات كبيرة
    } else if (screenWidth > 768) {
      return screenWidth * 0.7; // أجهزة لوحية
    } else {
      return screenWidth * 0.9; // هواتف
    }
  }

  // حساب الحد الأقصى لعرض الحوار
  double _getDialogMaxWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.95;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, provider, child) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: _getDialogWidth(context),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: _getDialogMaxWidth(context),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade700,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.edit,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'تحرير معلومات المخطط',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                    // Title field
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'عنوان المخطط *',
                        hintText: 'أدخل عنوان المخطط الذهني',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال عنوان المخطط';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Description field
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'الوصف (اختياري)',
                        hintText: 'أدخل وصف المخطط الذهني',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16),

                    // Subject dropdown
                    DropdownButtonFormField<String>(
                      value: _selectedSubject,
                      decoration: const InputDecoration(
                        labelText: 'المادة الدراسية (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                      hint: const Text('اختر المادة الدراسية'),
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('بدون مادة'),
                        ),
                        ...provider.subjects.map((subject) {
                          return DropdownMenuItem<String>(
                            value: subject.name,
                            child: Text(subject.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedSubject = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Tags section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'العلامات',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 8),
                        
                        // Add tag field
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _tagController,
                                decoration: const InputDecoration(
                                  hintText: 'أضف علامة جديدة',
                                  border: OutlineInputBorder(),
                                ),
                                onSubmitted: _addTag,
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () => _addTag(_tagController.text),
                              icon: const Icon(Icons.add),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Tags display
                        if (_tags.isNotEmpty)
                          Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children: _tags.map((tag) {
                              return Chip(
                                label: Text(tag),
                                deleteIcon: const Icon(Icons.close, size: 18),
                                onDeleted: () => _removeTag(tag),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                      ],
                      ),
                    
                    ),
                  ),
                ),
                // Footer with buttons
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إلغاء',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: _updateMindMap,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade700,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'حفظ التغييرات',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      ],
                  ),
                  
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isNotEmpty && !_tags.contains(trimmedTag)) {
      setState(() {
        _tags.add(trimmedTag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _updateMindMap() {
    if (_formKey.currentState!.validate()) {
      final provider = context.read<MindMapProvider>();
      
      // تحديث معلومات المخطط
      widget.mindMap.updateInfo(
        newTitle: _titleController.text.trim(),
        newDescription: _descriptionController.text.trim(),
        newSubject: _selectedSubject ?? '',
      );
      
      // تحديث العلامات
      widget.mindMap.tags.clear();
      widget.mindMap.tags.addAll(_tags);
      
      // حفظ التغييرات
      provider.updateMindMap(widget.mindMap);
      
      Navigator.pop(context);
      
      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحديث معلومات "${widget.mindMap.title}" بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
