import 'dart:math' as math;
import 'dart:math';
import 'package:firebase_database/firebase_database.dart';
import '../models/follow_suggestion_model.dart';
import '../models/user_model.dart';

class FollowSuggestionsService {
  static final DatabaseReference _database = FirebaseDatabase.instance.ref();
  static final Random _random = Random();

  /// الحصول على اقتراحات المتابعة للمستخدم
  static Future<List<FollowSuggestionModel>> getFollowSuggestions({
    required String currentUserId,
    int limit = 5,
  }) async {
    try {
      print('🔍 جلب اقتراحات المتابعة للمستخدم: $currentUserId');

      // جلب بيانات المستخدم الحالي
      final currentUserSnapshot = await _database.child('users/$currentUserId').get();
      if (!currentUserSnapshot.exists) {
        print('❌ المستخدم الحالي غير موجود');
        return [];
      }

      // تحويل بيانات المستخدم الحالي بأمان
      final currentUserData = currentUserSnapshot.value;
      print('🔍 نوع بيانات المستخدم الحالي: ${currentUserData.runtimeType}');

      if (currentUserData is! Map) {
        print('❌ بيانات المستخدم الحالي ليست من نوع Map: ${currentUserData.runtimeType}');
        return [];
      }

      final currentUser = UserModel.fromMap(
        Map<String, dynamic>.from(currentUserData)
      );

      // جلب قائمة المتابعين الحاليين
      final followingSnapshot = await _database.child('users/$currentUserId/following').get();
      final currentFollowing = <String>{};
      if (followingSnapshot.exists) {
        final followingData = followingSnapshot.value;
        print('🔍 نوع بيانات المتابعين: ${followingData.runtimeType}');

        if (followingData is Map) {
          // تحويل مفاتيح الخريطة إلى قائمة نصوص
          for (final key in followingData.keys) {
            if (key != null) {
              currentFollowing.add(key.toString());
            }
          }
          print('✅ تم جلب ${currentFollowing.length} متابع من Map');
        } else if (followingData is List) {
          // في حالة كانت البيانات على شكل قائمة
          for (int i = 0; i < followingData.length; i++) {
            if (followingData[i] != null) {
              currentFollowing.add(followingData[i].toString());
            }
          }
          print('✅ تم جلب ${currentFollowing.length} متابع من List');
        } else {
          print('⚠️ نوع بيانات المتابعين غير مدعوم: ${followingData.runtimeType}');
        }
      } else {
        print('ℹ️ لا توجد قائمة متابعين للمستخدم الحالي');
      }

      // جلب جميع المستخدمين
      final usersSnapshot = await _database.child('users').get();
      if (!usersSnapshot.exists) {
        print('❌ لا توجد مستخدمين في قاعدة البيانات');
        return [];
      }

      final allUsers = <UserModel>[];
      final usersData = usersSnapshot.value;

      // التحقق من نوع البيانات
      if (usersData is Map) {
        for (final entry in usersData.entries) {
          try {
            final userId = entry.key as String;
            final userValue = entry.value;

            // التأكد من أن البيانات من النوع الصحيح
            if (userValue is Map) {
              final userData = Map<String, dynamic>.from(userValue);

              // تجاهل المستخدم الحالي والمتابعين الحاليين
              if (userId != currentUserId && !currentFollowing.contains(userId)) {
                final user = UserModel.fromMap(userData);

                // التحقق من إعدادات الخصوصية
                if (_shouldIncludeInSuggestions(user)) {
                  allUsers.add(user);
                } else {
                  print('🔒 تم تجاهل ${user.fullName} بسبب إعدادات الخصوصية');
                }
              }
            }
          } catch (e) {
            print('❌ خطأ في معالجة بيانات المستخدم $entry.key: $e');
            continue; // تجاهل هذا المستخدم والمتابعة
          }
        }
      } else {
        print('❌ تنسيق بيانات المستخدمين غير صحيح: ${usersData.runtimeType}');
        return [];
      }

      if (allUsers.isEmpty) {
        print('❌ لا توجد مستخدمين متاحين للاقتراح');
        return [];
      }

      // إنشاء اقتراحات المتابعة
      final suggestions = <FollowSuggestionModel>[];
      
      for (final user in allUsers) {
        final suggestion = await _createSuggestion(currentUser, user);
        if (suggestion != null) {
          suggestions.add(suggestion);
        }
      }

      // ترتيب الاقتراحات حسب الخوارزمية المحسنة
      // (المتابعين + المنشورات + الانضمام الحديث + سبب الاقتراح)
      suggestions.sort((a, b) => _getSuggestionPriority(b) - _getSuggestionPriority(a));

      // إرجاع عدد محدود من الاقتراحات
      final limitedSuggestions = suggestions.take(limit).toList();
      
      print('✅ تم إنشاء ${limitedSuggestions.length} اقتراح متابعة');
      return limitedSuggestions;

    } catch (e) {
      print('❌ خطأ في جلب اقتراحات المتابعة: $e');
      return [];
    }
  }

  /// إنشاء اقتراح متابعة لمستخدم معين
  static Future<FollowSuggestionModel?> _createSuggestion(
    UserModel currentUser, 
    UserModel suggestedUser
  ) async {
    try {
      // جلب عدد المنشورات أولاً
      final postsSnapshot = await _database
          .child('posts')
          .orderByChild('authorId')
          .equalTo(suggestedUser.uid)
          .get();

      int postsCount = 0;
      if (postsSnapshot.exists) {
        final postsData = postsSnapshot.value;
        print('🔍 نوع بيانات المنشورات لـ ${suggestedUser.fullName}: ${postsData.runtimeType}');

        if (postsData is Map) {
          postsCount = postsData.length;
          print('✅ عدد المنشورات من Map: $postsCount');
        } else if (postsData is List) {
          postsCount = postsData.where((element) => element != null).length;
          print('✅ عدد المنشورات من List: $postsCount');
        } else {
          print('⚠️ نوع بيانات المنشورات غير مدعوم: ${postsData.runtimeType}');
          postsCount = 0;
        }
      } else {
        print('ℹ️ لا توجد منشورات للمستخدم ${suggestedUser.fullName}');
      }

      // تحديد سبب الاقتراح مع معلومات المنشورات
      final reason = await _determineSuggestionReason(currentUser, suggestedUser, postsCount);
      if (reason == null) return null;

      // حساب الاهتمامات المشتركة
      final commonInterests = _findCommonInterests(currentUser, suggestedUser);

      return FollowSuggestionModel(
        id: '${currentUser.uid}_${suggestedUser.uid}_${DateTime.now().millisecondsSinceEpoch}',
        userId: suggestedUser.uid,
        userName: suggestedUser.fullName,
        userEmail: suggestedUser.email,
        university: suggestedUser.university,
        major: suggestedUser.major,
        followersCount: suggestedUser.followersCount,
        postsCount: postsCount,
        profileImageUrl: null, // سيتم إضافة دعم الصور لاحقاً
        commonInterests: commonInterests,
        suggestionReason: reason.displayName,
        isFollowing: false,
        createdAt: DateTime.now(),
      );

    } catch (e) {
      print('❌ خطأ في إنشاء اقتراح المتابعة: $e');
      return null;
    }
  }

  /// تحديد سبب اقتراح المتابعة المحسن
  static Future<SuggestionReason?> _determineSuggestionReason(
    UserModel currentUser,
    UserModel suggestedUser,
    int postsCount
  ) async {
    // نفس الجامعة والتخصص (أولوية عالية)
    if (currentUser.university == suggestedUser.university &&
        currentUser.major == suggestedUser.major) {
      return SuggestionReason.sameMajor;
    }

    // نفس الجامعة (أولوية متوسطة-عالية)
    if (currentUser.university == suggestedUser.university) {
      return SuggestionReason.sameUniversity;
    }

    // مستخدم شائع جداً (أكثر من 100 متابع)
    if (suggestedUser.followersCount >= 100) {
      return SuggestionReason.popularUser;
    }

    // مستخدم منضم حديثاً (آخر 14 يوم)
    final twoWeeksAgo = DateTime.now().subtract(const Duration(days: 14));
    if (suggestedUser.createdAt.isAfter(twoWeeksAgo)) {
      return SuggestionReason.recentJoined;
    }

    // مستخدم نشط (أكثر من 10 منشورات)
    if (postsCount >= 10) {
      return SuggestionReason.popularUser;
    }

    // مستخدم شائع (أكثر من 20 متابع)
    if (suggestedUser.followersCount >= 20) {
      return SuggestionReason.popularUser;
    }

    // اقتراح عام للمستخدمين الآخرين
    return SuggestionReason.commonInterests;
  }

  /// البحث عن الاهتمامات المشتركة
  static List<String> _findCommonInterests(
    UserModel currentUser, 
    UserModel suggestedUser
  ) {
    final interests = <String>[];
    
    // إضافة الجامعة كاهتمام مشترك
    if (currentUser.university == suggestedUser.university) {
      interests.add(currentUser.university);
    }
    
    // إضافة التخصص كاهتمام مشترك
    if (currentUser.major == suggestedUser.major) {
      interests.add(currentUser.major);
    }

    return interests;
  }

  /// حساب أولوية الاقتراح المحسنة
  static int _getSuggestionPriority(FollowSuggestionModel suggestion) {
    double priority = 0.0;

    // 1. أولوية حسب سبب الاقتراح (الوزن الأساسي)
    switch (suggestion.suggestionReason) {
      case 'نفس التخصص':
        priority += 1000;
        break;
      case 'نفس الجامعة':
        priority += 800;
        break;
      case 'مستخدم شائع':
        priority += 600;
        break;
      case 'انضم حديثاً':
        priority += 400;
        break;
      default:
        priority += 200;
    }

    // 2. أولوية حسب عدد المتابعين (خوارزمية متدرجة)
    final followersScore = _calculateFollowersScore(suggestion.followersCount);
    priority += followersScore;

    // 3. أولوية حسب عدد المنشورات (النشاط)
    final postsScore = _calculatePostsScore(suggestion.postsCount);
    priority += postsScore;

    // 4. أولوية حسب تاريخ الانضمام (المستخدمين الجدد)
    final recencyScore = _calculateRecencyScore(suggestion.createdAt);
    priority += recencyScore;

    // 5. أولوية إضافية حسب عدد الاهتمامات المشتركة
    priority += suggestion.commonInterests.length * 50;

    // طباعة تفاصيل الحساب للتأكد من عمل الخوارزمية
    print('🔍 حساب أولوية ${suggestion.userName}:');
    print('   - سبب الاقتراح: ${suggestion.suggestionReason}');
    print('   - المتابعين: ${suggestion.followersCount} (نقاط: $followersScore)');
    print('   - المنشورات: ${suggestion.postsCount} (نقاط: $postsScore)');
    print('   - الانضمام: ${suggestion.createdAt} (نقاط: $recencyScore)');
    print('   - الاهتمامات المشتركة: ${suggestion.commonInterests.length}');
    print('   - المجموع النهائي: ${priority.round()}');
    print('   ---');

    return priority.round();
  }

  /// حساب نقاط المتابعين
  static double _calculateFollowersScore(int followersCount) {
    if (followersCount >= 500) return 300;
    if (followersCount >= 200) return 250;
    if (followersCount >= 100) return 200;
    if (followersCount >= 50) return 150;
    if (followersCount >= 20) return 100;
    if (followersCount >= 10) return 75;
    if (followersCount >= 5) return 50;
    return 25; // للمستخدمين الجدد
  }

  /// حساب نقاط المنشورات (النشاط)
  static double _calculatePostsScore(int postsCount) {
    if (postsCount >= 50) return 200;
    if (postsCount >= 30) return 150;
    if (postsCount >= 20) return 120;
    if (postsCount >= 10) return 100;
    if (postsCount >= 5) return 75;
    if (postsCount >= 2) return 50;
    if (postsCount >= 1) return 25;
    return 0; // لا توجد منشورات
  }

  /// حساب نقاط الانضمام الحديث
  static double _calculateRecencyScore(DateTime createdAt) {
    final now = DateTime.now();
    final daysSinceJoined = now.difference(createdAt).inDays;

    if (daysSinceJoined <= 7) return 150;   // أسبوع واحد
    if (daysSinceJoined <= 30) return 100;  // شهر واحد
    if (daysSinceJoined <= 90) return 75;   // 3 أشهر
    if (daysSinceJoined <= 180) return 50;  // 6 أشهر
    if (daysSinceJoined <= 365) return 25;  // سنة واحدة
    return 0; // أكثر من سنة
  }

  /// الحصول على موضع عشوائي لإدراج الاقتراح
  static int getRandomInsertPosition(int totalPosts) {
    if (totalPosts <= 2) {
      return totalPosts; // إذا كان عدد المنشورات قليل
    }

    // إدراج الاقتراح في موضع عشوائي بين المنشور الثاني والأخير
    final minPosition = 2;
    final maxPosition = totalPosts - 1;
    final position = minPosition + _random.nextInt(maxPosition - minPosition + 1);

    return position;
  }

  /// تحديث حالة المتابعة
  static Future<bool> updateFollowStatus({
    required String currentUserId,
    required String targetUserId,
    required bool isFollowing,
  }) async {
    try {
      if (isFollowing) {
        // إضافة المتابعة
        await _database.child('users/$currentUserId/following/$targetUserId').set(true);
        await _database.child('users/$targetUserId/followers/$currentUserId').set(true);
        
        // تحديث العدادات
        await _updateFollowCounts(currentUserId, targetUserId, increment: true);
      } else {
        // إلغاء المتابعة
        await _database.child('users/$currentUserId/following/$targetUserId').remove();
        await _database.child('users/$targetUserId/followers/$currentUserId').remove();
        
        // تحديث العدادات
        await _updateFollowCounts(currentUserId, targetUserId, increment: false);
      }
      
      return true;
    } catch (e) {
      print('❌ خطأ في تحديث حالة المتابعة: $e');
      return false;
    }
  }

  /// تحديث عدادات المتابعة
  static Future<void> _updateFollowCounts(
    String currentUserId, 
    String targetUserId, 
    {required bool increment}
  ) async {
    final delta = increment ? 1 : -1;
    
    // تحديث عداد المتابَعين للمستخدم الحالي
    final currentUserRef = _database.child('users/$currentUserId');
    final currentUserSnapshot = await currentUserRef.child('followingCount').get();
    final currentFollowingCount = (currentUserSnapshot.value as int? ?? 0) + delta;
    await currentUserRef.child('followingCount').set(math.max(0, currentFollowingCount));
    
    // تحديث عداد المتابعين للمستخدم المستهدف
    final targetUserRef = _database.child('users/$targetUserId');
    final targetUserSnapshot = await targetUserRef.child('followersCount').get();
    final targetFollowersCount = (targetUserSnapshot.value as int? ?? 0) + delta;
    await targetUserRef.child('followersCount').set(math.max(0, targetFollowersCount));
  }

  /// التحقق من إمكانية تضمين المستخدم في اقتراحات المتابعة
  static bool _shouldIncludeInSuggestions(UserModel user) {
    // التحقق من إعدادات الخصوصية الأساسية

    // 1. التحقق من أن الملف الشخصي عام (افتراضياً true إذا كان null)
    final isProfilePublic = user.isProfilePublic ?? true;
    if (!isProfilePublic) {
      print('🔒 ${user.fullName}: الملف الشخصي خاص');
      return false;
    }

    // 2. التحقق من أن المستخدم يسمح بالمتابعة (افتراضياً true إذا كان null)
    final allowFollowing = user.allowFollowing ?? true;
    if (!allowFollowing) {
      print('🔒 ${user.fullName}: لا يسمح بالمتابعة');
      return false;
    }

    // 3. التحقق من أن المستخدم غير محظور أو مقيد
    if (user.isBanned == true) {
      print('🔒 ${user.fullName}: محظور');
      return false;
    }

    if (user.isRestricted == true) {
      print('🔒 ${user.fullName}: مقيد');
      return false;
    }

    // 4. التحقق من أن المستخدم ليس مديراً (اختياري - يمكن إزالة هذا الشرط)
    // if (user.isAdmin == true) {
    //   return false;
    // }

    return true;
  }
}
