# تحديث قواعد Firebase Database لإجراءات المستخدمين

## المشكلة الحالية
```
Index not defined, add ".indexOn": "createdBy", for path "/mind_maps", to the rules
```

## الحل

### 1. الذهاب إلى Firebase Console
1. افتح [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروعك
3. اذهب إلى **Realtime Database**
4. اختر تبويب **Rules**

### 2. تحديث القواعد
انسخ محتوى ملف `updated_firebase_rules.json` والصقه في قواعد Firebase.

### 3. الإضافات الجديدة المطلوبة
أضف هذه الأقسام لقواعدك الحالية:

### 4. الإضافات الجديدة فقط
أضف هذه الأقسام لقواعدك الموجودة:

#### 1. تحديث فهرسة المستخدمين:
```json
"users": {
  ".indexOn": ["email", "createdAt", "university", "major", "isAdmin"]
}
```

#### 2. تحديث فهرسة المخططات الذهنية:
```json
"userProjects": {
  "$userId": {
    "mindMaps": {
      ".indexOn": ["subject", "createdAt", "isPublished", "createdBy"]
    },
    "subjects": {
      ".indexOn": ["name", "createdAt"]
    }
  }
}
```

#### 3. إضافة قسم تحذيرات المستخدمين:
```json
"user_warnings": {
  "$userId": {
    ".indexOn": ["sentAt", "sentBy", "isRead"],
    ".read": "auth != null && (auth.uid == $userId || root.child('users').child(auth.uid).child('isAdmin').val() == true)",
    ".write": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true"
  }
}
```

#### 4. إضافة قسم إجراءات المستخدمين:
```json
"user_actions": {
  "$userId": {
    ".indexOn": ["actionType", "actionDate", "actionBy"],
    ".read": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true",
    ".write": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true"
  }
}
```

#### 5. إضافة قسم إحصائيات المدير:
```json
"admin_statistics": {
  ".read": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true",
  ".write": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true"
}
```

### 4. فوائد الفهرسة
- ✅ **أداء أسرع** للاستعلامات
- ✅ **تكلفة أقل** لقراءة البيانات
- ✅ **استعلامات معقدة** ممكنة
- ✅ **ترتيب وفلترة** فعالة

### 5. بعد تطبيق القواعد
1. احفظ القواعد في Firebase Console
2. انتظر بضع دقائق لتطبيق التغييرات
3. أعد تشغيل التطبيق
4. ستختفي رسائل الخطأ المتعلقة بالفهرسة

### 6. التحقق من نجاح التطبيق
في debug console، يجب أن ترى:
```
✅ تم تحميل X مستخدم
📊 إحصائيات المستخدم: {posts: X, mindMaps: X, warnings: X}
```

بدلاً من:
```
❌ خطأ في تحميل إحصائيات المستخدم: Index not defined
```

### 7. ملاحظات مهمة
- **الأمان**: القواعد تضمن أن المدراء فقط يمكنهم الوصول لبيانات المستخدمين
- **الخصوصية**: كل مستخدم يمكنه الوصول لبياناته فقط
- **الأداء**: الفهرسة تحسن سرعة الاستعلامات بشكل كبير

### 8. إذا لم تعمل القواعد
1. تأكد من نسخ القواعد بالكامل
2. تأكد من عدم وجود أخطاء في JSON syntax
3. جرب إعادة تشغيل التطبيق
4. تحقق من أن المستخدم الحالي له صلاحيات admin
