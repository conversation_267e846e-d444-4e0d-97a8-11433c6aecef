import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';

class QuickCommentWidget extends StatefulWidget {
  final Post post;

  const QuickCommentWidget({
    Key? key,
    required this.post,
  }) : super(key: key);

  @override
  State<QuickCommentWidget> createState() => _QuickCommentWidgetState();
}

class _QuickCommentWidgetState extends State<QuickCommentWidget> {
  final TextEditingController _controller = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.userModel;
        
        if (user == null) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(top: BorderSide(color: Colors.grey.shade200)),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.blue.shade100,
                child: Text(
                  _getAuthorInitial('${user.firstName} ${user.lastName}'.trim()),
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextField(
                  controller: _controller,
                  decoration: InputDecoration(
                    hintText: 'اكتب تعليقاً...',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: BorderSide(color: Colors.blue.shade400),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    suffixIcon: _isSubmitting
                        ? const Padding(
                            padding: EdgeInsets.all(12),
                            child: SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            ),
                          )
                        : IconButton(
                            onPressed: _submitComment,
                            icon: Icon(
                              Icons.send,
                              color: Colors.blue.shade600,
                              size: 20,
                            ),
                          ),
                  ),
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _submitComment(),
                  maxLines: 1,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _submitComment() async {
    final content = _controller.text.trim();
    if (content.isEmpty || _isSubmitting) return;

    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final user = authProvider.userModel;

    if (user == null) return;

    // التحقق من تقييد التعليق
    final restriction = await RestrictionService.checkCommentingRestriction(user.uid);
    if (restriction != null && mounted) {
      await RestrictionDialog.show(context, restriction, 'التعليق');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      await postsProvider.addComment(
        postId: widget.post.id,
        content: content,
        author: user,
      );

      _controller.clear();

      // إظهار رسالة نجاح مختصرة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم إضافة التعليق'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }
}
