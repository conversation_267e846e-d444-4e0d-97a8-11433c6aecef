import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/mind_map_template.dart';
import '../models/mind_map_node.dart';
import '../models/mind_map_connection.dart';

class PredefinedTemplates {
  static const _uuid = Uuid();

  // قائمة جميع القوالب المحددة مسبقاً
  static List<MindMapTemplate> getAllTemplates() {
    return [
      _createMathematicsTemplate(),
      _createPhysicsTemplate(),
      _createChemistryTemplate(),
      _createHistoryTemplate(),
      _createArabicTemplate(),
      _createBiologyTemplate(),
      _createGeographyTemplate(),
      _createLiteratureTemplate(),
      _createPhilosophyTemplate(),
      _createComputerScienceTemplate(),
      _createSociologyTemplate(),
      _createSociologyResearchTemplate(),
      _createSociologyOrganizationTemplate(),
      _createOrganizationalTheoriesTemplate(),
      _createOrganizationalBehaviorTemplate(),
      _createHumanResourcesTemplate(),
      _createOrganizationalCultureTemplate(),
      _createOrganizationalLeadershipTemplate(),
      _createOrganizationalCommunicationTemplate(),
      _createOrganizationalConflictTemplate(),
      _createOrganizationalChangeTemplate(),
      _createWorkPerformanceTemplate(),
      _createWorkEnvironmentTemplate(),
      _createSociologyUrbanTemplate(),
      _createSociologyRuralTemplate(),
      _createSociologyFamilyTemplate(),
      _createSociologyEducationTemplate(),
      _createSociologyCrimeTemplate(),
      _createSociologyPoliticalTemplate(),
      _createSociologyReligionTemplate(),
      _createPsychologyTemplate(),
      _createPsychologyDevelopmentTemplate(),
      _createEducationTemplate(),
      _createEducationMethodsTemplate(),
    ];
  }

  // قالب الرياضيات - المعادلات التربيعية
  static MindMapTemplate _createMathematicsTemplate() {
    final nodes = [
      TemplateNode(
        id: 'math_root',
        title: 'المعادلات التربيعية',
        description: 'ax² + bx + c = 0',
        position: const Offset(1000, 500),
        color: Colors.blue,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'math_formula',
        title: 'القانون العام',
        description: 'x = (-b ± √(b²-4ac)) / 2a',
        position: const Offset(800, 350),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'math_discriminant',
        title: 'المميز (Δ)',
        description: 'Δ = b² - 4ac',
        position: const Offset(1200, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'math_cases',
        title: 'حالات الحلول',
        description: '',
        position: const Offset(1200, 650),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'math_positive',
        title: 'Δ > 0',
        description: 'حلان حقيقيان مختلفان',
        position: const Offset(1000, 800),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'math_zero',
        title: 'Δ = 0',
        description: 'حل حقيقي واحد (مكرر)',
        position: const Offset(1200, 800),
        color: Colors.yellow,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'math_negative',
        title: 'Δ < 0',
        description: 'لا يوجد حلول حقيقية',
        position: const Offset(1400, 800),
        color: Colors.red,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'conn1',
        fromNodeId: 'math_root',
        toNodeId: 'math_formula',
        label: 'الحل',
        color: Colors.blue,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'conn2',
        fromNodeId: 'math_root',
        toNodeId: 'math_discriminant',
        label: 'المميز',
        color: Colors.blue,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'conn3',
        fromNodeId: 'math_discriminant',
        toNodeId: 'math_cases',
        label: 'يحدد',
        color: Colors.orange,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'conn4',
        fromNodeId: 'math_cases',
        toNodeId: 'math_positive',
        label: '',
        color: Colors.purple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'conn5',
        fromNodeId: 'math_cases',
        toNodeId: 'math_zero',
        label: '',
        color: Colors.purple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'conn6',
        fromNodeId: 'math_cases',
        toNodeId: 'math_negative',
        label: '',
        color: Colors.purple,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'المعادلات التربيعية',
      description: 'قالب شامل لدراسة المعادلات التربيعية وحلولها',
      category: TemplateCategory.mathematics,
      subject: 'الرياضيات',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'math_root',
      createdAt: DateTime.now(),
      tags: ['معادلات', 'جبر', 'رياضيات'],
    );
  }

  // قالب الفيزياء - قوانين نيوتن
  static MindMapTemplate _createPhysicsTemplate() {
    final nodes = [
      TemplateNode(
        id: 'physics_root',
        title: 'قوانين نيوتن للحركة',
        description: 'الأسس الثلاثة للميكانيكا الكلاسيكية',
        position: const Offset(1000, 500),
        color: Colors.purple,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'physics_first',
        title: 'القانون الأول',
        description: 'قانون القصور الذاتي',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'physics_second',
        title: 'القانون الثاني',
        description: 'F = ma',
        position: const Offset(1000, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'physics_third',
        title: 'القانون الثالث',
        description: 'لكل فعل رد فعل مساوٍ ومضاد',
        position: const Offset(1300, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'physics_inertia',
        title: 'القصور الذاتي',
        description: 'الجسم يبقى في حالة سكون أو حركة منتظمة',
        position: const Offset(500, 200),
        color: Colors.lightBlue,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'physics_force',
        title: 'القوة',
        description: 'F = القوة المحصلة',
        position: const Offset(800, 150),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'physics_mass',
        title: 'الكتلة',
        description: 'm = كتلة الجسم',
        position: const Offset(1000, 150),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'physics_acceleration',
        title: 'التسارع',
        description: 'a = التسارع',
        position: const Offset(1200, 150),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'phys_conn1',
        fromNodeId: 'physics_root',
        toNodeId: 'physics_first',
        label: '1',
        color: Colors.purple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phys_conn2',
        fromNodeId: 'physics_root',
        toNodeId: 'physics_second',
        label: '2',
        color: Colors.purple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phys_conn3',
        fromNodeId: 'physics_root',
        toNodeId: 'physics_third',
        label: '3',
        color: Colors.purple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phys_conn4',
        fromNodeId: 'physics_first',
        toNodeId: 'physics_inertia',
        label: 'يعني',
        color: Colors.blue,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phys_conn5',
        fromNodeId: 'physics_second',
        toNodeId: 'physics_force',
        label: '',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phys_conn6',
        fromNodeId: 'physics_second',
        toNodeId: 'physics_mass',
        label: '',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phys_conn7',
        fromNodeId: 'physics_second',
        toNodeId: 'physics_acceleration',
        label: '',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'قوانين نيوتن للحركة',
      description: 'القوانين الثلاثة الأساسية للميكانيكا الكلاسيكية',
      category: TemplateCategory.physics,
      subject: 'الفيزياء',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'physics_root',
      createdAt: DateTime.now(),
      tags: ['نيوتن', 'حركة', 'ميكانيكا', 'فيزياء'],
    );
  }

  // قالب الكيمياء - الجدول الدوري
  static MindMapTemplate _createChemistryTemplate() {
    final nodes = [
      TemplateNode(
        id: 'chem_root',
        title: 'الجدول الدوري',
        description: 'تصنيف العناصر الكيميائية',
        position: const Offset(1000, 500),
        color: Colors.green,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'chem_groups',
        title: 'المجموعات',
        description: 'الأعمدة الرأسية',
        position: const Offset(800, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'chem_periods',
        title: 'الدورات',
        description: 'الصفوف الأفقية',
        position: const Offset(1200, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'chem_metals',
        title: 'الفلزات',
        description: 'موصلة للكهرباء والحرارة',
        position: const Offset(700, 650),
        color: Colors.grey,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'chem_nonmetals',
        title: 'اللافلزات',
        description: 'عازلة للكهرباء والحرارة',
        position: const Offset(1000, 650),
        color: Colors.yellow,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'chem_metalloids',
        title: 'أشباه الفلزات',
        description: 'خصائص متوسطة',
        position: const Offset(1300, 650),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'chem_conn1',
        fromNodeId: 'chem_root',
        toNodeId: 'chem_groups',
        label: 'ينقسم إلى',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'chem_conn2',
        fromNodeId: 'chem_root',
        toNodeId: 'chem_periods',
        label: 'ينقسم إلى',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'chem_conn3',
        fromNodeId: 'chem_root',
        toNodeId: 'chem_metals',
        label: 'يحتوي على',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'chem_conn4',
        fromNodeId: 'chem_root',
        toNodeId: 'chem_nonmetals',
        label: 'يحتوي على',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'chem_conn5',
        fromNodeId: 'chem_root',
        toNodeId: 'chem_metalloids',
        label: 'يحتوي على',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الجدول الدوري',
      description: 'تصنيف وخصائص العناصر الكيميائية',
      category: TemplateCategory.chemistry,
      subject: 'الكيمياء',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'chem_root',
      createdAt: DateTime.now(),
      tags: ['عناصر', 'جدول دوري', 'كيمياء'],
    );
  }

  // قالب التاريخ - الحضارة الإسلامية
  static MindMapTemplate _createHistoryTemplate() {
    final nodes = [
      TemplateNode(
        id: 'hist_root',
        title: 'الحضارة الإسلامية',
        description: 'العصر الذهبي للإسلام',
        position: const Offset(1000, 500),
        color: Colors.amber,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'hist_science',
        title: 'العلوم',
        description: 'الطب، الرياضيات، الفلك',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hist_arts',
        title: 'الفنون',
        description: 'العمارة، الخط، الزخرفة',
        position: const Offset(1300, 350),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hist_literature',
        title: 'الأدب',
        description: 'الشعر، النثر، القصص',
        position: const Offset(1000, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hist_philosophy',
        title: 'الفلسفة',
        description: 'ابن سينا، الفارابي، ابن رشد',
        position: const Offset(800, 650),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hist_trade',
        title: 'التجارة',
        description: 'طريق الحرير، التجارة البحرية',
        position: const Offset(1200, 650),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'hist_conn1',
        fromNodeId: 'hist_root',
        toNodeId: 'hist_science',
        label: 'ازدهرت',
        color: Colors.amber,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hist_conn2',
        fromNodeId: 'hist_root',
        toNodeId: 'hist_arts',
        label: 'ازدهرت',
        color: Colors.amber,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hist_conn3',
        fromNodeId: 'hist_root',
        toNodeId: 'hist_literature',
        label: 'ازدهر',
        color: Colors.amber,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hist_conn4',
        fromNodeId: 'hist_root',
        toNodeId: 'hist_philosophy',
        label: 'ازدهرت',
        color: Colors.amber,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hist_conn5',
        fromNodeId: 'hist_root',
        toNodeId: 'hist_trade',
        label: 'ازدهرت',
        color: Colors.amber,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الحضارة الإسلامية',
      description: 'العصر الذهبي للحضارة الإسلامية وإنجازاتها',
      category: TemplateCategory.history,
      subject: 'التاريخ',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'hist_root',
      createdAt: DateTime.now(),
      tags: ['حضارة', 'إسلام', 'تاريخ', 'عصر ذهبي'],
    );
  }

  // قالب اللغة العربية - أقسام الكلام
  static MindMapTemplate _createArabicTemplate() {
    final nodes = [
      TemplateNode(
        id: 'arab_root',
        title: 'أقسام الكلام',
        description: 'تصنيف الكلمات في اللغة العربية',
        position: const Offset(1000, 500),
        color: Colors.red,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'arab_noun',
        title: 'الاسم',
        description: 'ما دل على معنى في نفسه',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'arab_verb',
        title: 'الفعل',
        description: 'ما دل على حدث مقترن بزمن',
        position: const Offset(1000, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'arab_particle',
        title: 'الحرف',
        description: 'ما دل على معنى في غيره',
        position: const Offset(1300, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'arab_past',
        title: 'الماضي',
        description: 'دل على حدث في الزمن الماضي',
        position: const Offset(800, 150),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'arab_present',
        title: 'المضارع',
        description: 'دل على حدث في الحاضر أو المستقبل',
        position: const Offset(1000, 150),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
      TemplateNode(
        id: 'arab_imperative',
        title: 'الأمر',
        description: 'دل على طلب الفعل',
        position: const Offset(1200, 150),
        color: Colors.lightGreen,
        shape: NodeShape.oval,
        fontSize: 12.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'arab_conn1',
        fromNodeId: 'arab_root',
        toNodeId: 'arab_noun',
        label: '1',
        color: Colors.red,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'arab_conn2',
        fromNodeId: 'arab_root',
        toNodeId: 'arab_verb',
        label: '2',
        color: Colors.red,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'arab_conn3',
        fromNodeId: 'arab_root',
        toNodeId: 'arab_particle',
        label: '3',
        color: Colors.red,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'arab_conn4',
        fromNodeId: 'arab_verb',
        toNodeId: 'arab_past',
        label: 'ينقسم إلى',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'arab_conn5',
        fromNodeId: 'arab_verb',
        toNodeId: 'arab_present',
        label: 'ينقسم إلى',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'arab_conn6',
        fromNodeId: 'arab_verb',
        toNodeId: 'arab_imperative',
        label: 'ينقسم إلى',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'أقسام الكلام',
      description: 'تصنيف الكلمات في النحو العربي',
      category: TemplateCategory.arabic,
      subject: 'اللغة العربية',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'arab_root',
      createdAt: DateTime.now(),
      tags: ['نحو', 'كلام', 'عربية', 'قواعد'],
    );
  }

  // قالب الأحياء - الخلية
  static MindMapTemplate _createBiologyTemplate() {
    final nodes = [
      TemplateNode(
        id: 'bio_root',
        title: 'الخلية',
        description: 'الوحدة الأساسية للحياة',
        position: const Offset(1000, 500),
        color: Colors.green,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'bio_nucleus',
        title: 'النواة',
        description: 'مركز التحكم في الخلية',
        position: const Offset(800, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'bio_cytoplasm',
        title: 'السيتوبلازم',
        description: 'المادة الهلامية داخل الخلية',
        position: const Offset(1200, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'bio_membrane',
        title: 'الغشاء الخلوي',
        description: 'يحيط بالخلية وينظم دخول وخروج المواد',
        position: const Offset(1000, 650),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'bio_conn1',
        fromNodeId: 'bio_root',
        toNodeId: 'bio_nucleus',
        label: 'تحتوي على',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'bio_conn2',
        fromNodeId: 'bio_root',
        toNodeId: 'bio_cytoplasm',
        label: 'تحتوي على',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'bio_conn3',
        fromNodeId: 'bio_root',
        toNodeId: 'bio_membrane',
        label: 'محاطة بـ',
        color: Colors.green,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الخلية',
      description: 'مكونات الخلية ووظائفها الأساسية',
      category: TemplateCategory.biology,
      subject: 'الأحياء',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'bio_root',
      createdAt: DateTime.now(),
      tags: ['خلية', 'أحياء', 'علوم'],
    );
  }

  // قالب الجغرافيا - القارات
  static MindMapTemplate _createGeographyTemplate() {
    final nodes = [
      TemplateNode(
        id: 'geo_root',
        title: 'قارات العالم',
        description: 'القارات السبع',
        position: const Offset(1000, 500),
        color: Colors.cyan,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'geo_asia',
        title: 'آسيا',
        description: 'أكبر القارات',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'geo_africa',
        title: 'أفريقيا',
        description: 'القارة السمراء',
        position: const Offset(1000, 250),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'geo_europe',
        title: 'أوروبا',
        description: 'القارة العجوز',
        position: const Offset(1300, 300),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'geo_america_north',
        title: 'أمريكا الشمالية',
        description: 'العالم الجديد',
        position: const Offset(600, 650),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'geo_america_south',
        title: 'أمريكا الجنوبية',
        description: 'قارة الأمازون',
        position: const Offset(900, 700),
        color: Colors.lightGreen,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'geo_australia',
        title: 'أستراليا',
        description: 'أصغر القارات',
        position: const Offset(1200, 700),
        color: Colors.yellow,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'geo_antarctica',
        title: 'القطب الجنوبي',
        description: 'القارة المتجمدة',
        position: const Offset(1400, 650),
        color: Colors.lightBlue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'geo_conn1',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_asia',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'geo_conn2',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_africa',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'geo_conn3',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_europe',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'geo_conn4',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_america_north',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'geo_conn5',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_america_south',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'geo_conn6',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_australia',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'geo_conn7',
        fromNodeId: 'geo_root',
        toNodeId: 'geo_antarctica',
        label: '',
        color: Colors.cyan,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'قارات العالم',
      description: 'القارات السبع وخصائصها',
      category: TemplateCategory.geography,
      subject: 'الجغرافيا',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'geo_root',
      createdAt: DateTime.now(),
      tags: ['قارات', 'جغرافيا', 'عالم'],
    );
  }

  // قالب الأدب - أنواع الشعر
  static MindMapTemplate _createLiteratureTemplate() {
    final nodes = [
      TemplateNode(
        id: 'lit_root',
        title: 'أنواع الشعر',
        description: 'تصنيف الشعر العربي',
        position: const Offset(1000, 500),
        color: Colors.brown,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'lit_classical',
        title: 'الشعر الكلاسيكي',
        description: 'الشعر التقليدي',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'lit_modern',
        title: 'الشعر الحديث',
        description: 'شعر التفعيلة والنثر',
        position: const Offset(1300, 350),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'lit_conn1',
        fromNodeId: 'lit_root',
        toNodeId: 'lit_classical',
        label: 'ينقسم إلى',
        color: Colors.brown,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'lit_conn2',
        fromNodeId: 'lit_root',
        toNodeId: 'lit_modern',
        label: 'ينقسم إلى',
        color: Colors.brown,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'أنواع الشعر',
      description: 'تصنيف الشعر العربي وأنواعه',
      category: TemplateCategory.literature,
      subject: 'الأدب',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'lit_root',
      createdAt: DateTime.now(),
      tags: ['شعر', 'أدب', 'عربي'],
    );
  }

  // قالب الفلسفة - مدارس الفكر
  static MindMapTemplate _createPhilosophyTemplate() {
    final nodes = [
      TemplateNode(
        id: 'phil_root',
        title: 'مدارس الفكر الفلسفي',
        description: 'التيارات الفلسفية الرئيسية',
        position: const Offset(1000, 500),
        color: Colors.indigo,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'phil_rationalism',
        title: 'العقلانية',
        description: 'الاعتماد على العقل',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'phil_empiricism',
        title: 'التجريبية',
        description: 'الاعتماد على التجربة',
        position: const Offset(1300, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'phil_conn1',
        fromNodeId: 'phil_root',
        toNodeId: 'phil_rationalism',
        label: 'تشمل',
        color: Colors.indigo,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'phil_conn2',
        fromNodeId: 'phil_root',
        toNodeId: 'phil_empiricism',
        label: 'تشمل',
        color: Colors.indigo,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'مدارس الفكر الفلسفي',
      description: 'التيارات الفلسفية الرئيسية ومناهجها',
      category: TemplateCategory.philosophy,
      subject: 'الفلسفة',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'phil_root',
      createdAt: DateTime.now(),
      tags: ['فلسفة', 'فكر', 'مدارس'],
    );
  }

  // قالب علوم الحاسوب - هياكل البيانات
  static MindMapTemplate _createComputerScienceTemplate() {
    final nodes = [
      TemplateNode(
        id: 'cs_root',
        title: 'هياكل البيانات',
        description: 'طرق تنظيم وتخزين البيانات',
        position: const Offset(1000, 500),
        color: Colors.deepPurple,
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'cs_array',
        title: 'المصفوفات',
        description: 'Arrays',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'cs_list',
        title: 'القوائم المترابطة',
        description: 'Linked Lists',
        position: const Offset(1000, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'cs_stack',
        title: 'المكدس',
        description: 'Stack - LIFO',
        position: const Offset(1300, 350),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'cs_queue',
        title: 'الطابور',
        description: 'Queue - FIFO',
        position: const Offset(800, 650),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'cs_tree',
        title: 'الأشجار',
        description: 'Trees',
        position: const Offset(1200, 650),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'cs_conn1',
        fromNodeId: 'cs_root',
        toNodeId: 'cs_array',
        label: 'تشمل',
        color: Colors.deepPurple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'cs_conn2',
        fromNodeId: 'cs_root',
        toNodeId: 'cs_list',
        label: 'تشمل',
        color: Colors.deepPurple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'cs_conn3',
        fromNodeId: 'cs_root',
        toNodeId: 'cs_stack',
        label: 'تشمل',
        color: Colors.deepPurple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'cs_conn4',
        fromNodeId: 'cs_root',
        toNodeId: 'cs_queue',
        label: 'تشمل',
        color: Colors.deepPurple,
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'cs_conn5',
        fromNodeId: 'cs_root',
        toNodeId: 'cs_tree',
        label: 'تشمل',
        color: Colors.deepPurple,
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'هياكل البيانات',
      description: 'هياكل البيانات الأساسية في علوم الحاسوب',
      category: TemplateCategory.computer,
      subject: 'علوم الحاسوب',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'cs_root',
      createdAt: DateTime.now(),
      tags: ['حاسوب', 'برمجة', 'هياكل', 'بيانات'],
    );
  }

  // قالب علم الاجتماع - النظريات الاجتماعية
  static MindMapTemplate _createSociologyTemplate() {
    final nodes = [
      TemplateNode(
        id: 'soc_root',
        title: 'النظريات الاجتماعية',
        description: 'النظريات الأساسية في علم الاجتماع',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'soc_functionalism',
        title: 'الوظيفية',
        description: 'نظرية دوركايم وبارسونز',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soc_conflict',
        title: 'نظرية الصراع',
        description: 'نظرية ماركس وويبر',
        position: const Offset(1300, 350),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soc_symbolic',
        title: 'التفاعلية الرمزية',
        description: 'نظرية ميد وبلومر',
        position: const Offset(1000, 250),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soc_institutions',
        title: 'المؤسسات الاجتماعية',
        description: 'الأسرة، التعليم، الدين',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soc_stratification',
        title: 'التدرج الاجتماعي',
        description: 'الطبقات والحراك الاجتماعي',
        position: const Offset(1000, 700),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soc_deviance',
        title: 'الانحراف الاجتماعي',
        description: 'السلوك المنحرف والضبط',
        position: const Offset(1400, 600),
        color: Colors.brown,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'soc_conn1',
        fromNodeId: 'soc_root',
        toNodeId: 'soc_functionalism',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soc_conn2',
        fromNodeId: 'soc_root',
        toNodeId: 'soc_conflict',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soc_conn3',
        fromNodeId: 'soc_root',
        toNodeId: 'soc_symbolic',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soc_conn4',
        fromNodeId: 'soc_root',
        toNodeId: 'soc_institutions',
        label: 'تدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soc_conn5',
        fromNodeId: 'soc_root',
        toNodeId: 'soc_stratification',
        label: 'تدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soc_conn6',
        fromNodeId: 'soc_root',
        toNodeId: 'soc_deviance',
        label: 'تدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'النظريات الاجتماعية',
      description: 'النظريات الأساسية والمفاهيم في علم الاجتماع',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'soc_root',
      createdAt: DateTime.now(),
      tags: ['اجتماع', 'نظريات', 'مجتمع', 'سلوك'],
    );
  }

  // قالب علم النفس - فروع علم النفس
  static MindMapTemplate _createPsychologyTemplate() {
    final nodes = [
      TemplateNode(
        id: 'psy_root',
        title: 'فروع علم النفس',
        description: 'التخصصات الرئيسية في علم النفس',
        position: const Offset(1000, 500),
        color: const Color(0xFF9C27B0),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'psy_cognitive',
        title: 'علم النفس المعرفي',
        description: 'دراسة العمليات العقلية',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psy_clinical',
        title: 'علم النفس الإكلينيكي',
        description: 'التشخيص والعلاج النفسي',
        position: const Offset(1300, 300),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psy_developmental',
        title: 'علم النفس التطوري',
        description: 'نمو الإنسان عبر المراحل',
        position: const Offset(1000, 200),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psy_social',
        title: 'علم النفس الاجتماعي',
        description: 'السلوك في السياق الاجتماعي',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psy_educational',
        title: 'علم النفس التربوي',
        description: 'التعلم والتعليم',
        position: const Offset(1000, 750),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psy_personality',
        title: 'علم نفس الشخصية',
        description: 'نظريات الشخصية والفروق الفردية',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psy_abnormal',
        title: 'علم النفس المرضي',
        description: 'الاضطرابات النفسية',
        position: const Offset(500, 450),
        color: Colors.brown,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'psy_conn1',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_cognitive',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psy_conn2',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_clinical',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psy_conn3',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_developmental',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psy_conn4',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_social',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psy_conn5',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_educational',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psy_conn6',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_personality',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psy_conn7',
        fromNodeId: 'psy_root',
        toNodeId: 'psy_abnormal',
        label: 'يشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'فروع علم النفس',
      description: 'التخصصات والفروع الرئيسية في علم النفس',
      category: TemplateCategory.psychology,
      subject: 'علم النفس',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'psy_root',
      createdAt: DateTime.now(),
      tags: ['نفس', 'سلوك', 'معرفة', 'شخصية'],
    );
  }

  // قالب علوم التربية - نظريات التعلم
  static MindMapTemplate _createEducationTemplate() {
    final nodes = [
      TemplateNode(
        id: 'edu_root',
        title: 'نظريات التعلم',
        description: 'النظريات الأساسية في التعلم والتعليم',
        position: const Offset(1000, 500),
        color: const Color(0xFF4CAF50),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'edu_behaviorism',
        title: 'النظرية السلوكية',
        description: 'بافلوف، سكينر، واطسون',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edu_cognitivism',
        title: 'النظرية المعرفية',
        description: 'بياجيه، برونر، أوزوبل',
        position: const Offset(1300, 300),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edu_constructivism',
        title: 'النظرية البنائية',
        description: 'فيجوتسكي، التعلم النشط',
        position: const Offset(1000, 200),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edu_humanistic',
        title: 'النظرية الإنسانية',
        description: 'ماسلو، روجرز، التعلم الذاتي',
        position: const Offset(600, 600),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edu_social',
        title: 'نظرية التعلم الاجتماعي',
        description: 'باندورا، التعلم بالملاحظة',
        position: const Offset(1400, 600),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edu_multiple_intelligence',
        title: 'نظرية الذكاءات المتعددة',
        description: 'جاردنر، أنواع الذكاء',
        position: const Offset(1000, 750),
        color: Colors.indigo,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edu_bloom',
        title: 'تصنيف بلوم',
        description: 'مستويات الأهداف التعليمية',
        position: const Offset(500, 400),
        color: Colors.brown,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'edu_conn1',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_behaviorism',
        label: 'تتضمن',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edu_conn2',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_cognitivism',
        label: 'تتضمن',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edu_conn3',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_constructivism',
        label: 'تتضمن',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edu_conn4',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_humanistic',
        label: 'تتضمن',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edu_conn5',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_social',
        label: 'تتضمن',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edu_conn6',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_multiple_intelligence',
        label: 'تتضمن',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edu_conn7',
        fromNodeId: 'edu_root',
        toNodeId: 'edu_bloom',
        label: 'تستخدم',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'نظريات التعلم',
      description: 'النظريات التربوية الأساسية في التعلم والتعليم',
      category: TemplateCategory.education,
      subject: 'علوم التربية',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'edu_root',
      createdAt: DateTime.now(),
      tags: ['تربية', 'تعلم', 'تعليم', 'نظريات'],
    );
  }

  // قالب علم الاجتماع - البحث الاجتماعي
  static MindMapTemplate _createSociologyResearchTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socres_root',
        title: 'البحث الاجتماعي',
        description: 'مناهج وأدوات البحث في علم الاجتماع',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socres_quantitative',
        title: 'البحث الكمي',
        description: 'الاستبيانات والإحصاء',
        position: const Offset(700, 350),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socres_qualitative',
        title: 'البحث النوعي',
        description: 'المقابلات والملاحظة',
        position: const Offset(1300, 350),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socres_survey',
        title: 'المسح الاجتماعي',
        description: 'دراسة المجتمعات والظواهر',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socres_ethnography',
        title: 'الإثنوغرافيا',
        description: 'دراسة الثقافات والمجتمعات',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socres_case_study',
        title: 'دراسة الحالة',
        description: 'التحليل المعمق للحالات',
        position: const Offset(1000, 700),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socres_conn1',
        fromNodeId: 'socres_root',
        toNodeId: 'socres_quantitative',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socres_conn2',
        fromNodeId: 'socres_root',
        toNodeId: 'socres_qualitative',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socres_conn3',
        fromNodeId: 'socres_root',
        toNodeId: 'socres_survey',
        label: 'يستخدم',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socres_conn4',
        fromNodeId: 'socres_root',
        toNodeId: 'socres_ethnography',
        label: 'يستخدم',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socres_conn5',
        fromNodeId: 'socres_root',
        toNodeId: 'socres_case_study',
        label: 'يستخدم',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'البحث الاجتماعي',
      description: 'مناهج وأدوات البحث في علم الاجتماع',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socres_root',
      createdAt: DateTime.now(),
      tags: ['بحث', 'منهجية', 'اجتماع', 'دراسة'],
    );
  }

  // قالب علم النفس - مراحل النمو النفسي
  static MindMapTemplate _createPsychologyDevelopmentTemplate() {
    final nodes = [
      TemplateNode(
        id: 'psydev_root',
        title: 'مراحل النمو النفسي',
        description: 'نظريات النمو والتطور النفسي',
        position: const Offset(1000, 500),
        color: const Color(0xFF9C27B0),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'psydev_piaget',
        title: 'نظرية بياجيه',
        description: 'مراحل النمو المعرفي',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psydev_erikson',
        title: 'نظرية إريكسون',
        description: 'مراحل النمو النفسي الاجتماعي',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psydev_freud',
        title: 'نظرية فرويد',
        description: 'مراحل النمو النفسي الجنسي',
        position: const Offset(1000, 200),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psydev_infancy',
        title: 'مرحلة الطفولة المبكرة',
        description: '0-2 سنة',
        position: const Offset(500, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psydev_childhood',
        title: 'مرحلة الطفولة',
        description: '2-12 سنة',
        position: const Offset(800, 700),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psydev_adolescence',
        title: 'مرحلة المراهقة',
        description: '12-18 سنة',
        position: const Offset(1200, 700),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'psydev_adulthood',
        title: 'مرحلة الرشد',
        description: '18+ سنة',
        position: const Offset(1500, 600),
        color: Colors.brown,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'psydev_conn1',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_piaget',
        label: 'تشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psydev_conn2',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_erikson',
        label: 'تشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psydev_conn3',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_freud',
        label: 'تشمل',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psydev_conn4',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_infancy',
        label: 'تمر بـ',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psydev_conn5',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_childhood',
        label: 'تمر بـ',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psydev_conn6',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_adolescence',
        label: 'تمر بـ',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'psydev_conn7',
        fromNodeId: 'psydev_root',
        toNodeId: 'psydev_adulthood',
        label: 'تمر بـ',
        color: const Color(0xFF9C27B0),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'مراحل النمو النفسي',
      description: 'نظريات ومراحل النمو والتطور النفسي',
      category: TemplateCategory.psychology,
      subject: 'علم النفس',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'psydev_root',
      createdAt: DateTime.now(),
      tags: ['نمو', 'تطور', 'مراحل', 'نفس'],
    );
  }

  // قالب علوم التربية - طرق التدريس
  static MindMapTemplate _createEducationMethodsTemplate() {
    final nodes = [
      TemplateNode(
        id: 'edumeth_root',
        title: 'طرق التدريس',
        description: 'الاستراتيجيات والطرق التعليمية',
        position: const Offset(1000, 500),
        color: const Color(0xFF4CAF50),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'edumeth_lecture',
        title: 'المحاضرة',
        description: 'الطريقة التقليدية',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edumeth_discussion',
        title: 'المناقشة',
        description: 'التعلم التفاعلي',
        position: const Offset(1300, 300),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edumeth_problem_solving',
        title: 'حل المشكلات',
        description: 'التعلم القائم على المشكلات',
        position: const Offset(1000, 200),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edumeth_cooperative',
        title: 'التعلم التعاوني',
        description: 'العمل في مجموعات',
        position: const Offset(600, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edumeth_project',
        title: 'التعلم بالمشاريع',
        description: 'التعلم القائم على المشاريع',
        position: const Offset(1400, 600),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edumeth_inquiry',
        title: 'التعلم بالاستقصاء',
        description: 'البحث والاكتشاف',
        position: const Offset(1000, 750),
        color: Colors.indigo,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'edumeth_technology',
        title: 'التعلم الإلكتروني',
        description: 'استخدام التكنولوجيا',
        position: const Offset(500, 400),
        color: Colors.brown,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'edumeth_conn1',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_lecture',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edumeth_conn2',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_discussion',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edumeth_conn3',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_problem_solving',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edumeth_conn4',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_cooperative',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edumeth_conn5',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_project',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edumeth_conn6',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_inquiry',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'edumeth_conn7',
        fromNodeId: 'edumeth_root',
        toNodeId: 'edumeth_technology',
        label: 'تشمل',
        color: const Color(0xFF4CAF50),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'طرق التدريس',
      description: 'الاستراتيجيات والطرق التعليمية المختلفة',
      category: TemplateCategory.education,
      subject: 'علوم التربية',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'edumeth_root',
      createdAt: DateTime.now(),
      tags: ['تدريس', 'طرق', 'استراتيجيات', 'تعليم'],
    );
  }

  // قالب علم اجتماع التنظيم والعمل
  static MindMapTemplate _createSociologyOrganizationTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socorg_root',
        title: 'علم اجتماع التنظيم والعمل',
        description: 'دراسة المنظمات وبيئة العمل',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socorg_structure',
        title: 'البناء التنظيمي',
        description: 'الهيكل والتسلسل الإداري',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socorg_culture',
        title: 'الثقافة التنظيمية',
        description: 'القيم والمعايير في المنظمة',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socorg_leadership',
        title: 'القيادة',
        description: 'أنماط وأساليب القيادة',
        position: const Offset(1000, 200),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socorg_motivation',
        title: 'الدافعية',
        description: 'نظريات التحفيز في العمل',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socorg_communication',
        title: 'الاتصال التنظيمي',
        description: 'شبكات وأنماط الاتصال',
        position: const Offset(1400, 600),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socorg_conflict',
        title: 'الصراع التنظيمي',
        description: 'إدارة النزاعات والصراعات',
        position: const Offset(1000, 750),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socorg_change',
        title: 'التغيير التنظيمي',
        description: 'إدارة التطوير والتغيير',
        position: const Offset(500, 400),
        color: Colors.indigo,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socorg_conn1',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_structure',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socorg_conn2',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_culture',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socorg_conn3',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_leadership',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socorg_conn4',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_motivation',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socorg_conn5',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_communication',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socorg_conn6',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_conflict',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socorg_conn7',
        fromNodeId: 'socorg_root',
        toNodeId: 'socorg_change',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم اجتماع التنظيم والعمل',
      description: 'دراسة المنظمات وبيئة العمل والسلوك التنظيمي',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socorg_root',
      createdAt: DateTime.now(),
      tags: ['تنظيم', 'عمل', 'إدارة', 'منظمات'],
    );
  }

  // قالب علم الاجتماع الحضري
  static MindMapTemplate _createSociologyUrbanTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socurb_root',
        title: 'علم الاجتماع الحضري',
        description: 'دراسة المجتمعات الحضرية والمدن',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socurb_urbanization',
        title: 'التحضر',
        description: 'عملية نمو المدن',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socurb_planning',
        title: 'التخطيط الحضري',
        description: 'تصميم وتنظيم المدن',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socurb_housing',
        title: 'الإسكان',
        description: 'مشاكل وسياسات الإسكان',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socurb_transport',
        title: 'النقل الحضري',
        description: 'أنظمة المواصلات',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socurb_environment',
        title: 'البيئة الحضرية',
        description: 'التلوث والمشاكل البيئية',
        position: const Offset(1000, 750),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socurb_segregation',
        title: 'الفصل الحضري',
        description: 'التمييز المكاني والاجتماعي',
        position: const Offset(1000, 200),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socurb_conn1',
        fromNodeId: 'socurb_root',
        toNodeId: 'socurb_urbanization',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socurb_conn2',
        fromNodeId: 'socurb_root',
        toNodeId: 'socurb_planning',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socurb_conn3',
        fromNodeId: 'socurb_root',
        toNodeId: 'socurb_housing',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socurb_conn4',
        fromNodeId: 'socurb_root',
        toNodeId: 'socurb_transport',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socurb_conn5',
        fromNodeId: 'socurb_root',
        toNodeId: 'socurb_environment',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socurb_conn6',
        fromNodeId: 'socurb_root',
        toNodeId: 'socurb_segregation',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم الاجتماع الحضري',
      description: 'دراسة المجتمعات الحضرية والحياة في المدن',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socurb_root',
      createdAt: DateTime.now(),
      tags: ['حضري', 'مدن', 'تخطيط', 'بيئة'],
    );
  }

  // قالب علم الاجتماع الريفي
  static MindMapTemplate _createSociologyRuralTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socrur_root',
        title: 'علم الاجتماع الريفي',
        description: 'دراسة المجتمعات الريفية والزراعية',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socrur_agriculture',
        title: 'النظم الزراعية',
        description: 'أنماط الإنتاج الزراعي',
        position: const Offset(700, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrur_community',
        title: 'المجتمع الريفي',
        description: 'خصائص الحياة الريفية',
        position: const Offset(1300, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrur_migration',
        title: 'الهجرة الريفية',
        description: 'النزوح من الريف للمدن',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrur_development',
        title: 'التنمية الريفية',
        description: 'برامج تطوير الريف',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrur_traditions',
        title: 'التقاليد الريفية',
        description: 'العادات والتراث الشعبي',
        position: const Offset(1000, 750),
        color: Colors.brown,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrur_economy',
        title: 'الاقتصاد الريفي',
        description: 'الأنشطة الاقتصادية الريفية',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socrur_conn1',
        fromNodeId: 'socrur_root',
        toNodeId: 'socrur_agriculture',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrur_conn2',
        fromNodeId: 'socrur_root',
        toNodeId: 'socrur_community',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrur_conn3',
        fromNodeId: 'socrur_root',
        toNodeId: 'socrur_migration',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrur_conn4',
        fromNodeId: 'socrur_root',
        toNodeId: 'socrur_development',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrur_conn5',
        fromNodeId: 'socrur_root',
        toNodeId: 'socrur_traditions',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrur_conn6',
        fromNodeId: 'socrur_root',
        toNodeId: 'socrur_economy',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم الاجتماع الريفي',
      description: 'دراسة المجتمعات الريفية والحياة الزراعية',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socrur_root',
      createdAt: DateTime.now(),
      tags: ['ريفي', 'زراعة', 'تقاليد', 'تنمية'],
    );
  }

  // قالب علم اجتماع الأسرة
  static MindMapTemplate _createSociologyFamilyTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socfam_root',
        title: 'علم اجتماع الأسرة',
        description: 'دراسة الأسرة والعلاقات الأسرية',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socfam_structure',
        title: 'بناء الأسرة',
        description: 'أنواع وأشكال الأسرة',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socfam_functions',
        title: 'وظائف الأسرة',
        description: 'الأدوار والمسؤوليات',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socfam_marriage',
        title: 'الزواج',
        description: 'أنماط وقوانين الزواج',
        position: const Offset(600, 600),
        color: Colors.pink,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socfam_socialization',
        title: 'التنشئة الاجتماعية',
        description: 'تربية الأطفال والقيم',
        position: const Offset(1400, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socfam_problems',
        title: 'المشاكل الأسرية',
        description: 'الطلاق والعنف الأسري',
        position: const Offset(1000, 750),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socfam_changes',
        title: 'التغيرات الأسرية',
        description: 'تطور الأسرة عبر الزمن',
        position: const Offset(1000, 200),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socfam_conn1',
        fromNodeId: 'socfam_root',
        toNodeId: 'socfam_structure',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socfam_conn2',
        fromNodeId: 'socfam_root',
        toNodeId: 'socfam_functions',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socfam_conn3',
        fromNodeId: 'socfam_root',
        toNodeId: 'socfam_marriage',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socfam_conn4',
        fromNodeId: 'socfam_root',
        toNodeId: 'socfam_socialization',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socfam_conn5',
        fromNodeId: 'socfam_root',
        toNodeId: 'socfam_problems',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socfam_conn6',
        fromNodeId: 'socfam_root',
        toNodeId: 'socfam_changes',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم اجتماع الأسرة',
      description: 'دراسة الأسرة والعلاقات والتنشئة الاجتماعية',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socfam_root',
      createdAt: DateTime.now(),
      tags: ['أسرة', 'زواج', 'تنشئة', 'علاقات'],
    );
  }

  // قالب علم اجتماع التربية
  static MindMapTemplate _createSociologyEducationTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socedu_root',
        title: 'علم اجتماع التربية',
        description: 'دراسة التعليم والمؤسسات التعليمية',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socedu_school',
        title: 'المدرسة كمؤسسة',
        description: 'البناء الاجتماعي للمدرسة',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socedu_inequality',
        title: 'عدم المساواة التعليمية',
        description: 'الفجوات في التعليم',
        position: const Offset(1300, 300),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socedu_curriculum',
        title: 'المنهج الخفي',
        description: 'القيم والمعايير المضمرة',
        position: const Offset(600, 600),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socedu_teacher',
        title: 'دور المعلم',
        description: 'المعلم كعامل اجتماعي',
        position: const Offset(1400, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socedu_mobility',
        title: 'الحراك الاجتماعي',
        description: 'التعليم والتنقل الطبقي',
        position: const Offset(1000, 750),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socedu_culture',
        title: 'الثقافة المدرسية',
        description: 'القيم والتقاليد التعليمية',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socedu_conn1',
        fromNodeId: 'socedu_root',
        toNodeId: 'socedu_school',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socedu_conn2',
        fromNodeId: 'socedu_root',
        toNodeId: 'socedu_inequality',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socedu_conn3',
        fromNodeId: 'socedu_root',
        toNodeId: 'socedu_curriculum',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socedu_conn4',
        fromNodeId: 'socedu_root',
        toNodeId: 'socedu_teacher',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socedu_conn5',
        fromNodeId: 'socedu_root',
        toNodeId: 'socedu_mobility',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socedu_conn6',
        fromNodeId: 'socedu_root',
        toNodeId: 'socedu_culture',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم اجتماع التربية',
      description: 'دراسة التعليم والمؤسسات التعليمية اجتماعياً',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socedu_root',
      createdAt: DateTime.now(),
      tags: ['تربية', 'تعليم', 'مدرسة', 'حراك'],
    );
  }

  // قالب علم اجتماع الجريمة
  static MindMapTemplate _createSociologyCrimeTemplate() {
    final nodes = [
      TemplateNode(
        id: 'soccrime_root',
        title: 'علم اجتماع الجريمة',
        description: 'دراسة الجريمة والانحراف الاجتماعي',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'soccrime_theories',
        title: 'نظريات الجريمة',
        description: 'النظريات المفسرة للسلوك الإجرامي',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soccrime_deviance',
        title: 'الانحراف الاجتماعي',
        description: 'السلوك المخالف للمعايير',
        position: const Offset(1300, 300),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soccrime_control',
        title: 'الضبط الاجتماعي',
        description: 'آليات منع الجريمة',
        position: const Offset(600, 600),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soccrime_justice',
        title: 'العدالة الجنائية',
        description: 'نظام القضاء والعقاب',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soccrime_victims',
        title: 'ضحايا الجريمة',
        description: 'دراسة المتضررين',
        position: const Offset(1000, 750),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'soccrime_prevention',
        title: 'الوقاية من الجريمة',
        description: 'استراتيجيات المنع',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'soccrime_conn1',
        fromNodeId: 'soccrime_root',
        toNodeId: 'soccrime_theories',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soccrime_conn2',
        fromNodeId: 'soccrime_root',
        toNodeId: 'soccrime_deviance',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soccrime_conn3',
        fromNodeId: 'soccrime_root',
        toNodeId: 'soccrime_control',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soccrime_conn4',
        fromNodeId: 'soccrime_root',
        toNodeId: 'soccrime_justice',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soccrime_conn5',
        fromNodeId: 'soccrime_root',
        toNodeId: 'soccrime_victims',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'soccrime_conn6',
        fromNodeId: 'soccrime_root',
        toNodeId: 'soccrime_prevention',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم اجتماع الجريمة',
      description: 'دراسة الجريمة والانحراف والعدالة الجنائية',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'soccrime_root',
      createdAt: DateTime.now(),
      tags: ['جريمة', 'انحراف', 'عدالة', 'ضبط'],
    );
  }

  // قالب علم الاجتماع السياسي
  static MindMapTemplate _createSociologyPoliticalTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socpol_root',
        title: 'علم الاجتماع السياسي',
        description: 'دراسة السلطة والسياسة في المجتمع',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socpol_power',
        title: 'السلطة',
        description: 'مفاهيم وأشكال السلطة',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socpol_state',
        title: 'الدولة',
        description: 'نظريات الدولة والحكم',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socpol_democracy',
        title: 'الديمقراطية',
        description: 'المشاركة السياسية',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socpol_movements',
        title: 'الحركات الاجتماعية',
        description: 'التغيير والاحتجاج',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socpol_ideology',
        title: 'الأيديولوجيا',
        description: 'الأفكار والمعتقدات السياسية',
        position: const Offset(1000, 750),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socpol_elite',
        title: 'النخبة السياسية',
        description: 'الطبقة الحاكمة',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socpol_conn1',
        fromNodeId: 'socpol_root',
        toNodeId: 'socpol_power',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socpol_conn2',
        fromNodeId: 'socpol_root',
        toNodeId: 'socpol_state',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socpol_conn3',
        fromNodeId: 'socpol_root',
        toNodeId: 'socpol_democracy',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socpol_conn4',
        fromNodeId: 'socpol_root',
        toNodeId: 'socpol_movements',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socpol_conn5',
        fromNodeId: 'socpol_root',
        toNodeId: 'socpol_ideology',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socpol_conn6',
        fromNodeId: 'socpol_root',
        toNodeId: 'socpol_elite',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم الاجتماع السياسي',
      description: 'دراسة السلطة والسياسة والحكم في المجتمع',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socpol_root',
      createdAt: DateTime.now(),
      tags: ['سياسة', 'سلطة', 'دولة', 'ديمقراطية'],
    );
  }

  // قالب علم اجتماع الدين
  static MindMapTemplate _createSociologyReligionTemplate() {
    final nodes = [
      TemplateNode(
        id: 'socrel_root',
        title: 'علم اجتماع الدين',
        description: 'دراسة الدين والمعتقدات في المجتمع',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'socrel_functions',
        title: 'وظائف الدين',
        description: 'الأدوار الاجتماعية للدين',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrel_institutions',
        title: 'المؤسسات الدينية',
        description: 'التنظيم الديني',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrel_secularization',
        title: 'العلمنة',
        description: 'تراجع الدين في المجتمع',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrel_fundamentalism',
        title: 'الأصولية الدينية',
        description: 'التطرف والتشدد',
        position: const Offset(1400, 600),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrel_ritual',
        title: 'الطقوس الدينية',
        description: 'الممارسات والشعائر',
        position: const Offset(1000, 750),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'socrel_diversity',
        title: 'التنوع الديني',
        description: 'التعددية والتسامح',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'socrel_conn1',
        fromNodeId: 'socrel_root',
        toNodeId: 'socrel_functions',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrel_conn2',
        fromNodeId: 'socrel_root',
        toNodeId: 'socrel_institutions',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrel_conn3',
        fromNodeId: 'socrel_root',
        toNodeId: 'socrel_secularization',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrel_conn4',
        fromNodeId: 'socrel_root',
        toNodeId: 'socrel_fundamentalism',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrel_conn5',
        fromNodeId: 'socrel_root',
        toNodeId: 'socrel_ritual',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'socrel_conn6',
        fromNodeId: 'socrel_root',
        toNodeId: 'socrel_diversity',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'علم اجتماع الدين',
      description: 'دراسة الدين والمعتقدات والممارسات الدينية',
      category: TemplateCategory.sociology,
      subject: 'علم الاجتماع',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'socrel_root',
      createdAt: DateTime.now(),
      tags: ['دين', 'معتقدات', 'طقوس', 'علمنة'],
    );
  }

  // قالب نظريات التنظيم
  static MindMapTemplate _createOrganizationalTheoriesTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orgtheory_root',
        title: 'نظريات التنظيم',
        description: 'النظريات الأساسية في علم التنظيم',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orgtheory_classical',
        title: 'النظرية الكلاسيكية',
        description: 'تايلور وفايول وويبر',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgtheory_human_relations',
        title: 'نظرية العلاقات الإنسانية',
        description: 'مايو وهوثورن',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgtheory_systems',
        title: 'نظرية النظم',
        description: 'المنظمة كنظام مفتوح',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgtheory_contingency',
        title: 'النظرية الموقفية',
        description: 'التكيف مع البيئة',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgtheory_institutional',
        title: 'النظرية المؤسسية',
        description: 'التشابه والشرعية',
        position: const Offset(1000, 750),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgtheory_resource',
        title: 'نظرية الموارد',
        description: 'الاعتماد على الموارد',
        position: const Offset(1000, 200),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orgtheory_conn1',
        fromNodeId: 'orgtheory_root',
        toNodeId: 'orgtheory_classical',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgtheory_conn2',
        fromNodeId: 'orgtheory_root',
        toNodeId: 'orgtheory_human_relations',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgtheory_conn3',
        fromNodeId: 'orgtheory_root',
        toNodeId: 'orgtheory_systems',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgtheory_conn4',
        fromNodeId: 'orgtheory_root',
        toNodeId: 'orgtheory_contingency',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgtheory_conn5',
        fromNodeId: 'orgtheory_root',
        toNodeId: 'orgtheory_institutional',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgtheory_conn6',
        fromNodeId: 'orgtheory_root',
        toNodeId: 'orgtheory_resource',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'نظريات التنظيم',
      description: 'النظريات الأساسية في دراسة المنظمات',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orgtheory_root',
      createdAt: DateTime.now(),
      tags: ['نظريات', 'تنظيم', 'إدارة', 'كلاسيكية'],
    );
  }

  // قالب السلوك التنظيمي
  static MindMapTemplate _createOrganizationalBehaviorTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orgbeh_root',
        title: 'السلوك التنظيمي',
        description: 'دراسة سلوك الأفراد والجماعات في المنظمات',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orgbeh_individual',
        title: 'السلوك الفردي',
        description: 'الشخصية والاتجاهات والإدراك',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgbeh_group',
        title: 'سلوك الجماعة',
        description: 'ديناميكية الجماعات وفرق العمل',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgbeh_motivation',
        title: 'الدافعية',
        description: 'نظريات التحفيز والرضا الوظيفي',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgbeh_stress',
        title: 'ضغوط العمل',
        description: 'مصادر وإدارة الضغوط',
        position: const Offset(1400, 600),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgbeh_learning',
        title: 'التعلم التنظيمي',
        description: 'اكتساب المهارات والمعرفة',
        position: const Offset(1000, 750),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgbeh_decision',
        title: 'اتخاذ القرارات',
        description: 'عمليات وأساليب القرار',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orgbeh_conn1',
        fromNodeId: 'orgbeh_root',
        toNodeId: 'orgbeh_individual',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgbeh_conn2',
        fromNodeId: 'orgbeh_root',
        toNodeId: 'orgbeh_group',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgbeh_conn3',
        fromNodeId: 'orgbeh_root',
        toNodeId: 'orgbeh_motivation',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgbeh_conn4',
        fromNodeId: 'orgbeh_root',
        toNodeId: 'orgbeh_stress',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgbeh_conn5',
        fromNodeId: 'orgbeh_root',
        toNodeId: 'orgbeh_learning',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgbeh_conn6',
        fromNodeId: 'orgbeh_root',
        toNodeId: 'orgbeh_decision',
        label: 'يدرس',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'السلوك التنظيمي',
      description: 'دراسة سلوك الأفراد والجماعات في بيئة العمل',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orgbeh_root',
      createdAt: DateTime.now(),
      tags: ['سلوك', 'تنظيمي', 'دافعية', 'جماعات'],
    );
  }

  // قالب إدارة الموارد البشرية
  static MindMapTemplate _createHumanResourcesTemplate() {
    final nodes = [
      TemplateNode(
        id: 'hr_root',
        title: 'إدارة الموارد البشرية',
        description: 'إدارة وتطوير العنصر البشري',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'hr_recruitment',
        title: 'التوظيف والاختيار',
        description: 'جذب واختيار المواهب',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hr_training',
        title: 'التدريب والتطوير',
        description: 'تنمية المهارات والقدرات',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hr_performance',
        title: 'تقييم الأداء',
        description: 'قياس وتحسين الأداء',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hr_compensation',
        title: 'التعويضات والمزايا',
        description: 'أنظمة الأجور والحوافز',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hr_relations',
        title: 'علاقات العمل',
        description: 'العلاقة بين الإدارة والعمال',
        position: const Offset(1000, 750),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'hr_planning',
        title: 'تخطيط الموارد البشرية',
        description: 'التنبؤ بالاحتياجات المستقبلية',
        position: const Offset(1000, 200),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'hr_conn1',
        fromNodeId: 'hr_root',
        toNodeId: 'hr_recruitment',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hr_conn2',
        fromNodeId: 'hr_root',
        toNodeId: 'hr_training',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hr_conn3',
        fromNodeId: 'hr_root',
        toNodeId: 'hr_performance',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hr_conn4',
        fromNodeId: 'hr_root',
        toNodeId: 'hr_compensation',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hr_conn5',
        fromNodeId: 'hr_root',
        toNodeId: 'hr_relations',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'hr_conn6',
        fromNodeId: 'hr_root',
        toNodeId: 'hr_planning',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'إدارة الموارد البشرية',
      description: 'إدارة وتطوير العنصر البشري في المنظمات',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'hr_root',
      createdAt: DateTime.now(),
      tags: ['موارد بشرية', 'توظيف', 'تدريب', 'أداء'],
    );
  }

  // قالب الثقافة التنظيمية
  static MindMapTemplate _createOrganizationalCultureTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orgcult_root',
        title: 'الثقافة التنظيمية',
        description: 'القيم والمعتقدات والممارسات المشتركة',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orgcult_values',
        title: 'القيم التنظيمية',
        description: 'المبادئ الأساسية للمنظمة',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcult_norms',
        title: 'المعايير والقواعد',
        description: 'السلوكيات المقبولة',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcult_symbols',
        title: 'الرموز والطقوس',
        description: 'التعبيرات الثقافية المرئية',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcult_language',
        title: 'اللغة التنظيمية',
        description: 'المصطلحات والتعبيرات الخاصة',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcult_change',
        title: 'تغيير الثقافة',
        description: 'استراتيجيات التطوير الثقافي',
        position: const Offset(1000, 750),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcult_types',
        title: 'أنواع الثقافات',
        description: 'الثقافة الهرمية والمرنة والسوقية',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orgcult_conn1',
        fromNodeId: 'orgcult_root',
        toNodeId: 'orgcult_values',
        label: 'تتضمن',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcult_conn2',
        fromNodeId: 'orgcult_root',
        toNodeId: 'orgcult_norms',
        label: 'تتضمن',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcult_conn3',
        fromNodeId: 'orgcult_root',
        toNodeId: 'orgcult_symbols',
        label: 'تتضمن',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcult_conn4',
        fromNodeId: 'orgcult_root',
        toNodeId: 'orgcult_language',
        label: 'تتضمن',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcult_conn5',
        fromNodeId: 'orgcult_root',
        toNodeId: 'orgcult_change',
        label: 'تخضع لـ',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcult_conn6',
        fromNodeId: 'orgcult_root',
        toNodeId: 'orgcult_types',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الثقافة التنظيمية',
      description: 'القيم والمعتقدات والممارسات في المنظمات',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orgcult_root',
      createdAt: DateTime.now(),
      tags: ['ثقافة', 'قيم', 'معايير', 'رموز'],
    );
  }

  // قالب القيادة التنظيمية
  static MindMapTemplate _createOrganizationalLeadershipTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orglead_root',
        title: 'القيادة التنظيمية',
        description: 'أنماط وأساليب القيادة في المنظمات',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orglead_autocratic',
        title: 'القيادة الأوتوقراطية',
        description: 'السلطة المركزية والتحكم',
        position: const Offset(700, 300),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orglead_democratic',
        title: 'القيادة الديمقراطية',
        description: 'المشاركة في اتخاذ القرارات',
        position: const Offset(1300, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orglead_transformational',
        title: 'القيادة التحويلية',
        description: 'الإلهام والتحفيز للتغيير',
        position: const Offset(600, 600),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orglead_transactional',
        title: 'القيادة التبادلية',
        description: 'المكافآت والعقوبات',
        position: const Offset(1400, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orglead_servant',
        title: 'القيادة الخادمة',
        description: 'خدمة الآخرين والتطوير',
        position: const Offset(1000, 750),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orglead_situational',
        title: 'القيادة الموقفية',
        description: 'التكيف مع الظروف',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orglead_conn1',
        fromNodeId: 'orglead_root',
        toNodeId: 'orglead_autocratic',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orglead_conn2',
        fromNodeId: 'orglead_root',
        toNodeId: 'orglead_democratic',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orglead_conn3',
        fromNodeId: 'orglead_root',
        toNodeId: 'orglead_transformational',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orglead_conn4',
        fromNodeId: 'orglead_root',
        toNodeId: 'orglead_transactional',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orglead_conn5',
        fromNodeId: 'orglead_root',
        toNodeId: 'orglead_servant',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orglead_conn6',
        fromNodeId: 'orglead_root',
        toNodeId: 'orglead_situational',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'القيادة التنظيمية',
      description: 'أنماط وأساليب القيادة في بيئة العمل',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orglead_root',
      createdAt: DateTime.now(),
      tags: ['قيادة', 'إدارة', 'تحويلية', 'ديمقراطية'],
    );
  }

  // قالب الاتصال التنظيمي
  static MindMapTemplate _createOrganizationalCommunicationTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orgcomm_root',
        title: 'الاتصال التنظيمي',
        description: 'شبكات وأنماط الاتصال في المنظمات',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orgcomm_formal',
        title: 'الاتصال الرسمي',
        description: 'القنوات الرسمية للمعلومات',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcomm_informal',
        title: 'الاتصال غير الرسمي',
        description: 'الشبكات الاجتماعية والإشاعات',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcomm_vertical',
        title: 'الاتصال العمودي',
        description: 'من الأعلى للأسفل والعكس',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcomm_horizontal',
        title: 'الاتصال الأفقي',
        description: 'بين الأقسام والزملاء',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcomm_barriers',
        title: 'معوقات الاتصال',
        description: 'الحواجز والتشويش',
        position: const Offset(1000, 750),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgcomm_technology',
        title: 'تكنولوجيا الاتصال',
        description: 'الوسائل الرقمية والحديثة',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orgcomm_conn1',
        fromNodeId: 'orgcomm_root',
        toNodeId: 'orgcomm_formal',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcomm_conn2',
        fromNodeId: 'orgcomm_root',
        toNodeId: 'orgcomm_informal',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcomm_conn3',
        fromNodeId: 'orgcomm_root',
        toNodeId: 'orgcomm_vertical',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcomm_conn4',
        fromNodeId: 'orgcomm_root',
        toNodeId: 'orgcomm_horizontal',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcomm_conn5',
        fromNodeId: 'orgcomm_root',
        toNodeId: 'orgcomm_barriers',
        label: 'يواجه',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgcomm_conn6',
        fromNodeId: 'orgcomm_root',
        toNodeId: 'orgcomm_technology',
        label: 'يستخدم',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الاتصال التنظيمي',
      description: 'شبكات وأنماط الاتصال داخل المنظمات',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orgcomm_root',
      createdAt: DateTime.now(),
      tags: ['اتصال', 'شبكات', 'معلومات', 'تكنولوجيا'],
    );
  }

  // قالب الصراع التنظيمي
  static MindMapTemplate _createOrganizationalConflictTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orgconf_root',
        title: 'الصراع التنظيمي',
        description: 'أنواع ومصادر وإدارة الصراعات',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orgconf_sources',
        title: 'مصادر الصراع',
        description: 'الموارد والأهداف والقيم',
        position: const Offset(700, 300),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgconf_types',
        title: 'أنواع الصراع',
        description: 'فردي وجماعي وتنظيمي',
        position: const Offset(1300, 300),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgconf_levels',
        title: 'مستويات الصراع',
        description: 'داخلي وبين الأفراد والجماعات',
        position: const Offset(600, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgconf_management',
        title: 'إدارة الصراع',
        description: 'استراتيجيات الحل والوساطة',
        position: const Offset(1400, 600),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgconf_effects',
        title: 'آثار الصراع',
        description: 'الإيجابية والسلبية',
        position: const Offset(1000, 750),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgconf_prevention',
        title: 'منع الصراع',
        description: 'الوقاية والتدخل المبكر',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orgconf_conn1',
        fromNodeId: 'orgconf_root',
        toNodeId: 'orgconf_sources',
        label: 'ينشأ من',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgconf_conn2',
        fromNodeId: 'orgconf_root',
        toNodeId: 'orgconf_types',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgconf_conn3',
        fromNodeId: 'orgconf_root',
        toNodeId: 'orgconf_levels',
        label: 'يحدث على',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgconf_conn4',
        fromNodeId: 'orgconf_root',
        toNodeId: 'orgconf_management',
        label: 'يتطلب',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgconf_conn5',
        fromNodeId: 'orgconf_root',
        toNodeId: 'orgconf_effects',
        label: 'ينتج عنه',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgconf_conn6',
        fromNodeId: 'orgconf_root',
        toNodeId: 'orgconf_prevention',
        label: 'يمكن',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الصراع التنظيمي',
      description: 'أنواع ومصادر وإدارة الصراعات في المنظمات',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orgconf_root',
      createdAt: DateTime.now(),
      tags: ['صراع', 'نزاع', 'إدارة', 'حلول'],
    );
  }

  // قالب التغيير التنظيمي
  static MindMapTemplate _createOrganizationalChangeTemplate() {
    final nodes = [
      TemplateNode(
        id: 'orgchange_root',
        title: 'التغيير التنظيمي',
        description: 'عمليات وإدارة التغيير في المنظمات',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'orgchange_drivers',
        title: 'محركات التغيير',
        description: 'العوامل الداخلية والخارجية',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgchange_types',
        title: 'أنواع التغيير',
        description: 'تدريجي وجذري ومخطط',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgchange_resistance',
        title: 'مقاومة التغيير',
        description: 'أسباب وأشكال المقاومة',
        position: const Offset(600, 600),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgchange_strategies',
        title: 'استراتيجيات التغيير',
        description: 'التخطيط والتنفيذ والمتابعة',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgchange_leadership',
        title: 'قيادة التغيير',
        description: 'دور القادة في التغيير',
        position: const Offset(1000, 750),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'orgchange_culture',
        title: 'التغيير الثقافي',
        description: 'تطوير القيم والسلوكيات',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'orgchange_conn1',
        fromNodeId: 'orgchange_root',
        toNodeId: 'orgchange_drivers',
        label: 'يحركه',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgchange_conn2',
        fromNodeId: 'orgchange_root',
        toNodeId: 'orgchange_types',
        label: 'يشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgchange_conn3',
        fromNodeId: 'orgchange_root',
        toNodeId: 'orgchange_resistance',
        label: 'يواجه',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgchange_conn4',
        fromNodeId: 'orgchange_root',
        toNodeId: 'orgchange_strategies',
        label: 'يتطلب',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgchange_conn5',
        fromNodeId: 'orgchange_root',
        toNodeId: 'orgchange_leadership',
        label: 'يحتاج',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'orgchange_conn6',
        fromNodeId: 'orgchange_root',
        toNodeId: 'orgchange_culture',
        label: 'يتضمن',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'التغيير التنظيمي',
      description: 'عمليات وإدارة التغيير في المنظمات',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'orgchange_root',
      createdAt: DateTime.now(),
      tags: ['تغيير', 'تطوير', 'مقاومة', 'استراتيجية'],
    );
  }

  // قالب الأداء والإنتاجية
  static MindMapTemplate _createWorkPerformanceTemplate() {
    final nodes = [
      TemplateNode(
        id: 'workperf_root',
        title: 'الأداء والإنتاجية',
        description: 'قياس وتحسين الأداء في العمل',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'workperf_measurement',
        title: 'قياس الأداء',
        description: 'المؤشرات والمعايير',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workperf_factors',
        title: 'عوامل الأداء',
        description: 'القدرة والدافعية والبيئة',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workperf_evaluation',
        title: 'تقييم الأداء',
        description: 'أساليب وطرق التقييم',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workperf_improvement',
        title: 'تحسين الأداء',
        description: 'استراتيجيات التطوير',
        position: const Offset(1400, 600),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workperf_productivity',
        title: 'الإنتاجية',
        description: 'العلاقة بين المدخلات والمخرجات',
        position: const Offset(1000, 750),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workperf_feedback',
        title: 'التغذية الراجعة',
        description: 'المراجعة والتوجيه',
        position: const Offset(1000, 200),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'workperf_conn1',
        fromNodeId: 'workperf_root',
        toNodeId: 'workperf_measurement',
        label: 'يتطلب',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workperf_conn2',
        fromNodeId: 'workperf_root',
        toNodeId: 'workperf_factors',
        label: 'يتأثر بـ',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workperf_conn3',
        fromNodeId: 'workperf_root',
        toNodeId: 'workperf_evaluation',
        label: 'يخضع لـ',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workperf_conn4',
        fromNodeId: 'workperf_root',
        toNodeId: 'workperf_improvement',
        label: 'يحتاج',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workperf_conn5',
        fromNodeId: 'workperf_root',
        toNodeId: 'workperf_productivity',
        label: 'يرتبط بـ',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workperf_conn6',
        fromNodeId: 'workperf_root',
        toNodeId: 'workperf_feedback',
        label: 'يستفيد من',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'الأداء والإنتاجية',
      description: 'قياس وتحسين الأداء والإنتاجية في العمل',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'workperf_root',
      createdAt: DateTime.now(),
      tags: ['أداء', 'إنتاجية', 'تقييم', 'تحسين'],
    );
  }

  // قالب بيئة العمل
  static MindMapTemplate _createWorkEnvironmentTemplate() {
    final nodes = [
      TemplateNode(
        id: 'workenv_root',
        title: 'بيئة العمل',
        description: 'العوامل المؤثرة في بيئة العمل',
        position: const Offset(1000, 500),
        color: const Color(0xFFFF5722),
        shape: NodeShape.circle,
        fontSize: 16.0,
        isRoot: true,
      ),
      TemplateNode(
        id: 'workenv_physical',
        title: 'البيئة المادية',
        description: 'المكان والإضاءة والتهوية',
        position: const Offset(700, 300),
        color: Colors.blue,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workenv_social',
        title: 'البيئة الاجتماعية',
        description: 'العلاقات والتفاعل',
        position: const Offset(1300, 300),
        color: Colors.green,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workenv_psychological',
        title: 'البيئة النفسية',
        description: 'الضغوط والرضا الوظيفي',
        position: const Offset(600, 600),
        color: Colors.orange,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workenv_safety',
        title: 'الأمان والسلامة',
        description: 'إجراءات الحماية والوقاية',
        position: const Offset(1400, 600),
        color: Colors.red,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workenv_technology',
        title: 'التكنولوجيا',
        description: 'الأدوات والأنظمة الرقمية',
        position: const Offset(1000, 750),
        color: Colors.purple,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
      TemplateNode(
        id: 'workenv_flexibility',
        title: 'المرونة',
        description: 'ساعات العمل والعمل عن بعد',
        position: const Offset(1000, 200),
        color: Colors.teal,
        shape: NodeShape.rectangle,
        fontSize: 14.0,
      ),
    ];

    final connections = [
      TemplateConnection(
        id: 'workenv_conn1',
        fromNodeId: 'workenv_root',
        toNodeId: 'workenv_physical',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workenv_conn2',
        fromNodeId: 'workenv_root',
        toNodeId: 'workenv_social',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workenv_conn3',
        fromNodeId: 'workenv_root',
        toNodeId: 'workenv_psychological',
        label: 'تشمل',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workenv_conn4',
        fromNodeId: 'workenv_root',
        toNodeId: 'workenv_safety',
        label: 'تتطلب',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workenv_conn5',
        fromNodeId: 'workenv_root',
        toNodeId: 'workenv_technology',
        label: 'تستخدم',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
      TemplateConnection(
        id: 'workenv_conn6',
        fromNodeId: 'workenv_root',
        toNodeId: 'workenv_flexibility',
        label: 'توفر',
        color: const Color(0xFFFF5722),
        style: ConnectionStyle.solid,
      ),
    ];

    return MindMapTemplate(
      id: _uuid.v4(),
      name: 'بيئة العمل',
      description: 'العوامل المؤثرة في بيئة العمل والإنتاجية',
      category: TemplateCategory.sociology,
      subject: 'علم اجتماع التنظيم',
      nodes: nodes,
      connections: connections,
      rootNodeId: 'workenv_root',
      createdAt: DateTime.now(),
      tags: ['بيئة عمل', 'أمان', 'مرونة', 'تكنولوجيا'],
    );
  }
}
