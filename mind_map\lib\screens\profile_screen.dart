import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_database/firebase_database.dart';
import '../providers/auth_provider.dart';
import '../providers/mind_map_provider.dart';
import '../providers/posts_provider.dart';
import '../screens/user_profile_screen.dart';
import '../models/user_model.dart';
import '../models/post.dart';
import '../services/profile_visit_service.dart';
import '../widgets/post_card.dart';


class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث آخر نشاط وإعادة تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.updateLastActive();

      // إعادة تحميل بيانات المستخدم إذا لم تكن موجودة
      if (authProvider.user != null && authProvider.userModel == null) {
        print('🔄 إعادة تحميل بيانات المستخدم في البروفايل...');
        authProvider.reloadUserData();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              final user = authProvider.user;
              final userModel = authProvider.userModel;
              final isAnonymous = user?.isAnonymous ?? false;

              // Debug information
              print('🔍 Profile Screen Debug:');
              print('   User: ${user?.uid}');
              print('   UserModel: ${userModel?.email}');
              print('   IsAnonymous: $isAnonymous');
              print('   IsLoading: ${authProvider.isLoading}');

              return _buildResponsiveLayout(
                constraints: constraints,
                isAnonymous: isAnonymous,
                userModel: userModel,
                user: user,
                authProvider: authProvider,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildResponsiveLayout({
    required BoxConstraints constraints,
    required bool isAnonymous,
    required dynamic userModel,
    required dynamic user,
    required AuthProvider authProvider,
  }) {
    final isTablet = constraints.maxWidth > 768;
    final isDesktop = constraints.maxWidth > 1200;

    if (isDesktop) {
      return _buildDesktopLayout(constraints, isAnonymous, userModel, user, authProvider);
    } else if (isTablet) {
      return _buildTabletLayout(constraints, isAnonymous, userModel, user, authProvider);
    } else {
      return _buildMobileLayout(constraints, isAnonymous, userModel, user, authProvider);
    }
  }

  // تخطيط الهاتف المحمول
  Widget _buildMobileLayout(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return RefreshIndicator(
      onRefresh: () => _refreshProfileData(authProvider),
      color: Colors.purple.shade600,
      backgroundColor: Colors.white,
      child: CustomScrollView(
        slivers: [
          _buildResponsiveAppBar(constraints, isAnonymous, userModel: userModel),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(_getHorizontalPadding(constraints)),
              child: Column(
                children: _buildProfileContent(constraints, isAnonymous, userModel, user, authProvider),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // تخطيط الجهاز اللوحي
  Widget _buildTabletLayout(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return RefreshIndicator(
      onRefresh: () => _refreshProfileData(authProvider),
      color: Colors.purple.shade600,
      backgroundColor: Colors.white,
      child: CustomScrollView(
        slivers: [
          _buildResponsiveAppBar(constraints, isAnonymous, userModel: userModel),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getHorizontalPadding(constraints),
                vertical: 24,
              ),
              child: _buildTabletGrid(constraints, isAnonymous, userModel, user, authProvider),
            ),
          ),
        ],
      ),
    );
  }

  // تخطيط سطح المكتب
  Widget _buildDesktopLayout(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return Row(
      children: [
        // الشريط الجانبي للمعلومات الأساسية
        Container(
          width: 350,
          color: Colors.white,
          child: Column(
            children: [
              _buildResponsiveAppBar(constraints, isAnonymous, isDesktop: true, userModel: userModel),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      _buildMainInfoCard(isAnonymous, userModel, user),
                      const SizedBox(height: 16),
                      if (isAnonymous)
                        _buildGuestOptionsCard(context),
                      const Spacer(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // المحتوى الرئيسي
        Expanded(
          child: RefreshIndicator(
            onRefresh: () => _refreshProfileData(authProvider),
            color: Colors.purple.shade600,
            backgroundColor: Colors.white,
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: _buildDesktopGrid(constraints, isAnonymous, userModel, user, authProvider),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // حساب المسافة الأفقية حسب حجم الشاشة
  double _getHorizontalPadding(BoxConstraints constraints) {
    if (constraints.maxWidth > 1200) {
      return constraints.maxWidth * 0.1; // 10% من عرض الشاشة للشاشات الكبيرة
    } else if (constraints.maxWidth > 768) {
      return 32.0; // مسافة ثابتة للأجهزة اللوحية
    } else {
      return 16.0; // مسافة صغيرة للهواتف
    }
  }

  // بناء شريط التطبيق المتجاوب
  Widget _buildResponsiveAppBar(BoxConstraints constraints, bool isAnonymous, {bool isDesktop = false, dynamic userModel}) {
    double expandedHeight = isDesktop ? 120 : (constraints.maxWidth > 768 ? 200 : 180);
    double avatarRadius = isDesktop ? 35 : (constraints.maxWidth > 768 ? 50 : 45);
    double iconSize = isDesktop ? 40 : (constraints.maxWidth > 768 ? 55 : 50);

    if (isDesktop) {
      return Container(
        height: expandedHeight,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade500,
              Colors.blue.shade700,
              Colors.blue.shade900,
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: avatarRadius,
                    backgroundColor: Colors.white,
                    child: Icon(
                      isAnonymous ? Icons.person_outline : Icons.person,
                      size: iconSize,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
                // مؤشر حالة النشاط للمستخدمين المسجلين (المستخدم يرى حالته دائماً)
                if (!isAnonymous && userModel != null)
                  Positioned(
                    bottom: 4,
                    right: 4,
                    child: Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: userModel.isActive ? Colors.green : Colors.grey,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 3,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    }

    return SliverAppBar(
      expandedHeight: expandedHeight,
      floating: false,
      pinned: true,
      elevation: 0,
      automaticallyImplyLeading: false, // إزالة سهم الرجوع
      title: const Text(
        'الملف الشخصي',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      backgroundColor: Colors.blue.shade700,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade500,
                Colors.blue.shade700,
                Colors.blue.shade900,
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.shade200.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const SizedBox(height: 60),
                Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 8),
                          ),
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.1),
                            blurRadius: 5,
                            offset: const Offset(0, -2),
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: avatarRadius,
                        backgroundColor: Colors.white,
                        child: Icon(
                          isAnonymous ? Icons.person_outline : Icons.person,
                          size: iconSize,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                    // مؤشر حالة النشاط للمستخدمين المسجلين (المستخدم يرى حالته دائماً)
                    if (!isAnonymous && userModel != null)
                      Positioned(
                        bottom: 4,
                        right: 4,
                        child: Container(
                          width: 18,
                          height: 18,
                          decoration: BoxDecoration(
                            color: userModel.isActive ? Colors.green : Colors.grey,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 3,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء محتوى البروفايل للهاتف المحمول
  List<Widget> _buildProfileContent(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    List<Widget> widgets = [
      _buildMainInfoCard(isAnonymous, userModel, user),
      const SizedBox(height: 16),
    ];

    if (!isAnonymous && userModel != null) {
      widgets.addAll([
        _buildAcademicInfoCard(userModel),
        const SizedBox(height: 16),
        _buildPersonalInfoCard(userModel),
        const SizedBox(height: 16),
        _buildProjectsStatsCard(context),
        const SizedBox(height: 16),
        _buildUserPostsCard(context, userModel),
        const SizedBox(height: 16),
      ]);
    }

    if (isAnonymous) {
      widgets.add(_buildGuestOptionsCard(context));
    }

    widgets.addAll([
      const SizedBox(height: 32),
    ]);

    return widgets;
  }

  // بناء شبكة للجهاز اللوحي
  Widget _buildTabletGrid(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return Column(
      children: [
        // الصف الأول: المعلومات الأساسية والأكاديمية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildMainInfoCard(isAnonymous, userModel, user),
            ),
            const SizedBox(width: 16),
            if (!isAnonymous && userModel != null)
              Expanded(
                child: _buildAcademicInfoCard(userModel),
              ),
          ],
        ),

        if (!isAnonymous && userModel != null) ...[
          const SizedBox(height: 16),
          // الصف الثاني: المعلومات الشخصية والإحصائيات
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildPersonalInfoCard(userModel),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildProjectsStatsCard(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // منشورات المستخدم
          _buildUserPostsCard(context, userModel),
        ],

        if (isAnonymous) ...[
          const SizedBox(height: 16),
          _buildGuestOptionsCard(context),
        ],
      ],
    );
  }

  // بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    if (isAnonymous) {
      return Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: _buildGuestOptionsCard(context),
        ),
      );
    }

    if (userModel == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      children: [
        // الصف الأول: المعلومات الأكاديمية والشخصية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildAcademicInfoCard(userModel),
            ),
            const SizedBox(width: 24),
            Expanded(
              child: _buildPersonalInfoCard(userModel),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // الصف الثاني: إحصائيات المشاريع
        _buildProjectsStatsCard(context),

        const SizedBox(height: 24),

        // منشورات المستخدم
        _buildUserPostsCard(context, userModel),
      ],
    );
  }

  // بطاقة المعلومات الأساسية
  Widget _buildMainInfoCard(bool isAnonymous, userModel, user) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.blue.shade50,
            ],
          ),
        ),
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            // اسم المستخدم
            Text(
              isAnonymous
                  ? 'مستخدم ضيف'
                  : (userModel?.fullName ?? user?.email ?? 'مستخدم'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),

            // البريد الإلكتروني (حسب إعدادات الخصوصية)
            if (isAnonymous || (userModel?.showEmail ?? false))
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.email, size: 16, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Text(
                      isAnonymous
                          ? 'تم تسجيل الدخول كضيف'
                          : (user?.email ?? 'مستخدم مسجل'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            // إحصائيات المتابعة (حسب إعدادات الخصوصية)
            if (!isAnonymous && userModel != null) ...[
              // المستخدم يرى إحصائياته دائماً، لذا نعرض الإحصائيات
              Builder(
                builder: (context) {
                  final canViewFollowers = userModel.canViewFollowers(userModel.uid);
                  final canViewFollowing = userModel.canViewFollowing(userModel.uid);

                  if (!canViewFollowers && !canViewFollowing) {
                    return const SizedBox.shrink();
                  }

                  return Column(
                    children: [
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          children: [
                            // عرض المتابعين فقط إذا كان مسموحاً
                            if (canViewFollowers) ...[
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => _showFollowersList(context, userModel),
                                  child: _buildFollowStatItem(
                                    icon: Icons.people,
                                    count: userModel.followersCount,
                                    label: 'متابع',
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                              // فاصل بين المتابعين والمتابَعين
                              Container(
                                width: 1,
                                height: 50,
                                color: Colors.grey.shade300,
                                margin: const EdgeInsets.symmetric(horizontal: 16),
                              ),
                            ],
                            // عرض المتابَعين فقط إذا كان مسموحاً
                            if (canViewFollowing) ...[
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => _showFollowingList(context, userModel),
                                  child: _buildFollowStatItem(
                                    icon: Icons.person_add,
                                    count: userModel.followingCount,
                                    label: 'متابَع',
                                    color: Colors.green,
                                  ),
                                ),
                              ),
                              // فاصل قبل عدد الزوار
                              Container(
                                width: 1,
                                height: 50,
                                color: Colors.grey.shade300,
                                margin: const EdgeInsets.symmetric(horizontal: 16),
                              ),
                            ],
                            // عرض عدد زوار الملف الشخصي
                            Expanded(
                              child: FutureBuilder<int>(
                                future: ProfileVisitService.getProfileVisitorsCount(userModel.uid),
                                builder: (context, snapshot) {
                                  final visitorsCount = snapshot.data ?? 0;
                                  return GestureDetector(
                                    onTap: () => _showProfileVisitorsList(context, userModel),
                                    child: _buildFollowStatItem(
                                      icon: Icons.visibility,
                                      count: visitorsCount,
                                      label: 'زائر',
                                      color: Colors.purple,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],

            // النبذة الشخصية
            if (!isAnonymous && userModel != null && userModel.bio.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, size: 20, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'النبذة الشخصية',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      userModel.bio,
                      style: const TextStyle(fontSize: 14, height: 1.5),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // بطاقة المعلومات الأكاديمية
  Widget _buildAcademicInfoCard(userModel) {
    // التحقق من إعدادات الخصوصية
    final showUniversity = userModel.showUniversity ?? true;
    final showMajor = userModel.showMajor ?? true;

    // إذا كانت جميع المعلومات مخفية، لا تعرض البطاقة
    if (!showUniversity && !showMajor) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الأكاديمية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // عرض الجامعة فقط إذا كانت مسموحة
            if (showUniversity) ...[
              _buildInfoRow(Icons.school, 'الجامعة', userModel.university),
              if (showMajor) const SizedBox(height: 12),
            ],
            // عرض التخصص فقط إذا كان مسموحاً
            if (showMajor)
              _buildInfoRow(Icons.book, 'التخصص', userModel.major),
          ],
        ),
      ),
    );
  }

  // بطاقة المعلومات الشخصية
  Widget _buildPersonalInfoCard(userModel) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الشخصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.cake, 'العمر', '${userModel.age} سنة'),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الميلاد',
              '${userModel.birthDate.day}/${userModel.birthDate.month}/${userModel.birthDate.year}'
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.person_add,
              'تاريخ إنشاء الحساب',
              '${userModel.createdAt.day}/${userModel.createdAt.month}/${userModel.createdAt.year}'
            ),
          ],
        ),
      ),
    );
  }



  // بطاقة خيارات الضيف
  Widget _buildGuestOptionsCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.shade50,
              Colors.orange.shade100,
            ],
          ),
        ),
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.orange.shade700,
            ),
            const SizedBox(height: 16),
            const Text(
              'أنت تستخدم التطبيق كضيف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'سجل حساب جديد أو ادخل بحسابك لحفظ بياناتك في السحابة والاستفادة من جميع الميزات',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false),
                    icon: const Icon(Icons.person_add),
                    label: const Text('إنشاء حساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false),
                    icon: const Icon(Icons.login),
                    label: const Text('تسجيل دخول'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }



  // دالة مساعدة لعرض صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.blue.shade700),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  // عرض قائمة المتابعين
  void _showFollowersList(BuildContext context, dynamic userModel) {
    // المستخدم يرى قائمة متابعيه دائماً (تمرير uid المستخدم نفسه)
    final canViewFollowers = userModel.canViewFollowers(userModel.uid);
    if (!canViewFollowers) {
      // هذا لن يحدث أبداً للمستخدم نفسه، لكن نبقي الكود للأمان
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.lock, color: Colors.white),
              SizedBox(width: 8),
              Text('قائمة المتابعين مخفية'),
            ],
          ),
          backgroundColor: Colors.orange.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // العنوان
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  children: [
                    Icon(Icons.people, color: Colors.blue.shade700, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'المتابعون (${userModel.followersCount})',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(),

              // قائمة المتابعين
              Expanded(
                child: _buildFollowersListContent(scrollController, userModel),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض قائمة المتابَعين
  void _showFollowingList(BuildContext context, dynamic userModel) {
    // المستخدم يرى قائمة متابَعيه دائماً (تمرير uid المستخدم نفسه)
    final canViewFollowing = userModel.canViewFollowing(userModel.uid);
    if (!canViewFollowing) {
      // هذا لن يحدث أبداً للمستخدم نفسه، لكن نبقي الكود للأمان
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.lock, color: Colors.white),
              SizedBox(width: 8),
              Text('قائمة المتابَعين مخفية'),
            ],
          ),
          backgroundColor: Colors.orange.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // العنوان
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  children: [
                    Icon(Icons.person_add, color: Colors.green.shade700, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'المتابَعون (${userModel.followingCount})',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(),

              // قائمة المتابَعين
              Expanded(
                child: _buildFollowingListContent(scrollController, userModel),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFollowStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  // محتوى قائمة المتابعين
  Widget _buildFollowersListContent(ScrollController scrollController, dynamic userModel) {
    return FutureBuilder<List<dynamic>>(
      future: _loadFollowersData(userModel.followers),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(50),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(50),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل المتابعين',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.red.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final followers = snapshot.data ?? [];

        return ListView(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          children: [
            if (followers.isEmpty) ...[
              const SizedBox(height: 50),
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.people_outline,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا يوجد متابعون بعد',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'شارك مخططاتك الذهنية لجذب المتابعين',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // عرض المتابعين الحقيقيين
              for (var follower in followers)
                _buildFollowUserItem(
                  name: follower['fullName'] ?? '${follower['firstName'] ?? ''} ${follower['lastName'] ?? ''}',
                  university: follower['university'] ?? '',
                  major: follower['major'] ?? '',
                  isFollowing: false,
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToUserProfile(context, follower);
                  },
                ),
            ],

            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  // محتوى قائمة المتابَعين
  Widget _buildFollowingListContent(ScrollController scrollController, dynamic userModel) {
    return FutureBuilder<List<dynamic>>(
      future: _loadFollowingData(userModel.following),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(50),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(50),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل المتابَعين',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.red.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final following = snapshot.data ?? [];

        return ListView(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          children: [
            if (following.isEmpty) ...[
              const SizedBox(height: 50),
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.person_add_outlined,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا تتابع أحداً بعد',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'ابحث عن مستخدمين لمتابعتهم',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // عرض المتابَعين الحقيقيين
              for (var followingUser in following)
                _buildFollowUserItem(
                  name: followingUser['fullName'] ?? '${followingUser['firstName'] ?? ''} ${followingUser['lastName'] ?? ''}',
                  university: followingUser['university'] ?? '',
                  major: followingUser['major'] ?? '',
                  isFollowing: true,
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToUserProfile(context, followingUser);
                  },
                ),
            ],

            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  // عنصر مستخدم في قائمة المتابعة
  Widget _buildFollowUserItem({
    required String name,
    String? university,
    String? major,
    required bool isFollowing,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade100,
          child: Icon(
            Icons.person,
            color: Colors.blue.shade700,
          ),
        ),
        title: Text(
          name.trim().isEmpty ? 'مستخدم' : name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (university != null && university.isNotEmpty) ...[
              Text(
                university,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
            ],
            if (major != null && major.isNotEmpty) ...[
              Text(
                major,
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isFollowing ? Colors.green.shade50 : Colors.blue.shade50,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isFollowing ? Colors.green.shade200 : Colors.blue.shade200,
            ),
          ),
          child: Text(
            isFollowing ? 'متابَع' : 'متابع',
            style: TextStyle(
              color: isFollowing ? Colors.green.shade700 : Colors.blue.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  // التنقل إلى ملف المستخدم الشخصي
  void _navigateToUserProfile(BuildContext context, Map<String, dynamic> userData) {
    try {
      // تحويل البيانات إلى UserModel
      final userModel = UserModel.fromMap(userData);

      // التنقل إلى شاشة الملف الشخصي
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(user: userModel),
        ),
      );
    } catch (e) {
      // في حالة الخطأ، عرض رسالة للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('خطأ في فتح الملف الشخصي'),
          backgroundColor: Colors.red.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  // تحميل بيانات المتابعين من Firebase
  Future<List<dynamic>> _loadFollowersData(List<String> followerIds) async {
    if (followerIds.isEmpty) {
      print('📊 قائمة المتابعين فارغة');
      return [];
    }

    try {
      print('📊 بدء تحميل بيانات ${followerIds.length} متابع: $followerIds');
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final followers = <dynamic>[];
      final invalidFollowers = <String>[];

      for (String followerId in followerIds) {
        print('📊 تحميل بيانات المتابع: $followerId');
        final followerData = await authProvider.getUserById(followerId);
        if (followerData != null) {
          print('✅ تم العثور على بيانات المتابع: ${followerData.fullName}');
          followers.add(followerData.toMap());
        } else {
          print('❌ لم يتم العثور على بيانات المتابع: $followerId');
          invalidFollowers.add(followerId);
        }
      }

      print('📊 النتيجة النهائية: ${followers.length} متابع صالح، ${invalidFollowers.length} متابع غير صالح');
      if (invalidFollowers.isNotEmpty) {
        print('❌ المتابعون غير الصالحون: $invalidFollowers');
        // تنظيف قائمة المتابعين من المستخدمين غير الصالحين
        _cleanupInvalidFollowers(invalidFollowers);
      }

      return followers;
    } catch (e) {
      print('❌ خطأ في تحميل بيانات المتابعين: $e');
      return [];
    }
  }

  // تحميل بيانات المتابَعين من Firebase
  Future<List<dynamic>> _loadFollowingData(List<String> followingIds) async {
    if (followingIds.isEmpty) {
      print('📊 قائمة المتابَعين فارغة');
      return [];
    }

    try {
      print('📊 بدء تحميل بيانات ${followingIds.length} متابَع: $followingIds');
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final following = <dynamic>[];
      final invalidFollowing = <String>[];

      for (String followingId in followingIds) {
        print('📊 تحميل بيانات المتابَع: $followingId');
        final followingData = await authProvider.getUserById(followingId);
        if (followingData != null) {
          print('✅ تم العثور على بيانات المتابَع: ${followingData.fullName}');
          following.add(followingData.toMap());
        } else {
          print('❌ لم يتم العثور على بيانات المتابَع: $followingId');
          invalidFollowing.add(followingId);
        }
      }

      print('📊 النتيجة النهائية: ${following.length} متابَع صالح، ${invalidFollowing.length} متابَع غير صالح');
      if (invalidFollowing.isNotEmpty) {
        print('❌ المتابَعون غير الصالحون: $invalidFollowing');
        // تنظيف قائمة المتابَعين من المستخدمين غير الصالحين
        _cleanupInvalidFollowing(invalidFollowing);
      }

      return following;
    } catch (e) {
      print('❌ خطأ في تحميل بيانات المتابَعين: $e');
      return [];
    }
  }

  // بطاقة إحصائيات المشاريع
  Widget _buildProjectsStatsCard(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, mindMapProvider, child) {
        final totalMindMaps = mindMapProvider.mindMaps.length;
        final totalSubjects = mindMapProvider.subjects.length;
        final favoriteMindMaps = mindMapProvider.mindMaps.where((m) => m.isFavorite).length;
        final recentMindMaps = mindMapProvider.mindMaps
            .where((m) => DateTime.now().difference(m.updatedAt).inDays <= 7)
            .length;

        return Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.shade50,
                  Colors.blue.shade50,
                ],
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.analytics_outlined,
                        color: Colors.purple.shade700,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'إحصائيات المشاريع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // شبكة الإحصائيات
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 1.8,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  children: [
                    _buildStatItem(
                      icon: Icons.psychology_outlined,
                      title: 'الخرائط الذهنية',
                      value: totalMindMaps.toString(),
                      color: Colors.blue,
                    ),
                    _buildStatItem(
                      icon: Icons.school_outlined,
                      title: 'المواد الدراسية',
                      value: totalSubjects.toString(),
                      color: Colors.green,
                    ),
                    _buildStatItem(
                      icon: Icons.favorite_outline,
                      title: 'المفضلة',
                      value: favoriteMindMaps.toString(),
                      color: Colors.red,
                    ),
                    _buildStatItem(
                      icon: Icons.schedule_outlined,
                      title: 'الحديثة',
                      value: recentMindMaps.toString(),
                      color: Colors.orange,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // عنصر إحصائية واحد
  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // عرض قائمة زوار الملف الشخصي
  void _showProfileVisitorsList(BuildContext context, dynamic userModel) {
    final scrollController = ScrollController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(Icons.visibility, color: Colors.purple.shade700, size: 24),
                  const SizedBox(width: 12),
                  FutureBuilder<int>(
                    future: ProfileVisitService.getProfileVisitorsCount(userModel.uid),
                    builder: (context, snapshot) {
                      final visitorsCount = snapshot.data ?? 0;
                      return Text(
                        'زوار الملف الشخصي ($visitorsCount)',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            const Divider(),

            // قائمة الزوار
            Expanded(
              child: _buildProfileVisitorsListContent(scrollController, userModel),
            ),
          ],
        ),
      ),
    );
  }

  // محتوى قائمة زوار الملف الشخصي
  Widget _buildProfileVisitorsListContent(ScrollController scrollController, dynamic userModel) {
    return FutureBuilder<List<dynamic>>(
      future: _loadProfileVisitorsData(userModel.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(50),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل الزوار',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.red.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        }

        final visitors = snapshot.data ?? [];

        return ListView(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          children: [
            if (visitors.isEmpty) ...[
              const SizedBox(height: 50),
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.visibility_off,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا يوجد زوار بعد',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'شارك ملفك الشخصي لجذب الزوار',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // عرض الزوار الحقيقيين
              for (var visitor in visitors)
                _buildVisitorItem(
                  name: visitor['fullName'] ?? '${visitor['firstName'] ?? ''} ${visitor['lastName'] ?? ''}',
                  university: visitor['university'] ?? '',
                  major: visitor['major'] ?? '',
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToUserProfile(context, visitor);
                  },
                ),
            ],

            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  // عنصر زائر في القائمة
  Widget _buildVisitorItem({
    required String name,
    String? university,
    String? major,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        onTap: onTap,
        leading: CircleAvatar(
          backgroundColor: Colors.purple.shade100,
          child: Icon(
            Icons.person,
            color: Colors.purple.shade700,
          ),
        ),
        title: Text(
          name.isNotEmpty ? name : 'مستخدم',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (university != null && university.isNotEmpty)
              Text(
                university,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            if (major != null && major.isNotEmpty)
              Text(
                major,
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.purple.shade50,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.purple.shade200,
            ),
          ),
          child: Text(
            'زائر',
            style: TextStyle(
              color: Colors.purple.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  // تحميل بيانات زوار الملف الشخصي من Firebase
  Future<List<dynamic>> _loadProfileVisitorsData(String userId) async {
    try {
      final visitorIds = await ProfileVisitService.getProfileVisitors(userId);
      if (visitorIds.isEmpty) return [];

      if (!mounted) return [];
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final visitors = <dynamic>[];

      for (String visitorId in visitorIds) {
        final visitorData = await authProvider.getUserById(visitorId);
        if (visitorData != null) {
          visitors.add(visitorData.toMap());
        }
      }

      return visitors;
    } catch (e) {
      print('❌ خطأ في تحميل بيانات زوار الملف الشخصي: $e');
      return [];
    }
  }

  // إعادة تحميل بيانات الملف الشخصي
  Future<void> _refreshProfileData(AuthProvider authProvider) async {
    try {
      // إعادة تحميل بيانات المستخدم من Firebase
      await authProvider.reloadUserData();

      // تحديث آخر نشاط
      authProvider.updateLastActive();

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.refresh, color: Colors.white),
                SizedBox(width: 8),
                Text('تم تحديث البيانات بنجاح'),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // إظهار رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 8),
                Text('فشل في تحديث البيانات'),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('❌ خطأ في إعادة تحميل بيانات الملف الشخصي: $e');
    }
  }

  // تنظيف قائمة المتابعين من المستخدمين غير الصالحين
  Future<void> _cleanupInvalidFollowers(List<String> invalidFollowerIds) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.userModel;

      if (currentUser == null || authProvider.user == null) return;

      // إنشاء قائمة جديدة بدون المتابعين غير الصالحين
      final cleanFollowers = List<String>.from(currentUser.followers);
      for (String invalidId in invalidFollowerIds) {
        cleanFollowers.remove(invalidId);
      }

      print('🧹 تنظيف قائمة المتابعين: من ${currentUser.followers.length} إلى ${cleanFollowers.length}');

      // تحديث قاعدة البيانات مباشرة
      final database = FirebaseDatabase.instance;
      await database.ref('users/${authProvider.user!.uid}').update({
        'followers': cleanFollowers,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      print('✅ تم تنظيف قائمة المتابعين بنجاح');
    } catch (e) {
      print('❌ خطأ في تنظيف قائمة المتابعين: $e');
    }
  }

  // تنظيف قائمة المتابَعين من المستخدمين غير الصالحين
  Future<void> _cleanupInvalidFollowing(List<String> invalidFollowingIds) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.userModel;

      if (currentUser == null || authProvider.user == null) return;

      // إنشاء قائمة جديدة بدون المتابَعين غير الصالحين
      final cleanFollowing = List<String>.from(currentUser.following);
      for (String invalidId in invalidFollowingIds) {
        cleanFollowing.remove(invalidId);
      }

      print('🧹 تنظيف قائمة المتابَعين: من ${currentUser.following.length} إلى ${cleanFollowing.length}');

      // تحديث قاعدة البيانات مباشرة
      final database = FirebaseDatabase.instance;
      await database.ref('users/${authProvider.user!.uid}').update({
        'following': cleanFollowing,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      print('✅ تم تنظيف قائمة المتابَعين بنجاح');
    } catch (e) {
      print('❌ خطأ في تنظيف قائمة المتابَعين: $e');
    }
  }

  // بطاقة منشورات المستخدم
  Widget _buildUserPostsCard(BuildContext context, dynamic userModel) {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        // جلب منشورات المستخدم الحالي
        final userPosts = postsProvider.getPostsByUser(userModel.uid);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.purple.shade200, width: 1),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.article,
                      color: Colors.purple.shade700,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'منشوراتي',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade100,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      '${userPosts.length}',
                      style: TextStyle(
                        color: Colors.purple.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // عرض المنشورات
            if (userPosts.isEmpty) ...[
              // رسالة عدم وجود منشورات
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200, width: 1),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.article_outlined,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'لا توجد منشورات بعد',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      'ابدأ بإنشاء مخططات ذهنية ومشاركتها',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // عرض آخر 3 منشورات مباشرة
              ...userPosts.take(3).map((post) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: PostCard(post: post),
              )),

              // زر عرض جميع المنشورات إذا كان هناك أكثر من 3
              if (userPosts.length > 3) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton.icon(
                    onPressed: () => _showAllUserPosts(context, userPosts, userModel),
                    icon: const Icon(Icons.expand_more),
                    label: Text('عرض جميع المنشورات (${userPosts.length})'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.purple.shade700,
                      backgroundColor: Colors.purple.shade50,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ],
        );
      },
    );
  }

  // عرض جميع منشورات المستخدم في صفحة منفصلة
  void _showAllUserPosts(BuildContext context, List<Post> posts, dynamic userModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('منشورات ${userModel.fullName}'),
            backgroundColor: Colors.purple.shade700,
            foregroundColor: Colors.white,
          ),
          body: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: posts.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: PostCard(post: posts[index]),
              );
            },
          ),
        ),
      ),
    );
  }
}