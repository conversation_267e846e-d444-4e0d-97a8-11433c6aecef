# إعداد Firebase للمشروع

## ✅ ما تم إنجازه:

### 1. إعداد Android
- ✅ تم إضافة Firebase plugin إلى `android/app/build.gradle`
- ✅ تم إضافة Google Services classpath إلى `android/build.gradle`
- ✅ تم تحديث `applicationId` إلى `com.aymen.mind_map`
- ✅ تم تحديث `namespace` إلى `com.aymen.mind_map`

### 2. إعداد Flutter Dependencies
- ✅ تم إضافة Firebase packages إلى `pubspec.yaml`:
  - `firebase_core: ^3.8.0`
  - `firebase_auth: ^5.3.3`
  - `cloud_firestore: ^5.5.0`
  - `firebase_storage: ^12.3.6`

### 3. إعداد الكود
- ✅ تم إنشاء `lib/firebase_options.dart`
- ✅ تم إنشاء `lib/services/firebase_service.dart`
- ✅ تم إنشاء `lib/providers/auth_provider.dart`
- ✅ تم إنشاء `lib/screens/auth_screen.dart`
- ✅ تم تحديث `lib/main.dart` لتهيئة Firebase

## 🔧 الخطوات المطلوبة لإكمال الإعداد:

### 1. تحديث firebase_options.dart
يجب عليك تحديث الملف `lib/firebase_options.dart` بالقيم الصحيحة من مشروع Firebase الخاص بك:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'YOUR_ACTUAL_API_KEY',           // من google-services.json
  appId: 'YOUR_ACTUAL_APP_ID',             // من google-services.json
  messagingSenderId: 'YOUR_SENDER_ID',     // من google-services.json
  projectId: 'YOUR_PROJECT_ID',            // من google-services.json
  storageBucket: 'YOUR_STORAGE_BUCKET',    // من google-services.json
);
```

### 2. التحقق من google-services.json
تأكد من أن ملف `android/app/google-services.json` يحتوي على:
- `package_name`: `com.aymen.mind_map`
- جميع المفاتيح والمعرفات الصحيحة

### 3. إعداد Firebase Console
في Firebase Console، تأكد من تفعيل:
- **Authentication** مع Email/Password و Anonymous
- **Cloud Firestore** مع القواعد المناسبة
- **Storage** إذا كنت تريد رفع الملفات

### 4. قواعد Firestore المقترحة
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح للمستخدمين المسجلين بالوصول لبياناتهم فقط
    match /users/{userId}/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 🚀 المميزات المتاحة:

### 1. المصادقة
- تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
- إنشاء حساب جديد
- الدخول كضيف (Anonymous)
- تسجيل الخروج

### 2. قاعدة البيانات
- حفظ المخططات الذهنية في Cloud Firestore
- حفظ المواد الدراسية
- مزامنة البيانات بين الأجهزة
- الاستماع للتغييرات في الوقت الفعلي

### 3. الأمان
- كل مستخدم يصل لبياناته فقط
- تشفير البيانات أثناء النقل والتخزين
- مصادقة آمنة عبر Firebase Auth

## 🔍 اختبار الإعداد:

1. قم بتشغيل التطبيق: `flutter run`
2. يجب أن تظهر شاشة تسجيل الدخول
3. جرب إنشاء حساب جديد أو الدخول كضيف
4. تأكد من أن البيانات تُحفظ في Firebase Console

## 📝 ملاحظات مهمة:

- تأكد من أن `applicationId` في `android/app/build.gradle` يطابق `package_name` في `google-services.json`
- قم بتحديث قواعد Firestore لتناسب احتياجاتك
- فعّل Authentication methods في Firebase Console
- اختبر على جهاز حقيقي للتأكد من عمل Firebase بشكل صحيح
