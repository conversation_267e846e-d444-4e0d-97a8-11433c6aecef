import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/logout_loading_screen.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _universityController = TextEditingController();
  final _majorController = TextEditingController();
  final _bioController = TextEditingController();
  bool _isSignUp = false;
  bool _obscurePassword = true;
  DateTime? _selectedBirthDate;
  int? _calculatedAge;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _universityController.dispose();
    _majorController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  // حساب العمر من تاريخ الميلاد
  void _calculateAge() {
    if (_selectedBirthDate != null) {
      final now = DateTime.now();
      int age = now.year - _selectedBirthDate!.year;

      if (now.month < _selectedBirthDate!.month ||
          (now.month == _selectedBirthDate!.month && now.day < _selectedBirthDate!.day)) {
        age--;
      }

      setState(() {
        _calculatedAge = age;
      });
    }
  }

  // اختيار تاريخ الميلاد
  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 20)), // 20 سنة
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 100)), // 100 سنة
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 16)), // 16 سنة
      helpText: 'اختر تاريخ الميلاد',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
      _calculateAge();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2196F3),
              Color(0xFF1976D2),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // شعار التطبيق
                      const Icon(
                        Icons.psychology,
                        size: 80,
                        color: Color(0xFF2196F3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'مخطط ذهني للطلاب',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2196F3),
                        ),
                      ),
                      const SizedBox(height: 32),

                      // نموذج تسجيل الدخول/التسجيل
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // البريد الإلكتروني
                            TextFormField(
                              controller: _emailController,
                              keyboardType: TextInputType.emailAddress,
                              textDirection: TextDirection.ltr,
                              decoration: const InputDecoration(
                                labelText: 'البريد الإلكتروني',
                                prefixIcon: Icon(Icons.email),
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال البريد الإلكتروني';
                                }
                                if (!value.contains('@')) {
                                  return 'يرجى إدخال بريد إلكتروني صالح';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // كلمة المرور
                            TextFormField(
                              controller: _passwordController,
                              obscureText: _obscurePassword,
                              textDirection: TextDirection.ltr,
                              decoration: InputDecoration(
                                labelText: 'كلمة المرور',
                                prefixIcon: const Icon(Icons.lock),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                border: const OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال كلمة المرور';
                                }
                                if (value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),

                            // الحقول الإضافية لإنشاء الحساب
                            if (_isSignUp) ...[
                              const SizedBox(height: 16),

                              // الاسم الأول
                              TextFormField(
                                controller: _firstNameController,
                                decoration: const InputDecoration(
                                  labelText: 'الاسم الأول',
                                  prefixIcon: Icon(Icons.person),
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'يرجى إدخال الاسم الأول';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // اسم العائلة
                              TextFormField(
                                controller: _lastNameController,
                                decoration: const InputDecoration(
                                  labelText: 'اسم العائلة',
                                  prefixIcon: Icon(Icons.person_outline),
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'يرجى إدخال اسم العائلة';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // اسم الجامعة
                              TextFormField(
                                controller: _universityController,
                                decoration: const InputDecoration(
                                  labelText: 'اسم الجامعة',
                                  prefixIcon: Icon(Icons.school),
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'يرجى إدخال اسم الجامعة';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // التخصص
                              TextFormField(
                                controller: _majorController,
                                decoration: const InputDecoration(
                                  labelText: 'التخصص',
                                  prefixIcon: Icon(Icons.book),
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'يرجى إدخال التخصص';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // النبذة الشخصية
                              TextFormField(
                                controller: _bioController,
                                decoration: const InputDecoration(
                                  labelText: 'النبذة الشخصية (اختياري)',
                                  prefixIcon: Icon(Icons.info_outline),
                                  border: OutlineInputBorder(),
                                  hintText: 'اكتب نبذة مختصرة عن نفسك...',
                                ),
                                maxLines: 3,
                                maxLength: 200,
                              ),
                              const SizedBox(height: 16),

                              // تاريخ الميلاد
                              InkWell(
                                onTap: _selectBirthDate,
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today, color: Colors.grey),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Text(
                                          _selectedBirthDate == null
                                              ? 'اختر تاريخ الميلاد'
                                              : '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}',
                                          style: TextStyle(
                                            color: _selectedBirthDate == null ? Colors.grey : Colors.black,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // عرض العمر المحسوب
                              if (_calculatedAge != null) ...[
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.cake, color: Colors.blue),
                                      const SizedBox(width: 8),
                                      Text(
                                        'العمر: $_calculatedAge سنة',
                                        style: const TextStyle(
                                          color: Colors.blue,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],

                            const SizedBox(height: 24),

                            // رسالة الخطأ
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                if (authProvider.errorMessage != null) {
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.red.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                                    ),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.error, color: Colors.red),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            authProvider.errorMessage!,
                                            style: const TextStyle(color: Colors.red),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),

                            // زر تسجيل الدخول/التسجيل
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                return SizedBox(
                                  width: double.infinity,
                                  height: 48,
                                  child: ElevatedButton(
                                    onPressed: authProvider.isLoading
                                        ? null
                                        : () => _handleSubmit(authProvider),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF2196F3),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: authProvider.isLoading
                                        ? const CircularProgressIndicator(
                                            color: Colors.white,
                                          )
                                        : Text(
                                            _isSignUp ? 'إنشاء حساب' : 'تسجيل الدخول',
                                            style: const TextStyle(fontSize: 16),
                                          ),
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 16),

                            // زر التبديل بين تسجيل الدخول والتسجيل
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _isSignUp = !_isSignUp;
                                });
                                context.read<AuthProvider>().clearError();
                              },
                              child: Text(
                                _isSignUp
                                    ? 'لديك حساب بالفعل؟ تسجيل الدخول'
                                    : 'ليس لديك حساب؟ إنشاء حساب جديد',
                              ),
                            ),
                            const SizedBox(height: 16),

                            // زر الدخول كضيف
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                return OutlinedButton.icon(
                                  onPressed: authProvider.isLoading
                                      ? null
                                      : () => _signInAsGuest(authProvider),
                                  icon: const Icon(Icons.person),
                                  label: const Text('الدخول كضيف'),
                                  style: OutlinedButton.styleFrom(
                                    minimumSize: const Size(double.infinity, 48),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleSubmit(AuthProvider authProvider) async {
    if (!_formKey.currentState!.validate()) return;

    if (_isSignUp) {
      // التحقق من البيانات الإضافية
      if (_selectedBirthDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يرجى اختيار تاريخ الميلاد')),
        );
        return;
      }
    }

    // عرض شاشة التحميل المؤقتة
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _LoginLoadingScreen(
          isSignUp: _isSignUp,
          authProvider: authProvider,
          email: _emailController.text.trim(),
          password: _passwordController.text,
          firstName: _isSignUp ? _firstNameController.text.trim() : '',
          lastName: _isSignUp ? _lastNameController.text.trim() : '',
          university: _isSignUp ? _universityController.text.trim() : '',
          major: _isSignUp ? _majorController.text.trim() : '',
          bio: _isSignUp ? _bioController.text.trim() : '',
          birthDate: _isSignUp ? _selectedBirthDate : null,
        ),
      ),
    );
  }

  Future<void> _signInAsGuest(AuthProvider authProvider) async {
    // عرض شاشة التحميل المؤقتة للضيف
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LogoutLoadingScreen(
          title: 'جاري الدخول كضيف...',
          subtitle: 'استكشف التطبيق بحرية',
          duration: const Duration(seconds: 2),
          gradientColors: [
            Colors.orange.shade400,
            Colors.orange.shade600,
            Colors.orange.shade800,
          ],
          onComplete: () async {
            try {
              final success = await authProvider.signInAnonymously();
              if (success && context.mounted) {
                Navigator.of(context).pushReplacementNamed('/home');
              } else if (context.mounted) {
                Navigator.of(context).pop(); // العودة لشاشة تسجيل الدخول
              }
            } catch (e) {
              if (context.mounted) {
                Navigator.of(context).pop(); // العودة لشاشة تسجيل الدخول
              }
            }
          },
        ),
      ),
    );
  }
}

class _LoginLoadingScreen extends StatefulWidget {
  final bool isSignUp;
  final AuthProvider authProvider;
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String university;
  final String major;
  final String bio;
  final DateTime? birthDate;

  const _LoginLoadingScreen({
    required this.isSignUp,
    required this.authProvider,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    required this.university,
    required this.major,
    required this.bio,
    this.birthDate,
  });

  @override
  State<_LoginLoadingScreen> createState() => _LoginLoadingScreenState();
}

class _LoginLoadingScreenState extends State<_LoginLoadingScreen> {
  String subtitle = '';

  @override
  void initState() {
    super.initState();
    subtitle = widget.isSignUp ? 'مرحباً بك في التطبيق' : 'أهلاً بعودتك';
    _performAuth();
  }

  Future<void> _performAuth() async {
    await Future.delayed(const Duration(seconds: 1)); // انتظار قصير للأنيميشن

    bool success;
    try {
      if (widget.isSignUp) {
        success = await widget.authProvider.createUserWithCompleteProfile(
          email: widget.email,
          password: widget.password,
          firstName: widget.firstName,
          lastName: widget.lastName,
          university: widget.university,
          major: widget.major,
          bio: widget.bio,
          birthDate: widget.birthDate!,
        );
        if (success) {
          setState(() {
            subtitle = 'مرحباً بك في التطبيق\n${widget.firstName} ${widget.lastName}';
          });
        }
      } else {
        success = await widget.authProvider.signInWithEmailAndPassword(
          widget.email,
          widget.password,
        );
        if (success && widget.authProvider.userModel != null) {
          setState(() {
            subtitle = 'أهلاً بعودتك\n${widget.authProvider.userModel!.firstName} ${widget.authProvider.userModel!.lastName}';
          });
        }
      }

      await Future.delayed(const Duration(seconds: 2)); // انتظار لإظهار الاسم

      if (success && mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      } else if (mounted) {
        Navigator.of(context).pop(); // العودة لشاشة تسجيل الدخول
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // العودة لشاشة تسجيل الدخول
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LogoutLoadingScreen(
      title: widget.isSignUp ? 'جاري إنشاء الحساب...' : 'جاري تسجيل الدخول...',
      subtitle: subtitle,
      duration: const Duration(seconds: 4),
      gradientColors: [
        Colors.blue.shade500,
        Colors.blue.shade700,
        Colors.blue.shade900,
      ],
      onComplete: () {}, // لا نحتاج onComplete لأننا نتحكم في التنقل يدوياً
    );
  }
}
