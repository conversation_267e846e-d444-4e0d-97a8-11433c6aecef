import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/user_model.dart';
import '../providers/admin_user_actions_provider.dart';
import '../widgets/connectivity_wrapper.dart';

class UserManagementDetailScreen extends StatefulWidget {
  final UserModel user;

  const UserManagementDetailScreen({
    super.key,
    required this.user,
  });

  @override
  State<UserManagementDetailScreen> createState() => _UserManagementDetailScreenState();
}

class _UserManagementDetailScreenState extends State<UserManagementDetailScreen> {
  late AdminUserActionsProvider _provider;
  late UserModel _currentUser;
  int _refreshKey = 0; // مفتاح لإعادة بناء FutureBuilder

  // التحقق من حالة الحظر
  bool get isBanned => _currentUser.isBanned == true;

  @override
  void initState() {
    super.initState();
    _provider = AdminUserActionsProvider();
    _currentUser = widget.user;
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    // تحميل بيانات المستخدم المحدثة
    await _provider.loadUsers();
    final updatedUser = _provider.users.firstWhere(
      (user) => user.uid == widget.user.uid,
      orElse: () => widget.user,
    );
    if (mounted) {
      setState(() {
        _currentUser = updatedUser;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _provider,
      child: ConnectivityWrapper(
        child: Scaffold(
        appBar: AppBar(
          title: Text(
            'إدارة ${_currentUser.firstName}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.red.shade700,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة معلومات المستخدم
              _buildUserInfoCard(),
              const SizedBox(height: 20),
              
              // إحصائيات المستخدم
              _buildUserStatsCard(),
              const SizedBox(height: 20),
              
              // إجراءات المستخدم
              _buildUserActionsCard(),
              const SizedBox(height: 20),
              
              // تاريخ الإجراءات
              _buildActionHistoryCard(),
            ],
          ),
        ),
      ),
        ),
    );
  }

  Widget _buildUserInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade600,
              Colors.blue.shade800,
            ],
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 50,
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              child: Text(
                _currentUser.firstName.isNotEmpty
                    ? _currentUser.firstName[0].toUpperCase()
                    : 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // اسم المستخدم
            Text(
              _currentUser.fullName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // البريد الإلكتروني
            Text(
              _currentUser.email,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),



            // معلومات إضافية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildInfoItem(
                  Icons.school,
                  'الجامعة',
                  _currentUser.university.isEmpty ? 'غير محدد' : _currentUser.university,
                ),
                _buildInfoItem(
                  Icons.book,
                  'التخصص',
                  _currentUser.major.isEmpty ? 'غير محدد' : _currentUser.major,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white.withValues(alpha: 0.8),
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUserStatsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات المستخدم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Consumer<AdminUserActionsProvider>(
              builder: (context, provider, child) {
                return FutureBuilder<Map<String, int>>(
                  key: ValueKey('stats_$_refreshKey'), // مفتاح لإعادة البناء
                  future: provider.getUserStats(_currentUser.uid),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final stats = snapshot.data ?? {'warnings': 0};
                    final isRestricted = provider.isUserRestricted(_currentUser);

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildStatItem('التحذيرات', '${stats['warnings']}', Colors.red, Icons.warning),
                        _buildStatItem(
                          'الحالة',
                          isBanned ? 'محظور' : (isRestricted ? 'مقيد' : 'نشط'),
                          isBanned ? Colors.red : (isRestricted ? Colors.amber.shade400 : Colors.green),
                          isBanned ? Icons.block : (isRestricted ? Icons.warning : Icons.check_circle)
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color, IconData icon) {
    return GestureDetector(
      onTap: () {
        if (label == 'الحالة' && value == 'مقيد') {
          _showRestrictionDetails();
        }
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  void _showRestrictionDetails() {
    final provider = _provider;
    if (!provider.isUserRestricted(widget.user)) return;

    final restrictionTypes = provider.getActiveRestrictionTypes(widget.user);
    final restrictionTypesText = restrictionTypes.map((type) {
      switch (type) {
        case 'posting': return 'النشر';
        case 'commenting': return 'التعليق';
        case 'creating_mindmaps': return 'إنشاء مخططات ذهنية';
        case 'editing_mindmaps': return 'تعديل المخططات الذهنية';
        case 'following': return 'متابعة المستخدمين';
        case 'profile_editing': return 'تعديل المعلومات الشخصية';
        case 'password_change': return 'تغيير كلمة السر';
        case 'privacy_settings': return 'تعديل إعدادات الخصوصية';
        case 'reporting_edit_request': return 'طلب التعديل والإبلاغ';
        default: return type;
      }
    }).join('، ');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل التقييد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المستخدم: ${widget.user.fullName}'),
            const SizedBox(height: 8),
            if (widget.user.restrictionReason != null)
              Text('السبب: ${widget.user.restrictionReason}'),
            const SizedBox(height: 8),
            if (widget.user.restrictedAt != null)
              Text('تاريخ التقييد: ${widget.user.restrictedAt.toString().split(' ')[0]}'),
            const SizedBox(height: 8),
            if (widget.user.restrictionEndDate != null)
              Text('ينتهي في: ${widget.user.restrictionEndDate.toString().split(' ')[0]}'),
            const SizedBox(height: 8),
            Text('مقيد على: $restrictionTypesText'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showUnrestrictConfirmation();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('إلغاء التقييد'),
          ),
        ],
      ),
    );
  }

  Widget _buildUserActionsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.gavel, color: Colors.red.shade700),
                const SizedBox(width: 8),
                const Text(
                  'إجراءات المستخدم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // أزرار الإجراءات
            Consumer<AdminUserActionsProvider>(
              builder: (context, provider, child) {
                final isRestricted = provider.isUserRestricted(_currentUser);

                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 2.5,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  children: [
                    // زر الحظر/إلغاء الحظر
                    if (!isBanned)
                      _buildActionButton(
                        'حظر المستخدم',
                        Icons.block,
                        Colors.red,
                        () => _showBanDialog(),
                      ),
                    if (isBanned)
                      _buildActionButton(
                        'إلغاء الحظر',
                        Icons.check_circle,
                        Colors.green,
                        () => _showUnbanConfirmation(),
                      ),
                    // أزرار التقييد
                    if (!isRestricted && !isBanned)
                      _buildActionButton(
                        'تقييد مؤقت',
                        Icons.warning,
                        Colors.amber.shade400,
                        () => _showRestrictDialog(),
                      ),
                    if (isRestricted)
                      _buildActionButton(
                        'إلغاء التقييد',
                        Icons.check_circle_outline,
                        Colors.green,
                        () => _showUnrestrictConfirmation(),
                      ),
                    // أزرار أخرى
                    if (!isBanned)
                      _buildActionButton(
                        'إرسال تحذير',
                        Icons.warning,
                        Colors.yellow.shade700,
                        () => _showWarnDialog(),
                      ),
                    _buildActionButton(
                      'عرض التفاصيل',
                      Icons.info,
                      Colors.blue,
                      () => _showUserDetails(),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withValues(alpha: 0.3)),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionHistoryCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history, color: Colors.grey.shade700),
                const SizedBox(width: 8),
                const Text(
                  'تاريخ الإجراءات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // قائمة الإجراءات السابقة
            Consumer<AdminUserActionsProvider>(
              builder: (context, provider, child) {
                return FutureBuilder<List<Map<String, dynamic>>>(
                  key: ValueKey(_refreshKey), // مفتاح لإعادة البناء
                  future: provider.getUserActionHistory(_currentUser.uid),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final actions = snapshot.data ?? [];

                    return Column(
                      children: [
                        // إجراءات النظام الأساسية
                        _buildActionHistoryItem(
                          'تم إنشاء الحساب',
                          _currentUser.createdAt.toString().split(' ')[0],
                          Icons.person_add,
                          Colors.green,
                        ),
                        _buildActionHistoryItem(
                          'آخر نشاط',
                          _currentUser.lastActiveAt.toString().split(' ')[0],
                          Icons.access_time,
                          Colors.blue,
                        ),

                        // إجراءات الإدارة
                        ...actions.map((action) => _buildActionHistoryItem(
                          _getActionTitle(action['actionType'] ?? ''),
                          _formatActionDate(action['actionDate'] ?? ''),
                          _getActionIcon(action['actionType'] ?? ''),
                          _getActionColor(action['actionType'] ?? ''),
                          details: action['details'],
                        )),
                      ],
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionHistoryItem(String action, String date, IconData icon, Color color, {String? details}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  action,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  date,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                if (details != null && details.isNotEmpty)
                  Text(
                    details,
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey.shade500,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دوال مساعدة لتنسيق تاريخ الإجراءات
  String _getActionTitle(String actionType) {
    switch (actionType) {
      case 'ban': return 'تم حظر المستخدم';
      case 'restrict': return 'تم تقييد المستخدم';
      case 'warn': return 'تم إرسال تحذير';
      case 'unban': return 'تم إلغاء الحظر';
      case 'unrestrict': return 'تم إلغاء التقييد';
      default: return 'إجراء إداري';
    }
  }

  String _formatActionDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  IconData _getActionIcon(String actionType) {
    switch (actionType) {
      case 'ban': return Icons.block;
      case 'restrict': return Icons.warning;
      case 'warn': return Icons.warning;
      case 'unban': return Icons.check_circle;
      case 'unrestrict': return Icons.check_circle_outline;
      default: return Icons.admin_panel_settings;
    }
  }

  Color _getActionColor(String actionType) {
    switch (actionType) {
      case 'ban': return Colors.red;
      case 'restrict': return Colors.amber.shade400;
      case 'warn': return Colors.yellow.shade700;
      case 'unban': return Colors.green;
      case 'unrestrict': return Colors.green;
      default: return Colors.blue;
    }
  }

  // دوال الإجراءات
  void _showBanDialog() {
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.block, color: Colors.red),
            const SizedBox(width: 8),
            const Text('حظر المستخدم'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حظر المستخدم "${widget.user.fullName}"؟'),
            const SizedBox(height: 8),
            const Text(
              'سيتم منعه من الوصول للتطبيق نهائياً.',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('سبب الحظر:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                hintText: 'اكتب سبب الحظر...',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _banUser(reasonController.text.trim());
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حظر', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showRestrictDialog() {
    int selectedDays = 7;
    String reason = '';
    List<String> selectedRestrictions = [];
    final TextEditingController reasonController = TextEditingController();

    final restrictionOptions = {
      'posting': {'name': 'النشر', 'icon': Icons.post_add, 'description': 'منع إنشاء منشورات جديدة'},
      'commenting': {'name': 'التعليق', 'icon': Icons.comment, 'description': 'منع التعليق على المنشورات'},
      'creating_mindmaps': {'name': 'إنشاء مخططات ذهنية', 'icon': Icons.psychology, 'description': 'منع إنشاء مخططات ذهنية جديدة'},
      'editing_mindmaps': {'name': 'تعديل المخططات الذهنية', 'icon': Icons.edit, 'description': 'منع تعديل المخططات الموجودة'},
      'following': {'name': 'متابعة المستخدمين', 'icon': Icons.person_add, 'description': 'منع متابعة مستخدمين جدد'},
      'profile_editing': {'name': 'تعديل المعلومات الشخصية', 'icon': Icons.person, 'description': 'منع تعديل الملف الشخصي'},
      'password_change': {'name': 'تغيير كلمة السر', 'icon': Icons.lock, 'description': 'منع تغيير كلمة المرور'},
      'privacy_settings': {'name': 'تعديل إعدادات الخصوصية', 'icon': Icons.privacy_tip, 'description': 'منع تعديل إعدادات الخصوصية'},
      'reporting_edit_request': {'name': 'طلب التعديل والإبلاغ', 'icon': Icons.report, 'description': 'منع الإبلاغ وطلب التعديل'},
    };

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // العنوان
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.warning,
                          color: Colors.amber.shade700,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تقييد مؤقت',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.amber.shade800,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'تقييد المستخدم "${widget.user.fullName}"',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // المحتوى
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // مدة التقييد
                        _buildSectionTitle('⏰ مدة التقييد'),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: DropdownButton<int>(
                            value: selectedDays,
                            isExpanded: true,
                            underline: const SizedBox(),
                            onChanged: (value) => setState(() => selectedDays = value!),
                            items: const [
                              DropdownMenuItem(value: 1, child: Text('يوم واحد')),
                              DropdownMenuItem(value: 3, child: Text('3 أيام')),
                              DropdownMenuItem(value: 7, child: Text('أسبوع')),
                              DropdownMenuItem(value: 14, child: Text('أسبوعين')),
                              DropdownMenuItem(value: 30, child: Text('شهر')),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),

                        // أنواع التقييد
                        _buildSectionTitle('🚫 أنواع التقييد'),
                        const SizedBox(height: 12),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            children: restrictionOptions.entries.map((entry) {
                              final isSelected = selectedRestrictions.contains(entry.key);
                              return Container(
                                decoration: BoxDecoration(
                                  color: isSelected ? Colors.amber.shade50 : null,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: Colors.grey.shade200,
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                                child: CheckboxListTile(
                                  title: Row(
                                    children: [
                                      Icon(
                                        entry.value['icon'] as IconData,
                                        size: 20,
                                        color: isSelected ? Colors.amber.shade700 : Colors.grey.shade600,
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              entry.value['name'] as String,
                                              style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                color: isSelected ? Colors.amber.shade800 : null,
                                              ),
                                            ),
                                            Text(
                                              entry.value['description'] as String,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  value: isSelected,
                                  onChanged: (bool? value) {
                                    setState(() {
                                      if (value == true) {
                                        selectedRestrictions.add(entry.key);
                                      } else {
                                        selectedRestrictions.remove(entry.key);
                                      }
                                    });
                                  },
                                  activeColor: Colors.amber.shade600,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // سبب التقييد
                        _buildSectionTitle('📝 سبب التقييد'),
                        const SizedBox(height: 12),
                        TextField(
                          controller: reasonController,
                          onChanged: (value) => reason = value,
                          decoration: InputDecoration(
                            hintText: 'اكتب سبب التقييد بالتفصيل...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.amber.shade400, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                            contentPadding: const EdgeInsets.all(16),
                          ),
                          maxLines: 3,
                          minLines: 2,
                        ),
                      ],
                    ),
                  ),
                ),

                // الأزرار
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'إلغاء',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: selectedRestrictions.isEmpty ? null : () {
                            Navigator.pop(context);
                            _restrictUser(selectedDays, reason, selectedRestrictions);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.amber.shade400,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.warning, size: 20),
                              const SizedBox(width: 8),
                              const Text(
                                'تطبيق التقييد',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  void _showWarnDialog() {
    final TextEditingController messageController = TextEditingController();
    String selectedReason = 'سلوك غير مناسب';

    final warningReasons = [
      'سلوك غير مناسب',
      'انتهاك قوانين المجتمع',
      'محتوى غير لائق',
      'تحرش أو إزعاج',
      'نشر محتوى مخالف',
      'استخدام لغة غير مهذبة',
      'انتحال شخصية',
      'نشاط مشبوه',
      'أخرى',
    ];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // العنوان
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.warning,
                          color: Colors.orange.shade700,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إرسال تحذير',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade800,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'تحذير للمستخدم "${widget.user.fullName}"',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // المحتوى
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // سبب التحذير
                        _buildSectionTitle('📋 سبب التحذير'),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: DropdownButton<String>(
                            value: selectedReason,
                            isExpanded: true,
                            underline: const SizedBox(),
                            onChanged: (value) => setState(() => selectedReason = value!),
                            items: warningReasons.map((reason) => DropdownMenuItem(
                              value: reason,
                              child: Text(reason),
                            )).toList(),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // رسالة التحذير
                        _buildSectionTitle('💬 رسالة التحذير'),
                        const SizedBox(height: 12),
                        TextField(
                          controller: messageController,
                          decoration: InputDecoration(
                            hintText: 'اكتب رسالة التحذير التفصيلية...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.orange.shade400, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                            contentPadding: const EdgeInsets.all(16),
                          ),
                          maxLines: 4,
                          minLines: 3,
                        ),
                        const SizedBox(height: 16),

                        // ملاحظة تحذيرية
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info_outline, color: Colors.red.shade600, size: 20),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'سيتم إرسال هذا التحذير كإشعار للمستخدم. تراكم التحذيرات قد يؤدي إلى تقييد الحساب.',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.red.shade700,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // الأزرار
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'إلغاء',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            _warnUser(selectedReason, messageController.text);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange.shade600,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.send, size: 20),
                              const SizedBox(width: 8),
                              const Text(
                                'إرسال التحذير',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showUserDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${widget.user.fullName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('الاسم الكامل', widget.user.fullName),
              _buildDetailRow('البريد الإلكتروني', widget.user.email),
              _buildDetailRow('الجامعة', widget.user.university),
              _buildDetailRow('التخصص', widget.user.major),
              _buildDetailRow('النبذة الشخصية', widget.user.bio),
              _buildDetailRow('تاريخ التسجيل', widget.user.createdAt.toString().split(' ')[0]),
              _buildDetailRow('آخر نشاط', widget.user.lastActiveAt.toString().split(' ')[0]),
              _buildDetailRow('المتابعين', '${widget.user.followers.length}'),
              _buildDetailRow('يتابع', '${widget.user.following.length}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value.isEmpty ? 'غير محدد' : value)),
        ],
      ),
    );
  }

  void _banUser(String reason) async {
    if (!mounted) return;

    final banReason = reason.isNotEmpty ? reason : 'حظر من لوحة التحكم';

    try {
      // حظر المستخدم باستخدام المزود
      final success = await _provider.banUser(widget.user.uid, banReason);

      // تسجيل الإجراء في تاريخ المستخدم
      if (success) {
        final actionData = {
          'actionType': 'ban',
          'actionDate': DateTime.now().toIso8601String(),
          'actionBy': 'admin',
          'details': 'تم حظر المستخدم. السبب: $banReason',
          'banReason': banReason,
        };
        final database = FirebaseDatabase.instance.ref();
        await database.child('user_actions/${widget.user.uid}').push().set(actionData);
      }

      // إعادة تحميل البيانات لتحديث الواجهة
      if (success) {
        await _loadUserData();
        if (mounted) {
          setState(() {
            _refreshKey++; // تحديث المفتاح لإعادة بناء FutureBuilder
          });
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    success
                      ? 'تم حظر المستخدم ${widget.user.fullName}'
                      : 'فشل في حظر المستخدم',
                  ),
                ),
              ],
            ),
            backgroundColor: success ? Colors.red : Colors.grey,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            action: success ? SnackBarAction(
              label: 'تم ✓',
              textColor: Colors.white,
              onPressed: () {},
            ) : null,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text('خطأ في حظر المستخدم: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _restrictUser(int days, String reason, List<String> restrictionTypes) async {
    final provider = _provider;
    final success = await provider.restrictUser(
      _currentUser.uid,
      days,
      reason.isEmpty ? 'تقييد من لوحة التحكم' : reason,
      restrictionTypes
    );

    if (mounted) {
      final restrictionTypesText = restrictionTypes.map((type) {
        switch (type) {
          case 'posting': return 'النشر';
          case 'commenting': return 'التعليق';
          case 'creating_mindmaps': return 'إنشاء مخططات ذهنية';
          case 'editing_mindmaps': return 'تعديل المخططات الذهنية';
          case 'following': return 'متابعة المستخدمين';
          case 'profile_editing': return 'تعديل المعلومات الشخصية';
          case 'password_change': return 'تغيير كلمة السر';
          case 'privacy_settings': return 'تعديل إعدادات الخصوصية';
          case 'reporting_edit_request': return 'طلب التعديل والإبلاغ';
          default: return type;
        }
      }).join('، ');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success
            ? 'تم تقييد المستخدم ${_currentUser.fullName} لمدة $days أيام على: $restrictionTypesText'
            : 'فشل في تقييد المستخدم'),
          backgroundColor: success ? Colors.orange : Colors.red,
        ),
      );

      if (success) {
        // إعادة تحميل بيانات المستخدم
        await _loadUserData();
      }
    }
  }

  void _warnUser(String reason, String message) async {
    if (!mounted) return;

    final fullMessage = message.isNotEmpty ? '$reason: $message' : reason;

    try {
      // إرسال التحذير باستخدام المزود
      final success = await _provider.warnUser(widget.user.uid, fullMessage);

      // تسجيل الإجراء في تاريخ المستخدم
      if (success) {
        final actionData = {
          'actionType': 'warn',
          'actionDate': DateTime.now().toIso8601String(),
          'actionBy': 'admin',
          'details': 'تم إرسال تحذير للمستخدم. السبب: $fullMessage',
          'warningReason': reason,
          'warningMessage': message,
        };
        // استخدام Firebase مباشرة لتسجيل الإجراء
        final database = FirebaseDatabase.instance.ref();
        await database.child('user_actions/${widget.user.uid}').push().set(actionData);
      }

      // إعادة تحميل البيانات لتحديث الإحصائيات وتاريخ الإجراءات
      if (success) {
        await _loadUserData();
        if (mounted) {
          setState(() {
            _refreshKey++; // تحديث المفتاح لإعادة بناء FutureBuilder
          });
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    success
                      ? 'تم إرسال تحذير للمستخدم ${widget.user.fullName}'
                      : 'فشل في إرسال التحذير',
                  ),
                ),
              ],
            ),
            backgroundColor: success ? Colors.orange.shade600 : Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            action: success ? SnackBarAction(
              label: 'تم ✓',
              textColor: Colors.white,
              onPressed: () {},
            ) : null,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text('خطأ في إرسال التحذير: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _showUnrestrictConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء التقييد'),
        content: Text('هل أنت متأكد من إلغاء تقييد المستخدم "${widget.user.fullName}"؟\nسيتم السماح له بالوصول لجميع الميزات مرة أخرى.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _unrestrictUser();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('إلغاء التقييد'),
          ),
        ],
      ),
    );
  }

  void _showUnbanConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            const SizedBox(width: 8),
            const Text('إلغاء الحظر'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من إلغاء حظر المستخدم "${widget.user.fullName}"؟'),
            const SizedBox(height: 8),
            const Text(
              'سيتم السماح له بالوصول للتطبيق مرة أخرى.',
              style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
            ),
            if (_currentUser.banReason != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('سبب الحظر السابق:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red.shade700)),
                    const SizedBox(height: 4),
                    Text(_currentUser.banReason!, style: TextStyle(color: Colors.red.shade600)),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _unbanUser();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('إلغاء الحظر'),
          ),
        ],
      ),
    );
  }

  void _unrestrictUser() async {
    final provider = _provider;
    final success = await provider.unrestrictUser(_currentUser.uid);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success
            ? 'تم إلغاء تقييد المستخدم ${_currentUser.fullName} بنجاح'
            : 'فشل في إلغاء تقييد المستخدم'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );

      if (success) {
        // إعادة تحميل بيانات المستخدم
        await _loadUserData();
      }
    }
  }

  void _unbanUser() async {
    if (!mounted) return;

    try {
      // إلغاء حظر المستخدم باستخدام المزود
      final success = await _provider.unbanUser(widget.user.uid);

      // تسجيل الإجراء في تاريخ المستخدم
      if (success) {
        final actionData = {
          'actionType': 'unban',
          'actionDate': DateTime.now().toIso8601String(),
          'actionBy': 'admin',
          'details': 'تم إلغاء حظر المستخدم',
        };
        final database = FirebaseDatabase.instance.ref();
        await database.child('user_actions/${widget.user.uid}').push().set(actionData);
      }

      // إعادة تحميل البيانات لتحديث الواجهة
      if (success) {
        await _loadUserData();
        if (mounted) {
          setState(() {
            _refreshKey++; // تحديث المفتاح لإعادة بناء FutureBuilder
          });
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    success
                      ? 'تم إلغاء حظر المستخدم ${widget.user.fullName}'
                      : 'فشل في إلغاء حظر المستخدم',
                  ),
                ),
              ],
            ),
            backgroundColor: success ? Colors.green : Colors.grey,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            action: success ? SnackBarAction(
              label: 'تم ✓',
              textColor: Colors.white,
              onPressed: () {},
            ) : null,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text('خطأ في إلغاء حظر المستخدم: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}
