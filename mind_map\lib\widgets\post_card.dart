import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/post.dart';
import '../models/mind_map.dart';
import '../models/reaction.dart';
import '../models/post_report.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/edit_requests_provider.dart';
import '../screens/mind_map_editor_screen.dart';
import '../services/restriction_service.dart';
import 'mind_map_preview_painter.dart';
import 'add_comment_widget.dart';
import 'reaction_picker.dart';
import 'reply_dialog.dart';
import 'post_audience_selector.dart';
import 'restriction_dialog.dart';


class PostCard extends StatefulWidget {
  final Post post;
  final String? highlightCommentId;

  const PostCard({
    super.key,
    required this.post,
    this.highlightCommentId,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> with TickerProviderStateMixin {
  late AnimationController _highlightController;
  late Animation<Color?> _highlightAnimation;
  String? _currentHighlightId;

  @override
  void initState() {
    super.initState();
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _highlightAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.amber.withValues(alpha: 0.4),
    ).animate(CurvedAnimation(
      parent: _highlightController,
      curve: Curves.easeInOut,
    ));

    // بدء التمييز إذا كان هناك تعليق محدد
    if (widget.highlightCommentId != null) {
      _startHighlight(widget.highlightCommentId!);
    }

    // تسجيل مشاهدة المنشور تلقائياً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _recordPostView();
    });
  }

  @override
  void dispose() {
    _highlightController.dispose();
    super.dispose();
  }

  // تسجيل مشاهدة المنشور
  void _recordPostView() {
    try {
      print('🎯 PostCard: محاولة تسجيل مشاهدة للمنشور: ${widget.post.id}');
      final postsProvider = context.read<PostsProvider>();
      postsProvider.recordPostView(widget.post.id);
    } catch (e) {
      // تجاهل الأخطاء في تسجيل المشاهدات
      print('❌ PostCard: خطأ في تسجيل مشاهدة المنشور: $e');
    }
  }

  void _startHighlight(String commentId) {
    setState(() {
      _currentHighlightId = commentId;
    });

    // بدء الأنيميشن
    _highlightController.forward();

    // إيقاف التمييز بعد 5 ثوانٍ
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        _highlightController.reverse().then((_) {
          if (mounted) {
            setState(() {
              _currentHighlightId = null;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المنشور - معلومات المؤلف
          _buildPostHeader(context),

          // محتوى المنشور (معلومات المخطط + الوصف)
          _buildPostContent(context),

          // معاينة المخطط الذهني
          _buildMindMapPreview(context),

          // إحصائيات الإعجابات والتعليقات
          _buildPostStats(context),

          // خط فاصل
          Container(
            height: 1,
            color: Colors.grey.shade200,
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),

          // أزرار التفاعل
          _buildInteractionButtons(context),

          // التعليقات مدمجة في نفس البطاقة
          _buildCommentsSection(context),
        ],
      ),
    );
  }

  Widget _buildPostHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // أيقونة المستخدم
          CircleAvatar(
            backgroundColor: Colors.blue.shade100,
            child: Text(
              _getAuthorInitial(widget.post.authorName),
              style: TextStyle(
                color: Colors.blue.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // معلومات المؤلف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.post.authorName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Tooltip(
                      message: 'مؤلف المنشور',
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade100,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.blue.shade300,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.mic_external_on,
                          size: 12,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Text(
                  widget.post.authorUniversity,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
                // عرض معلومات المحرر إن وجد
                if (widget.post.editedByUserName != null) ...[
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(
                        Icons.edit,
                        size: 14,
                        color: Colors.blue.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'تم تعديله بواسطة ${widget.post.editedByUserName}',
                        style: TextStyle(
                          color: Colors.blue.shade600,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 2),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Text(
                      _formatTimeAgo(widget.post.createdAt),
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                    ),
                    // عرض نوع الجمهور دائماً
                    Tooltip(
                      message: _getAudienceTooltip(widget.post.audience),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: widget.post.audience.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: widget.post.audience.color.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              widget.post.audience.icon,
                              size: 12,
                              color: widget.post.audience.color,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              widget.post.audience.displayName,
                              style: TextStyle(
                                color: widget.post.audience.color,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // عرض المادة الدراسية بجانب نوع الجمهور
                    if (widget.post.mindMapSubject.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.orange.shade300,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          widget.post.mindMapSubject,
                          style: TextStyle(
                            color: Colors.orange.shade700,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          // زر المتابعة وقائمة الخيارات
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر المتابعة (يظهر فقط إذا لم يكن المستخدم يتابع صاحب المنشور)
              _buildFollowButton(context),

              // قائمة الخيارات
              _buildPostMenu(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [


        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان المخطط
              Row(
                children: [
                  const Icon(
                    Icons.account_tree,
                    color: Colors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.post.mindMapTitle,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),



              // وصف المنشور
              if (widget.post.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  widget.post.description,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMindMapPreview(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // معاينة المخطط
            Positioned.fill(
              child: CustomPaint(
                painter: MindMapPreviewPainter(
                  mindMap: MindMap.fromJson(widget.post.mindMapData),
                ),
              ),
            ),
            
            // زر فتح المخطط
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: () => _openMindMap(context),
                  icon: const Icon(Icons.open_in_new),
                  tooltip: 'فتح المخطط',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractionButtons(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUserId = authProvider.user?.uid;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // زر ردود الفعل
          ReactionButton(
            reactions: widget.post.reactions,
            currentUserId: currentUserId,
            onReactionTap: (reactionType) => _toggleReaction(context, reactionType),
            onLongPress: () => _showReactionPicker(context),
          ),
          
          const SizedBox(width: 16),
          
          // زر التعليقات
          InkWell(
            onTap: () {
              // التمرير إلى مربع إضافة التعليق (لا حاجة لشاشة منفصلة)
            },
            borderRadius: BorderRadius.circular(20),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.comment_outlined,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.post.commentsCount}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  // بناء زر المتابعة
  Widget _buildFollowButton(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUserId = authProvider.user?.uid;
        final currentUserModel = authProvider.userModel;

        // إخفاء الزر إذا:
        // 1. المستخدم غير مسجل الدخول
        // 2. المستخدم هو صاحب المنشور نفسه
        // 3. المستخدم يتابع صاحب المنشور بالفعل
        if (currentUserId == null ||
            currentUserId == widget.post.authorId ||
            currentUserModel == null) {
          return const SizedBox.shrink();
        }

        // التحقق من حالة المتابعة
        final isFollowing = currentUserModel.following.contains(widget.post.authorId);

        return Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: authProvider.isUserLoading(widget.post.authorId) ? null : () {
                if (isFollowing) {
                  _unfollowUser(context, authProvider);
                } else {
                  _followUser(context, authProvider);
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isFollowing ? Colors.green.shade50 : Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isFollowing ? Colors.green.shade300 : Colors.blue.shade300,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isFollowing ? Icons.check : Icons.person_add,
                      size: 16,
                      color: isFollowing ? Colors.green.shade700 : Colors.blue.shade700,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      isFollowing ? 'متابع' : 'متابعة',
                      style: TextStyle(
                        color: isFollowing ? Colors.green.shade700 : Colors.blue.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (authProvider.isUserLoading(widget.post.authorId)) ...[
                      const SizedBox(width: 4),
                      SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isFollowing ? Colors.green.shade700 : Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // دالة متابعة المستخدم
  Future<void> _followUser(BuildContext context, AuthProvider authProvider) async {
    // حفظ المعلومات قبل العملية غير المتزامنة
    final authorName = widget.post.authorName;
    final navigator = Navigator.of(context);

    final success = await authProvider.followUser(widget.post.authorId);

    if (!mounted) return;

    if (success) {
      _showSuccessDialog(
        navigator,
        title: 'تم بنجاح!',
        message: 'تم متابعة $authorName بنجاح',
        icon: Icons.check_circle,
        color: Colors.green,
      );
    } else {
      final errorMessage = authProvider.errorMessage ?? 'فشل في المتابعة';
      _showErrorDialog(
        navigator,
        title: 'خطأ في المتابعة',
        message: errorMessage,
      );
    }
  }

  // دالة إلغاء متابعة المستخدم
  Future<void> _unfollowUser(BuildContext context, AuthProvider authProvider) async {
    // حفظ المعلومات قبل العملية غير المتزامنة
    final authorName = widget.post.authorName;
    final navigator = Navigator.of(context);

    final success = await authProvider.unfollowUser(widget.post.authorId);

    if (!mounted) return;

    if (success) {
      _showSuccessDialog(
        navigator,
        title: 'تم بنجاح!',
        message: 'تم إلغاء متابعة $authorName',
        icon: Icons.check_circle,
        color: Colors.blue,
      );
    } else {
      final errorMessage = authProvider.errorMessage ?? 'فشل في إلغاء المتابعة';
      _showErrorDialog(
        navigator,
        title: 'خطأ في إلغاء المتابعة',
        message: errorMessage,
      );
    }
  }

  // عرض حوار النجاح
  void _showSuccessDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
    required IconData icon,
    required Color color,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض حوار الخطأ
  void _showErrorDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 36,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPostMenu(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUserId = authProvider.user?.uid;
    final isOwner = currentUserId == widget.post.authorId;

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            _editPost(context);
            break;
          case 'delete':
            _deletePost(context);
            break;
          case 'request_edit':
            _requestEdit(context);
            break;
          case 'report':
            _reportPost(context);
            break;
        }
      },
      itemBuilder: (context) => [
        if (isOwner) ...[
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit, color: Colors.blue),
              title: Text('تعديل المنشور'),
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('حذف المنشور'),
            ),
          ),
        ] else ...[
          const PopupMenuItem(
            value: 'request_edit',
            child: ListTile(
              leading: Icon(Icons.edit_note, color: Colors.blue),
              title: Text('طلب التعديل'),
            ),
          ),
        ],
        // خيار الإبلاغ متاح للجميع (عدا صاحب المنشور)
        if (!isOwner) ...[
          const PopupMenuItem(
            value: 'report',
            child: ListTile(
              leading: Icon(Icons.report, color: Colors.orange),
              title: Text('الإبلاغ عن المنشور'),
            ),
          ),
        ],
      ],
    );
  }

  // فتح المخطط الذهني
  void _openMindMap(BuildContext context) {
    final authProvider = context.read<AuthProvider>();
    final currentUserId = authProvider.user?.uid;
    final isOwner = currentUserId == widget.post.authorId;

    final mindMap = MindMap.fromJson(widget.post.mindMapData);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MindMapEditorScreen(
          mindMap: mindMap,
          isReadOnly: !isOwner, // وضع القراءة فقط للمستخدمين الآخرين
        ),
      ),
    );
  }

  // تبديل رد الفعل
  void _toggleReaction(BuildContext context, ReactionType reactionType) {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userId = authProvider.user?.uid;
    final userModel = authProvider.userModel;

    if (userId == null || userModel == null) return;

    postsProvider.togglePostReaction(widget.post.id, userId, reactionType, user: userModel);
  }

  // عرض منتقي ردود الفعل
  void _showReactionPicker(BuildContext context) {
    showReactionPicker(
      context: context,
      currentReaction: widget.post.reactions.getUserReaction(
        context.read<AuthProvider>().user?.uid ?? ''
      ),
      onReactionSelected: (reactionType) => _toggleReaction(context, reactionType),
    );
  }



  // تعديل المنشور
  void _editPost(BuildContext context) async {
    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.userModel;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول لتعديل المنشور')),
      );
      return;
    }

    // التحقق من تقييد طلب التعديل والإبلاغ (يشمل تعديل المنشور)
    final restriction = await RestrictionService.checkReportingAndEditRequestRestriction(currentUser.uid);
    if (restriction != null && context.mounted) {
      await RestrictionDialog.show(context, restriction, 'تعديل المنشور');
      return;
    }

    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => EditPostDialog(post: widget.post),
      );
    }
  }

  // طلب التعديل
  void _requestEdit(BuildContext context) async {
    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.userModel;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول لطلب التعديل')),
      );
      return;
    }

    // التحقق من تقييد طلب التعديل والإبلاغ
    final restriction = await RestrictionService.checkReportingAndEditRequestRestriction(currentUser.uid);
    if (restriction != null && context.mounted) {
      await RestrictionDialog.show(context, restriction, 'طلب التعديل');
      return;
    }

    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => RequestEditDialog(post: widget.post),
      );
    }
  }

  // حذف المنشور
  void _deletePost(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنشور'),
        content: const Text('هل أنت متأكد من حذف هذا المنشور؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<PostsProvider>().deletePost(widget.post.id);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // إحصائيات المنشور (الإعجابات والتعليقات والمشاهدات)
  Widget _buildPostStats(BuildContext context) {
    print('📊 عرض إحصائيات المنشور: likes=${widget.post.likesCount}, comments=${widget.post.comments.length}, views=${widget.post.viewsCount}');

    if (widget.post.likesCount == 0 && widget.post.comments.isEmpty && widget.post.viewsCount == 0) {
      print('📊 لا توجد إحصائيات للعرض');
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // الجانب الأيسر - الإعجابات والمشاهدات
          Row(
            children: [
              // إحصائيات الإعجابات
              if (widget.post.likesCount > 0) ...[
                Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.thumb_up,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  '${widget.post.likesCount}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
              ],

              // فاصل بين الإعجابات والمشاهدات
              if (widget.post.likesCount > 0 && widget.post.viewsCount > 0)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade400,
                    shape: BoxShape.circle,
                  ),
                ),

              // إحصائيات المشاهدات
              if (widget.post.viewsCount > 0)
                GestureDetector(
                  onTap: () => _showViewersDialog(context),
                  child: Row(
                    children: [
                      Icon(
                        Icons.visibility,
                        color: Colors.grey.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.post.viewsCount}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),

          // الجانب الأيمن - التعليقات
          if (widget.post.comments.isNotEmpty)
            Text(
              '${widget.post.comments.length} تعليق',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  // قسم التعليقات المدمج
  Widget _buildCommentsSection(BuildContext context) {
    return Column(
      children: [
        // التعليقات الموجودة
        if (widget.post.comments.isNotEmpty) _buildCommentsList(context),

        // مربع إضافة تعليق جديد
        _buildAddCommentBox(context),
      ],
    );
  }

  // قائمة التعليقات
  Widget _buildCommentsList(BuildContext context) {
    // ترتيب التعليقات من الأحدث إلى الأقدم
    final sortedComments = List<PostComment>.from(widget.post.comments)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // عرض أول 5 تعليقات فقط
    final commentsToShow = sortedComments.take(5).toList();
    final hasMoreComments = sortedComments.length > 5;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // عرض التعليقات المحددة
          ...commentsToShow.map((comment) => _buildCommentItem(context, comment)),

          // زر عرض المزيد من التعليقات
          if (hasMoreComments)
            GestureDetector(
              onTap: () => _showAllComments(context),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  'عرض ${sortedComments.length - 5} تعليقات أخرى',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // عرض جميع التعليقات في حوار منبثق
  void _showAllComments(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // عنوان
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'التعليقات (${widget.post.comments.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // قائمة التعليقات القابلة للتمرير
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: widget.post.comments.length,
                  itemBuilder: (context, index) {
                    // ترتيب التعليقات من الأحدث إلى الأقدم
                    final sortedComments = List<PostComment>.from(widget.post.comments)
                      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
                    final comment = sortedComments[index];

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _buildCommentItem(context, comment),
                    );
                  },
                ),
              ),

              // مربع إضافة تعليق في الأسفل
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: Colors.grey.shade200),
                  ),
                ),
                child: AddCommentWidget(post: widget.post),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عنصر تعليق واحد
  Widget _buildCommentItem(BuildContext context, PostComment comment) {
    final isHighlighted = _currentHighlightId == comment.id;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 18,
            backgroundColor: Colors.grey.shade300,
            child: Text(
              _getAuthorInitial(comment.authorName),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 10),

          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AnimatedBuilder(
                  animation: _highlightAnimation,
                  builder: (context, child) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isHighlighted
                            ? Color.lerp(Colors.grey.shade100, _highlightAnimation.value, 0.7)
                            : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(16),
                        border: isHighlighted
                            ? Border.all(color: Colors.amber, width: 2)
                            : null,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            comment.authorName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 13,
                              color: isHighlighted ? Colors.amber.shade800 : Colors.black,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            comment.content,
                            style: TextStyle(
                              fontSize: 14,
                              color: isHighlighted ? Colors.amber.shade700 : Colors.black,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                // أزرار التفاعل مع التعليق
                Padding(
                  padding: const EdgeInsets.only(top: 4, right: 12),
                  child: Row(
                    children: [
                      Text(
                        _formatTime(comment.createdAt),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 16),

                      // زر ردود الفعل للتعليق
                      ReactionButton(
                        reactions: comment.reactions,
                        currentUserId: context.read<AuthProvider>().user?.uid,
                        onReactionTap: (reactionType) => _toggleCommentReaction(context, comment, reactionType),
                        onLongPress: () => _showCommentReactionPicker(context, comment),
                      ),

                      const SizedBox(width: 16),
                      GestureDetector(
                        onTap: () => _showReplyDialog(context, comment),
                        child: Text(
                          'رد',
                          style: TextStyle(
                            color: Colors.blue.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // عرض الردود إذا وجدت
                if (comment.hasReplies) _buildRepliesSection(context, comment),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // مربع إضافة تعليق جديد
  Widget _buildAddCommentBox(BuildContext context) {
    return AddCommentWidget(post: widget.post);
  }

  // تبديل رد الفعل على التعليق
  void _toggleCommentReaction(BuildContext context, PostComment comment, ReactionType reactionType) {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userId = authProvider.user?.uid;
    final userModel = authProvider.userModel;

    if (userId == null || userModel == null) return;

    postsProvider.toggleCommentReaction(widget.post.id, comment.id, userId, reactionType, user: userModel);
  }

  // عرض منتقي ردود الفعل للتعليق
  void _showCommentReactionPicker(BuildContext context, PostComment comment) {
    showReactionPicker(
      context: context,
      currentReaction: comment.reactions.getUserReaction(
        context.read<AuthProvider>().user?.uid ?? ''
      ),
      onReactionSelected: (reactionType) => _toggleCommentReaction(context, comment, reactionType),
    );
  }

  // عرض حوار المشاهدين
  void _showViewersDialog(BuildContext context) {
    // التحقق من أن المستخدم هو مؤلف المنشور
    final postsProvider = context.read<PostsProvider>();
    final currentUserId = postsProvider.currentUserId;

    if (widget.post.authorId != currentUserId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يمكن للمؤلف فقط رؤية قائمة المشاهدين'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: 400, // ارتفاع ثابت
          child: Column(
            children: [
              // عنوان الحوار
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.visibility,
                      color: Colors.blue.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'المشاهدين (${widget.post.viewsCount})',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      color: Colors.grey.shade600,
                    ),
                  ],
                ),
              ),

              // قائمة المشاهدين - ارتفاع ثابت لعرض 4 عناصر
              Expanded(
                child: Container(
                  height: 280, // ارتفاع ثابت لعرض 4 عناصر (70 * 4)
                  child: FutureBuilder<List<Map<String, String>>>(
                    future: postsProvider.getPostViewers(widget.post.id),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      if (snapshot.hasError) {
                        return Center(
                          child: Text(
                            'خطأ في تحميل البيانات',
                            style: TextStyle(color: Colors.red.shade600),
                          ),
                        );
                      }

                      final viewers = snapshot.data ?? [];

                      if (viewers.isEmpty) {
                        return const Center(
                          child: Text(
                            'لا يوجد مشاهدين حتى الآن',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        );
                      }

                      return Column(
                        children: [
                          // مؤشر التمرير إذا كان هناك أكثر من 4 مشاهدين
                          if (viewers.length > 4)
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Colors.grey.shade600,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'مرر لرؤية المزيد',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          // قائمة المشاهدين
                          Expanded(
                            child: ListView.builder(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              itemCount: viewers.length,
                              itemBuilder: (context, index) {
                          final viewer = viewers[index];
                          return Container(
                            height: 70, // ارتفاع ثابت لكل عنصر
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            child: Row(
                              children: [
                                // الصورة الرمزية
                                CircleAvatar(
                                  radius: 24,
                                  backgroundColor: Colors.blue.shade100,
                                  child: Text(
                                    viewer['name']!.isNotEmpty
                                        ? viewer['name']![0].toUpperCase()
                                        : '؟',
                                    style: TextStyle(
                                      color: Colors.blue.shade700,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),

                                // معلومات المستخدم
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        viewer['name']!.isNotEmpty
                                            ? viewer['name']!
                                            : 'مستخدم غير معروف',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      if (viewer['university']!.isNotEmpty)
                                        const SizedBox(height: 4),
                                      if (viewer['university']!.isNotEmpty)
                                        Text(
                                          viewer['university']!,
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 14,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                );
                    },
                  ),
                ),
              ),

              // شريط سفلي يوضح العدد الإجمالي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.people,
                      color: Colors.grey.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'إجمالي المشاهدين: ${widget.post.viewsCount}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض حوار الرد على التعليق
  void _showReplyDialog(BuildContext context, PostComment comment) {
    print('🔄 فتح حوار الرد للتعليق: ${comment.id}');
    showDialog(
      context: context,
      builder: (context) => ReplyDialog(
        post: widget.post,
        parentComment: comment,
      ),
    );
  }

  // بناء قسم الردود
  Widget _buildRepliesSection(BuildContext context, PostComment comment) {
    // عرض أول 3 ردود فقط
    final repliesToShow = comment.replies.take(3).toList();
    final hasMoreReplies = comment.replies.length > 3;

    return Container(
      margin: const EdgeInsets.only(top: 8, right: 40),
      child: Column(
        children: [
          // عرض الردود
          ...repliesToShow.map((reply) => _buildReplyItem(context, reply)),

          // زر عرض المزيد من الردود
          if (hasMoreReplies)
            GestureDetector(
              onTap: () => _showAllReplies(context, comment),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Text(
                  'عرض ${comment.replies.length - 3} ردود أخرى',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // بناء عنصر رد واحد
  Widget _buildReplyItem(BuildContext context, PostComment reply) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم للرد
          CircleAvatar(
            radius: 14,
            backgroundColor: Colors.grey.shade300,
            child: Text(
              _getAuthorInitial(reply.authorName),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 8),

          // محتوى الرد
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reply.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        reply.content,
                        style: const TextStyle(fontSize: 13),
                      ),
                    ],
                  ),
                ),

                // أزرار التفاعل مع الرد
                Padding(
                  padding: const EdgeInsets.only(top: 2, right: 10),
                  child: Row(
                    children: [
                      Text(
                        _formatTime(reply.createdAt),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 11,
                        ),
                      ),
                      const SizedBox(width: 12),

                      // زر ردود الفعل للرد
                      ReactionButton(
                        reactions: reply.reactions,
                        currentUserId: context.read<AuthProvider>().user?.uid,
                        onReactionTap: (reactionType) => _toggleReplyReaction(context, reply, reactionType),
                        onLongPress: () => _showReplyReactionPicker(context, reply),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // عرض جميع الردود
  void _showAllReplies(BuildContext context, PostComment comment) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // عنوان
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'الردود (${comment.replies.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // قائمة الردود القابلة للتمرير
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: comment.replies.length,
                  itemBuilder: (context, index) {
                    final reply = comment.replies[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildReplyItem(context, reply),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تبديل رد الفعل على الرد
  void _toggleReplyReaction(BuildContext context, PostComment reply, ReactionType reactionType) {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userId = authProvider.user?.uid;

    if (userId == null) return;

    postsProvider.toggleReplyReaction(widget.post.id, reply.parentCommentId!, reply.id, userId, reactionType);
  }

  // عرض منتقي ردود الفعل للرد
  void _showReplyReactionPicker(BuildContext context, PostComment reply) {
    showReactionPicker(
      context: context,
      currentReaction: reply.reactions.getUserReaction(
        context.read<AuthProvider>().user?.uid ?? ''
      ),
      onReactionSelected: (reactionType) => _toggleReplyReaction(context, reply, reactionType),
    );
  }

  // تنسيق الوقت
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} ي';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  // تنسيق الوقت
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }

  // الحصول على نص tooltip لنوع الجمهور
  String _getAudienceTooltip(PostAudience audience) {
    switch (audience) {
      case PostAudience.everyone:
        return 'يمكن لأي شخص رؤية هذا المنشور';
      case PostAudience.followersOnly:
        return 'فقط متابعو المؤلف يمكنهم رؤية هذا المنشور';
      case PostAudience.mutualFollowers:
        return 'فقط الأشخاص الذين يتابعون المؤلف ويتابعهم المؤلف';
    }
  }

  // الإبلاغ عن المنشور
  void _reportPost(BuildContext context) async {
    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.userModel;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول للإبلاغ')),
      );
      return;
    }

    // التحقق من تقييد طلب التعديل والإبلاغ
    final restriction = await RestrictionService.checkReportingAndEditRequestRestriction(currentUser.uid);
    if (restriction != null && context.mounted) {
      await RestrictionDialog.show(context, restriction, 'الإبلاغ عن المنشور');
      return;
    }

    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => _ReportPostDialog(
          post: widget.post,
          reporter: currentUser,
        ),
      );
    }
  }
}

// حوار تعديل المنشور
class EditPostDialog extends StatefulWidget {
  final Post post;

  const EditPostDialog({
    super.key,
    required this.post,
  });

  @override
  State<EditPostDialog> createState() => _EditPostDialogState();
}

class _EditPostDialogState extends State<EditPostDialog> {
  late TextEditingController _descriptionController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController(text: widget.post.description);
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  // حساب عرض الحوار حسب حجم الشاشة
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) {
      return 600; // شاشات كبيرة
    } else if (screenWidth > 768) {
      return screenWidth * 0.7; // أجهزة لوحية
    } else {
      return screenWidth * 0.9; // هواتف
    }
  }

  // حساب الحد الأقصى لعرض الحوار
  double _getDialogMaxWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.95;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: _getDialogWidth(context),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: _getDialogMaxWidth(context),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.blue.shade700,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'تعديل المنشور',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
            // عرض عنوان المخطط الذهني (غير قابل للتعديل)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'المخطط الذهني:',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.post.mindMapTitle,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // حقل الوصف
            const Text(
              'الوصف',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: InputDecoration(
                hintText: 'أدخل وصف المنشور (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 4,
            ),
                  ],
                ),
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _updatePost,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade700,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'حفظ التغييرات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updatePost() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final postsProvider = context.read<PostsProvider>();

      await postsProvider.updatePostDescription(
        widget.post.id,
        _descriptionController.text.trim(),
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المنشور بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المنشور: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

// حوار طلب التعديل
class RequestEditDialog extends StatefulWidget {
  final Post post;

  const RequestEditDialog({
    super.key,
    required this.post,
  });

  @override
  State<RequestEditDialog> createState() => _RequestEditDialogState();
}

class _RequestEditDialogState extends State<RequestEditDialog> {
  final _messageController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('طلب التعديل'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المنشور
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المخطط: ${widget.post.mindMapTitle}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (widget.post.mindMapSubject.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text('المادة: ${widget.post.mindMapSubject}'),
                  ],
                  const SizedBox(height: 4),
                  Text('المؤلف: ${widget.post.authorName}'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // رسالة الطلب
            const Text(
              'رسالة الطلب:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                hintText: 'اكتب رسالة توضح سبب طلب التعديل...',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _sendRequest,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('إرسال الطلب'),
        ),
      ],
    );
  }

  void _sendRequest() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة رسالة الطلب')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authProvider = context.read<AuthProvider>();
      final editRequestsProvider = context.read<EditRequestsProvider>();
      final currentUser = authProvider.userModel;

      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      await editRequestsProvider.sendEditRequest(
        fromUser: currentUser,
        toUserId: widget.post.authorId,
        toUserName: widget.post.authorName,
        postId: widget.post.id,
        mindMapId: widget.post.mindMapId,
        mindMapTitle: widget.post.mindMapTitle,
        mindMapSubject: widget.post.mindMapSubject,
        requestMessage: message,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إرسال طلب التعديل بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إرسال الطلب: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

// حوار الإبلاغ عن المنشور
class _ReportPostDialog extends StatefulWidget {
  final Post post;
  final dynamic reporter; // UserModel

  const _ReportPostDialog({
    required this.post,
    required this.reporter,
  });

  @override
  State<_ReportPostDialog> createState() => _ReportPostDialogState();
}

class _ReportPostDialogState extends State<_ReportPostDialog> {
  ReportReason? _selectedReason;
  final TextEditingController _descriptionController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.report, color: Colors.orange),
          SizedBox(width: 8),
          Text('الإبلاغ عن المنشور'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المنشور: ${widget.post.mindMapTitle}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'سبب الإبلاغ:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...ReportReason.values.map((reason) => RadioListTile<ReportReason>(
              title: Text(reason.displayName),
              value: reason,
              groupValue: _selectedReason,
              onChanged: (value) {
                setState(() {
                  _selectedReason = value;
                });
              },
            )),
            const SizedBox(height: 16),
            const Text(
              'تفاصيل إضافية (اختياري):',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                hintText: 'اكتب تفاصيل إضافية عن سبب الإبلاغ...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم مراجعة البلاغ من قبل فريق الإدارة',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading || _selectedReason == null ? null : _submitReport,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('إرسال البلاغ'),
        ),
      ],
    );
  }

  Future<void> _submitReport() async {
    setState(() => _isLoading = true);

    try {
      final report = PostReport(
        postId: widget.post.id,
        reporterId: widget.reporter.uid,
        reporterName: widget.reporter.fullName,
        reason: _selectedReason!,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      // حفظ البلاغ في Firebase
      final database = FirebaseDatabase.instance;
      await database.ref('post_reports/${report.id}').set(report.toJson());

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال البلاغ بنجاح. سيتم مراجعته قريباً.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال البلاغ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
