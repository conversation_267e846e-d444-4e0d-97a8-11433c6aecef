import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/post_card.dart';
import '../widgets/follow_suggestion_card.dart';
import '../models/post.dart';
import '../models/follow_suggestion_model.dart';
import '../services/follow_suggestions_service.dart';

class PostsScreen extends StatefulWidget {
  final String? highlightPostId;
  final String? highlightCommentId;

  const PostsScreen({
    Key? key,
    this.highlightPostId,
    this.highlightCommentId,
  }) : super(key: key);

  @override
  State<PostsScreen> createState() => _PostsScreenState();
}

class _PostsScreenState extends State<PostsScreen> {
  Timer? _announcementTimer;
  String _currentAnnouncement = '';
  bool _showCheckIcon = false;

  // متغيرات الفلترة
  PostAudience? _selectedAudienceFilter;
  List<Post> _filteredPosts = [];

  // متغيرات اقتراحات المتابعة
  List<FollowSuggestionModel> _followSuggestions = [];
  bool _showFollowSuggestions = true;
  int? _suggestionInsertPosition;

  @override
  void initState() {
    super.initState();
    _startAnnouncementTimer();

    // تحميل المنشورات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تعيين context في AuthProvider للإشعارات
      context.read<AuthProvider>().setContext(context);

      context.read<PostsProvider>().loadPosts();
      _loadFollowSuggestions();
    });
  }

  // تحميل اقتراحات المتابعة
  Future<void> _loadFollowSuggestions() async {
    try {
      print('🔄 بدء تحميل اقتراحات المتابعة...');
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.userModel;

      if (currentUser == null) {
        print('❌ لا يوجد مستخدم مسجل دخول');
        return;
      }

      print('👤 المستخدم الحالي: ${currentUser.fullName}');

      final suggestions = await FollowSuggestionsService.getFollowSuggestions(
        currentUserId: currentUser.uid,
        limit: 7, // عرض 7 اقتراحات كحد أقصى
      );

      print('📋 تم جلب ${suggestions.length} اقتراح');

      if (mounted) {
        setState(() {
          _followSuggestions = suggestions;
          _showFollowSuggestions = suggestions.isNotEmpty;
        });
        print('✅ تم تحديث الحالة بـ ${_followSuggestions.length} اقتراح');
      }

    } catch (e) {
      print('❌ خطأ في تحميل اقتراحات المتابعة: $e');
    }
  }



  // فلترة المنشورات حسب نوع الجمهور
  void _filterPosts(List<Post> allPosts) {
    if (_selectedAudienceFilter == null) {
      _filteredPosts = List.from(allPosts);
    } else {
      _filteredPosts = allPosts.where((post) => post.audience == _selectedAudienceFilter).toList();
    }
  }

  // الحصول على عدد المنشورات لنوع جمهور معين
  int _getPostsCountForAudience(List<Post> allPosts, PostAudience? audience) {
    if (audience == null) return allPosts.length;
    return allPosts.where((post) => post.audience == audience).length;
  }

  // تغيير فلتر الجمهور
  void _changeAudienceFilter(PostAudience? newFilter) {
    setState(() {
      _selectedAudienceFilter = newFilter;
    });
  }

  // بناء شريط الفلترة
  Widget _buildFilterBar() {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        return Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(
                Icons.filter_list,
                color: Colors.grey,
                size: 20,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'فلترة حسب الجمهور:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '${_filteredPosts.length} منشور',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        label: 'الكل',
                        count: _getPostsCountForAudience(postsProvider.posts, null),
                        isSelected: _selectedAudienceFilter == null,
                        onTap: () => _changeAudienceFilter(null),
                        icon: Icons.public,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        label: 'للجميع',
                        count: _getPostsCountForAudience(postsProvider.posts, PostAudience.everyone),
                        isSelected: _selectedAudienceFilter == PostAudience.everyone,
                        onTap: () => _changeAudienceFilter(PostAudience.everyone),
                        icon: Icons.public,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        label: 'للمتابعين',
                        count: _getPostsCountForAudience(postsProvider.posts, PostAudience.followersOnly),
                        isSelected: _selectedAudienceFilter == PostAudience.followersOnly,
                        onTap: () => _changeAudienceFilter(PostAudience.followersOnly),
                        icon: Icons.people,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        label: 'متابعين متبادلين',
                        count: _getPostsCountForAudience(postsProvider.posts, PostAudience.mutualFollowers),
                        isSelected: _selectedAudienceFilter == PostAudience.mutualFollowers,
                        onTap: () => _changeAudienceFilter(PostAudience.mutualFollowers),
                        icon: Icons.swap_horiz,
                        color: Colors.purple,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // بناء رقاقة الفلتر
  Widget _buildFilterChip({
    required String label,
    required int count,
    required bool isSelected,
    required VoidCallback onTap,
    required IconData icon,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey.shade600,
              ),
            ),
            if (count > 0) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white.withValues(alpha: 0.2) : color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '$count',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.white : color,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _announcementTimer?.cancel();
    super.dispose();
  }

  // بدء مؤقت التحديث التلقائي
  void _startAnnouncementTimer() {
    _announcementTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        _updateAnnouncement();
      }
    });
    // تحديث فوري عند البداية
    _updateAnnouncement();
  }

  // تحديث نص الإعلان
  void _updateAnnouncement() {
    final postsProvider = context.read<PostsProvider>();
    if (postsProvider.posts.isNotEmpty) {
      final latestPost = postsProvider.posts.first;
      final now = DateTime.now();
      final timeDiff = now.difference(latestPost.createdAt);

      String newAnnouncement;
      if (timeDiff.inMinutes < 1) {
        newAnnouncement = 'تم نشر منشور جديد الآن بواسطة ${latestPost.authorName}';
      } else if (timeDiff.inMinutes < 60) {
        newAnnouncement = 'تم نشر منشور منذ ${timeDiff.inMinutes} دقيقة بواسطة ${latestPost.authorName}';
      } else if (timeDiff.inHours < 24) {
        newAnnouncement = 'تم نشر منشور منذ ${timeDiff.inHours} ساعة بواسطة ${latestPost.authorName}';
      } else {
        newAnnouncement = 'آخر منشور تم نشره بواسطة ${latestPost.authorName}';
      }

      if (mounted && _currentAnnouncement != newAnnouncement) {
        setState(() {
          _currentAnnouncement = newAnnouncement;
          _showCheckIcon = timeDiff.inMinutes < 5; // إظهار علامة الصح للمنشورات الحديثة
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        // تحديث الإعلان عند تغيير المنشورات
        if (postsProvider.posts.isNotEmpty && _currentAnnouncement.isEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateAnnouncement();
          });
        }

        // فلترة المنشورات
        _filterPosts(postsProvider.posts);

        if (postsProvider.isLoading) {
          return _buildLoadingScreen();
        }

        if (postsProvider.error != null) {
          return _buildErrorScreen(postsProvider);
        }

        if (postsProvider.posts.isEmpty) {
          return _buildEmptyScreen();
        }

        if (_filteredPosts.isEmpty && _selectedAudienceFilter != null) {
          return _buildNoFilterResultsScreen();
        }

        return _buildPostsScreen(postsProvider);
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
              strokeWidth: 3,
            ),
            const SizedBox(height: 24),
            Text(
              'جاري تحميل المنشورات...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى الانتظار',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(PostsProvider postsProvider) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red.shade400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'خطأ في تحميل المنشورات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                postsProvider.error!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => postsProvider.loadPosts(),
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.post_add,
                  size: 80,
                  color: Colors.blue.shade300,
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'لا توجد منشورات بعد',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'كن أول من يشارك مخططاً ذهنياً مع المجتمع!',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade500,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () {
                  // الانتقال إلى شاشة المشاريع
                  Navigator.pop(context);
                },
                icon: const Icon(Icons.add_circle_outline),
                label: const Text('إنشاء مخطط ذهني'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoFilterResultsScreen() {
    String filterName = '';
    IconData filterIcon = Icons.filter_list;
    Color filterColor = Colors.grey;

    switch (_selectedAudienceFilter) {
      case PostAudience.everyone:
        filterName = 'للجميع';
        filterIcon = Icons.public;
        filterColor = Colors.green;
        break;
      case PostAudience.followersOnly:
        filterName = 'للمتابعين';
        filterIcon = Icons.people;
        filterColor = Colors.orange;
        break;
      case PostAudience.mutualFollowers:
        filterName = 'للمتابعين المتبادلين';
        filterIcon = Icons.swap_horiz;
        filterColor = Colors.purple;
        break;
      default:
        filterName = 'غير محدد';
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: filterColor.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        filterIcon,
                        size: 80,
                        color: filterColor,
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      'لا توجد منشورات "$filterName"',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'لا توجد منشورات تطابق الفلتر المحدد حالياً',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade500,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsScreen(PostsProvider postsProvider) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterBar(),
          // المحتوى الرئيسي
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // تحديد نوع التخطيط حسب عرض الشاشة
                final isTablet = constraints.maxWidth > 768;
                final isDesktop = constraints.maxWidth > 1200;

                return RefreshIndicator(
                  onRefresh: () => postsProvider.loadPosts(),
                  color: Colors.blue.shade600,
                  child: _buildResponsiveLayout(postsProvider, constraints, isTablet, isDesktop),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveLayout(
    PostsProvider postsProvider,
    BoxConstraints constraints,
    bool isTablet,
    bool isDesktop
  ) {
    if (isDesktop) {
      return _buildDesktopLayout(postsProvider, constraints);
    } else if (isTablet) {
      return _buildTabletLayout(postsProvider, constraints);
    } else {
      return _buildMobileLayout(postsProvider, constraints);
    }
  }

  // تخطيط الهاتف المحمول
  Widget _buildMobileLayout(PostsProvider postsProvider, BoxConstraints constraints) {
    return CustomScrollView(
      slivers: [
        // شريط الإعلانات
        _buildAnnouncementBar(postsProvider),

        // قائمة المنشورات مع اقتراحات المتابعة
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              return _buildListItem(index, constraints);
            },
            childCount: _getListItemCount(),
          ),
        ),

        // مساحة إضافية في النهاية
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  // تخطيط الجهاز اللوحي
  Widget _buildTabletLayout(PostsProvider postsProvider, BoxConstraints constraints) {
    return CustomScrollView(
      slivers: [
        // شريط الإعلانات
        _buildAnnouncementBar(postsProvider),

        // قائمة المنشورات بتخطيط شبكي للأجهزة اللوحية
        SliverPadding(
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(constraints),
          ),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final post = _filteredPosts[index];
                return PostCard(
                  post: post,
                  highlightCommentId: widget.highlightPostId == post.id ? widget.highlightCommentId : null,
                );
              },
              childCount: _filteredPosts.length,
            ),
          ),
        ),

        // مساحة إضافية في النهاية
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  // تخطيط سطح المكتب
  Widget _buildDesktopLayout(PostsProvider postsProvider, BoxConstraints constraints) {
    return Row(
      children: [
        // الشريط الجانبي للإحصائيات (اختياري)
        if (constraints.maxWidth > 1400) _buildSidebar(postsProvider),

        // المحتوى الرئيسي
        Expanded(
          child: CustomScrollView(
            slivers: [
              // شريط الإعلانات
              _buildAnnouncementBar(postsProvider),

              // قائمة المنشورات بتخطيط شبكي للسطح المكتب
              SliverPadding(
                padding: EdgeInsets.symmetric(
                  horizontal: _getHorizontalPadding(constraints),
                ),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: constraints.maxWidth > 1600 ? 3 : 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 20,
                    mainAxisSpacing: 20,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final post = _filteredPosts[index];
                      return PostCard(
                        post: post,
                        highlightCommentId: widget.highlightPostId == post.id ? widget.highlightCommentId : null,
                      );
                    },
                    childCount: _filteredPosts.length,
                  ),
                ),
              ),

              // مساحة إضافية في النهاية
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // حساب المسافة الأفقية حسب حجم الشاشة
  double _getHorizontalPadding(BoxConstraints constraints) {
    if (constraints.maxWidth > 1200) {
      return constraints.maxWidth * 0.1; // 10% من عرض الشاشة للشاشات الكبيرة
    } else if (constraints.maxWidth > 768) {
      return 32.0; // مسافة ثابتة للأجهزة اللوحية
    } else {
      return 16.0; // مسافة صغيرة للهواتف
    }
  }

  // الشريط الجانبي للشاشات الكبيرة جداً
  Widget _buildSidebar(PostsProvider postsProvider) {
    return Container(
      width: 280,
      color: Colors.white,
      child: Column(
        children: [
          // إحصائيات سريعة
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إحصائيات سريعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildStatItem(
                  'إجمالي المنشورات',
                  '${postsProvider.posts.length}',
                  Icons.article,
                  Colors.blue,
                ),
                const SizedBox(height: 12),
                _buildStatItem(
                  'المنشورات المعروضة',
                  '${_filteredPosts.length}',
                  Icons.filter_list,
                  Colors.purple,
                ),
                const SizedBox(height: 12),
                _buildStatItem(
                  'المنشورات اليوم',
                  '${_getTodayPostsCount()}',
                  Icons.today,
                  Colors.green,
                ),
                const SizedBox(height: 12),
                _buildStatItem(
                  'المنشورات الشائعة',
                  '${_getPopularPostsCount()}',
                  Icons.trending_up,
                  Colors.orange,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int _getTodayPostsCount() {
    final today = DateTime.now();
    return _filteredPosts.where((post) {
      return post.createdAt.year == today.year &&
             post.createdAt.month == today.month &&
             post.createdAt.day == today.day;
    }).length;
  }

  int _getPopularPostsCount() {
    return _filteredPosts.where((post) {
      final totalInteractions = post.likes.length +
                               post.reactions.totalCount +
                               post.comments.length;
      return totalInteractions >= 5;
    }).length;
  }

  // بناء شريط الإعلانات
  Widget _buildAnnouncementBar(PostsProvider postsProvider) {
    // إذا لم توجد منشورات، لا تعرض الشريط
    if (postsProvider.posts.isEmpty || _currentAnnouncement.isEmpty) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    return _buildAnnouncementBarContent(
      title: _showCheckIcon ? 'منشور جديد!' : 'آخر الأنشطة',
      subtitle: _currentAnnouncement,
      icon: _showCheckIcon ? Icons.campaign_rounded : Icons.schedule_rounded,
      isNew: _showCheckIcon,
      onTap: () {
        setState(() {
          _showCheckIcon = false;
        });
      },
    );
  }

  // بناء محتوى شريط الإعلانات
  Widget _buildAnnouncementBarContent({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isNew,
    required VoidCallback onTap,
  }) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isNew
                ? [Colors.green.shade400, Colors.green.shade600]
                : [Colors.blue.shade400, Colors.blue.shade600],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: (isNew ? Colors.green : Colors.blue).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // أيقونة الإعلان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // نص الإعلان
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // علامات الحالة
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // علامة الصح للمنشورات الجديدة
                      if (isNew)
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.check_circle_rounded,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),

                      const SizedBox(width: 8),

                      // علامة X للمنشورات القديمة أو زر التحديث
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          isNew ? Icons.refresh_rounded : Icons.schedule_rounded,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // ==================== دوال اقتراحات المتابعة ====================

  /// حساب العدد الإجمالي للعناصر في القائمة (منشورات + اقتراحات)
  int _getListItemCount() {
    int count = _filteredPosts.length;

    // إضافة اقتراحات المتابعة إذا كانت متوفرة
    if (_followSuggestions.isNotEmpty && _showFollowSuggestions) {
      count += 1; // عنصر واحد لاقتراحات المتابعة
    }

    return count;
  }

  /// بناء عنصر القائمة (منشور أو اقتراح متابعة)
  Widget _buildListItem(int index, BoxConstraints constraints) {
    // تحديد موضع إدراج اقتراحات المتابعة
    if (_suggestionInsertPosition == null && _filteredPosts.length > 2 && _followSuggestions.isNotEmpty) {
      _suggestionInsertPosition = FollowSuggestionsService.getRandomInsertPosition(_filteredPosts.length);
    }

    // إذا كان هذا موضع اقتراحات المتابعة
    if (_followSuggestions.isNotEmpty &&
        _showFollowSuggestions &&
        _suggestionInsertPosition != null &&
        index == _suggestionInsertPosition) {

      return FollowSuggestionCard(
        suggestions: _followSuggestions,
        onDismiss: () {
          setState(() {
            _showFollowSuggestions = false;
          });
        },
      );
    }

    // تعديل الفهرس للمنشورات بعد إدراج اقتراحات المتابعة
    int postIndex = index;
    if (_followSuggestions.isNotEmpty &&
        _showFollowSuggestions &&
        _suggestionInsertPosition != null &&
        index > _suggestionInsertPosition!) {
      postIndex = index - 1;
    }

    // التأكد من أن الفهرس صحيح
    if (postIndex >= _filteredPosts.length) {
      return const SizedBox.shrink();
    }

    final post = _filteredPosts[postIndex];
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: _getHorizontalPadding(constraints),
        vertical: 8,
      ),
      child: PostCard(
        post: post,
        highlightCommentId: widget.highlightPostId == post.id ? widget.highlightCommentId : null,
      ),
    );
  }
}
