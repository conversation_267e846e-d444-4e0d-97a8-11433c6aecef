import 'dart:async';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityProvider extends ChangeNotifier {
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  
  bool _isConnected = true;
  bool _hasCheckedInitialConnection = false;
  
  bool get isConnected => _isConnected;
  bool get hasCheckedInitialConnection => _hasCheckedInitialConnection;

  ConnectivityProvider() {
    _initConnectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  // التحقق من الاتصال الأولي
  Future<void> _initConnectivity() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      _updateConnectionStatus(results);
    } catch (e) {
      print('❌ خطأ في فحص الاتصال الأولي: $e');
      _isConnected = false;
    }
    _hasCheckedInitialConnection = true;
    notifyListeners();
  }

  // تحديث حالة الاتصال
  void _updateConnectionStatus(List<ConnectivityResult> results) {
    final bool wasConnected = _isConnected;
    
    // التحقق من وجود أي نوع اتصال
    _isConnected = results.any((result) => 
      result == ConnectivityResult.mobile || 
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet
    );

    print('🌐 حالة الاتصال: ${_isConnected ? "متصل" : "غير متصل"}');
    print('📡 نوع الاتصال: ${results.join(", ")}');

    // إشعار المستمعين فقط إذا تغيرت الحالة
    if (wasConnected != _isConnected) {
      notifyListeners();
    }
  }

  // فحص الاتصال يدوياً
  Future<bool> checkConnection() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      _updateConnectionStatus(results);
      return _isConnected;
    } catch (e) {
      print('❌ خطأ في فحص الاتصال: $e');
      _isConnected = false;
      notifyListeners();
      return false;
    }
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    super.dispose();
  }
}
