import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../providers/mind_map_provider.dart';
import '../providers/edit_requests_provider.dart';
import '../providers/auth_provider.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';
import '../models/mind_map.dart';
import '../models/mind_map_node.dart';
import '../models/mind_map_connection.dart';
import '../widgets/connectivity_wrapper.dart';
import '../models/mind_map_comment.dart';
import '../models/mind_map_template.dart';
import '../widgets/mind_map_canvas.dart';
import '../widgets/node_editor_dialog.dart';
import '../widgets/save_as_template_dialog.dart';
import '../widgets/comments_panel.dart';

class MindMapEditorScreen extends StatefulWidget {
  final MindMap mindMap;
  final String? editRequestId; // معرف طلب التعديل (إن وجد)
  final bool isReadOnly; // وضع القراءة فقط للمعاينة

  const MindMapEditorScreen({
    super.key,
    required this.mindMap,
    this.editRequestId,
    this.isReadOnly = false,
  });

  @override
  State<MindMapEditorScreen> createState() => _MindMapEditorScreenState();
}

class _MindMapEditorScreenState extends State<MindMapEditorScreen> {
  late MindMap _currentMindMap;
  MindMapNode? _selectedNode;
  bool _isEditing = false;
  bool _isConnecting = false;
  bool _showCommentsPanel = false;
  MindMapNode? _connectionStartNode;
  MindMapConnection? _selectedConnection;
  final GlobalKey _canvasKey = GlobalKey();
  Timer? _saveTimer;
  final TransformationController _transformationController = TransformationController();

  @override
  void initState() {
    super.initState();

    // تفعيل وضع التحرير تلقائياً لطلبات التعديل
    if (widget.editRequestId != null) {
      _isEditing = true;
    }

    // إنشاء نسخة جديدة من المخطط لضمان التحديث
    _currentMindMap = MindMap(
      id: widget.mindMap.id,
      title: widget.mindMap.title,
      description: widget.mindMap.description,
      subject: widget.mindMap.subject,
      createdAt: widget.mindMap.createdAt,
      updatedAt: widget.mindMap.updatedAt,
      rootNodeId: widget.mindMap.rootNodeId,
      tags: List.from(widget.mindMap.tags),
      filePath: widget.mindMap.filePath,
    );

    // نسخ العقد
    for (final node in widget.mindMap.nodes.values) {
      _currentMindMap.addNode(node);
    }

    // نسخ الروابط
    for (final connection in widget.mindMap.connections.values) {
      _currentMindMap.addConnection(connection);
    }

    // نسخ التعليقات
    for (final comment in widget.mindMap.comments.values) {
      _currentMindMap.addComment(comment);
    }

    // تحديد المخطط الحالي في الـ Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MindMapProvider>().setCurrentMindMap(_currentMindMap);
      // تركيز تلقائي على جميع العقد بعد تحميل الشاشة
      _fitAllNodes();
    });
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    _transformationController.dispose();
    super.dispose();
  }

  // تركيز تلقائي على جميع العقد
  void _fitAllNodes() {
    if (_currentMindMap.nodes.isEmpty) return;

    // الحصول على حجم الشاشة الفعلي
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width - 100; // مساحة للهوامش الجانبية
    final screenHeight = screenSize.height - 280; // مساحة أكبر للشريط العلوي والسفلي

    // حساب الحدود الدنيا والعليا لجميع العقد
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (final node in _currentMindMap.nodes.values) {
      final left = node.position.dx - node.width / 2;
      final right = node.position.dx + node.width / 2;
      final top = node.position.dy - node.height / 2;
      final bottom = node.position.dy + node.height / 2;

      minX = minX < left ? minX : left;
      maxX = maxX > right ? maxX : right;
      minY = minY < top ? minY : top;
      maxY = maxY > bottom ? maxY : bottom;
    }

    // إضافة هامش حول العقد
    const margin = 50.0;
    minX -= margin;
    maxX += margin;
    minY -= margin;
    maxY += margin;

    // حساب المركز
    final centerX = (minX + maxX) / 2;
    final centerY = (minY + maxY) / 2;

    // حساب المقياس المناسب
    final contentWidth = maxX - minX;
    final contentHeight = maxY - minY;

    final scaleX = screenWidth / contentWidth;
    final scaleY = screenHeight / contentHeight;
    final scale = (scaleX < scaleY ? scaleX : scaleY).clamp(0.3, 1.5);

    // تطبيق التحويل مع ضمان عدم تغطية شريط الأدوات
    setState(() {
      final translateX = -centerX * scale + (screenSize.width / 2);
      final translateY = -centerY * scale + (screenSize.height / 2) - 40; // إزاحة للأسفل قليلاً

      _transformationController.value = Matrix4.identity()
        ..translate(translateX, translateY)
        ..scale(scale);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      child: PopScope(
      onPopInvokedWithResult: (didPop, result) {
        // حفظ قبل الخروج
        context.read<MindMapProvider>().updateMindMap(_currentMindMap);
      },
      child: Scaffold(
      appBar: AppBar(
        title: Text(widget.isReadOnly ? 'معاينة: ${_currentMindMap.title}' : _currentMindMap.title),
        backgroundColor: widget.isReadOnly ? Colors.blue : null,
        foregroundColor: widget.isReadOnly ? Colors.white : null,
        actions: [
          // زر "تم التعديل" لطلبات التعديل
          if (widget.editRequestId != null)
            ElevatedButton.icon(
              onPressed: _submitEditForApproval,
              icon: const Icon(Icons.check_circle, color: Colors.white),
              label: const Text('تم التعديل', style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),

          // Edit mode toggle (إخفاء في وضع القراءة فقط)
          if (widget.editRequestId == null && !widget.isReadOnly) // إخفاء في وضع التعديل ووضع القراءة فقط
            IconButton(
              icon: Icon(_isEditing ? Icons.check : Icons.edit),
              onPressed: () => _toggleEditMode(),
              tooltip: _isEditing ? 'إنهاء التحرير' : 'تحرير',
            ),

          // Connection mode toggle (إخفاء في وضع القراءة فقط)
          if ((_isEditing || widget.editRequestId != null) && !widget.isReadOnly)
            IconButton(
              icon: Icon(_isConnecting ? Icons.link_off : Icons.link),
              onPressed: _toggleConnectionMode,
              tooltip: _isConnecting ? 'إلغاء وضع الربط' : 'وضع ربط العقد',
              color: _isConnecting ? Colors.blue : null,
            ),

          // Comments button (متاح للجميع)
          IconButton(
            icon: Icon(_showCommentsPanel ? Icons.comment : Icons.comment_outlined),
            onPressed: _toggleCommentsPanel,
            tooltip: 'التعليقات',
            color: _showCommentsPanel ? Colors.blue : null,
          ),

          // More options menu
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'info',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('معلومات المخطط'),
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('تصدير'),
                ),
              ),
              // إخفاء خيار "مشاركة" في وضع القراءة فقط
              if (!widget.isReadOnly)
                const PopupMenuItem(
                  value: 'share',
                  child: ListTile(
                    leading: Icon(Icons.share),
                    title: Text('مشاركة'),
                  ),
                ),
              // إخفاء خيار "حفظ كقالب" في وضع القراءة فقط
              if (!widget.isReadOnly)
                const PopupMenuItem(
                  value: 'save_as_template',
                  child: ListTile(
                    leading: Icon(Icons.save_as),
                    title: Text('حفظ كقالب'),
                  ),
                ),
            ],
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Toolbar (when editing and not read-only)
          if (_isEditing && !widget.isReadOnly) _buildToolbar(),

          // Main content area
          Expanded(
            child: Row(
              children: [
                // Canvas
                Expanded(
                  child: RepaintBoundary(
                    key: _canvasKey,
                    child: MindMapCanvas(
                    mindMap: _currentMindMap,
                    isEditing: _isEditing && !widget.isReadOnly,
                    selectedNode: _selectedNode,
                    selectedConnection: _selectedConnection,
                    onNodeSelected: widget.isReadOnly ? (_) {} : _onNodeSelected,
                    onConnectionSelected: widget.isReadOnly ? (_) {} : _onConnectionSelected,
                    onNodeMoved: widget.isReadOnly ? (_, __) {} : _onNodeMoved,
                    onNodeDoubleTap: widget.isReadOnly ? (_) {} : _onNodeDoubleTap,
                    onCanvasTap: widget.isReadOnly ? (_) {} : _onCanvasTap,
                    onConnectionCreated: widget.isReadOnly ? (_, __) {} : _onConnectionCreated,
                    transformationController: _transformationController,
                    showComments: _showCommentsPanel && !widget.isReadOnly,
                    ),
                  ),
                ),

                // Comments panel (when visible)
                if (_showCommentsPanel)
                  CommentsPanel(
                    mindMap: _currentMindMap,
                    onCommentAdded: _onCommentAdded,
                    onCommentUpdated: _onCommentUpdated,
                    onCommentDeleted: _onCommentDeleted,
                    isReadOnly: widget.isReadOnly,
                  ),
              ],
            ),
          ),

          // Bottom info panel (إظهار للجميع)
          _buildBottomPanel(),
        ],
      ),

      // Floating action buttons (hidden in read-only mode)
      floatingActionButton: (_isEditing && !widget.isReadOnly) ? _buildEditingFABs() : null,
      ),
    ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
          // Node tools
          _buildToolButton(
            icon: Icons.add_circle,
            label: 'إضافة',
            onPressed: _addNewNode,
          ),

          _buildToolButton(
            icon: Icons.add_box,
            label: 'فرعية',
            onPressed: _selectedNode != null ? () => _addChildNode(_selectedNode!) : null,
          ),

          _buildToolButton(
            icon: Icons.delete,
            label: 'حذف',
            onPressed: _selectedNode != null ? () => _deleteNode(_selectedNode!) : null,
          ),

          const SizedBox(width: 8),

          _buildToolButton(
            icon: Icons.center_focus_strong,
            label: 'تركيز',
            onPressed: _fitAllNodes,
          ),

          const SizedBox(width: 8),
          Container(
            height: 40,
            width: 1,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
          const SizedBox(width: 8),

          // Connection tools
          _buildToolButton(
            icon: Icons.link,
            label: 'ربط',
            onPressed: _toggleConnectionMode,
            isActive: _isConnecting,
          ),

          _buildToolButton(
            icon: Icons.edit_note,
            label: 'تسمية',
            onPressed: _selectedConnection != null ? () => _editConnectionLabel(_selectedConnection!) : null,
          ),

          _buildToolButton(
            icon: Icons.color_lens,
            label: 'لون الربط',
            onPressed: _selectedConnection != null ? () => _changeConnectionColor(_selectedConnection!) : null,
          ),

          const SizedBox(width: 8),
          Container(
            height: 40,
            width: 1,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
          const SizedBox(width: 8),

          // Color tools
          _buildToolButton(
            icon: Icons.palette,
            label: 'لون',
            onPressed: _selectedNode != null ? () => _changeNodeColor(_selectedNode!) : null,
          ),

          _buildToolButton(
            icon: Icons.text_fields,
            label: 'نص',
            onPressed: _selectedNode != null ? () => _editNodeText(_selectedNode!) : null,
          ),



          const SizedBox(width: 20),

          // Save button
          ElevatedButton.icon(
            onPressed: _saveMindMap,
            icon: const Icon(Icons.save),
            label: const Text('حفظ'),
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomPanel() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Mind map info
          Expanded(
            child: Row(
              children: [
                // أيقونة المخطط
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.account_tree,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),

                const SizedBox(width: 12),

                // معلومات المخطط
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _currentMindMap.title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          // عدد العقد
                          _buildInfoChip(
                            icon: Icons.circle,
                            label: '${_currentMindMap.nodeCount}',
                            tooltip: 'عدد العقد',
                          ),

                          const SizedBox(width: 8),

                          // عدد الروابط
                          _buildInfoChip(
                            icon: Icons.link,
                            label: '${_currentMindMap.connections.length}',
                            tooltip: 'عدد الروابط',
                          ),

                          const SizedBox(width: 8),

                          // التعليقات
                          GestureDetector(
                            onTap: _toggleCommentsPanel,
                            child: _buildInfoChip(
                              icon: _showCommentsPanel ? Icons.comment : Icons.comment_outlined,
                              label: _currentMindMap.hasComments ? '${_currentMindMap.commentCount}' : '0',
                              tooltip: 'التعليقات',
                              isActive: _showCommentsPanel,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // معلومات إضافية
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'آخر تحديث',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  fontSize: 10,
                ),
              ),
              Text(
                _formatDateTime(_currentMindMap.updatedAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Selected node info
          if (_selectedNode != null) ...[
            const VerticalDivider(),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'العقدة المحددة:',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  Text(
                    _selectedNode!.title,
                    style: Theme.of(context).textTheme.titleSmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEditingFABs() {
    return const SizedBox.shrink();
  }

  // تبديل وضع التحرير مع فحص التقييد
  Future<void> _toggleEditMode() async {
    if (!_isEditing) {
      // التحقق من تقييد تعديل المخططات عند تفعيل وضع التحرير
      final authProvider = context.read<AuthProvider>();
      final user = authProvider.userModel;

      if (user != null) {
        final restriction = await RestrictionService.checkEditingMindMapsRestriction(user.uid);
        if (restriction != null && mounted) {
          await RestrictionDialog.show(context, restriction, 'تعديل المخططات الذهنية');
          return;
        }
      }
    }

    if (mounted) {
      setState(() {
        _isEditing = !_isEditing;
      });
    }
  }

  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool isActive = false,
  }) {
    final isEnabled = onPressed != null;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          constraints: const BoxConstraints(minWidth: 60),
          decoration: isActive ? BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            ),
          ) : null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : isEnabled
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.38),
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 9,
                  color: isActive
                      ? Theme.of(context).colorScheme.primary
                      : isEnabled
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.38),
                  fontWeight: isActive ? FontWeight.w600 : null,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onNodeSelected(MindMapNode? node) {
    if (node != null && _isConnecting) {
      _handleConnectionNodeSelection(node);
    } else {
      setState(() {
        _selectedNode = node;
      });
    }
  }

  void _toggleConnectionMode() {
    setState(() {
      _isConnecting = !_isConnecting;
      _connectionStartNode = null;
      if (!_isConnecting) {
        _selectedNode = null;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isConnecting
          ? 'وضع الربط مفعل - اختر عقدتين لربطهما'
          : 'تم إلغاء وضع الربط'),
        duration: const Duration(seconds: 2),
        backgroundColor: _isConnecting ? Colors.blue : Colors.grey,
      ),
    );
  }

  void _handleConnectionNodeSelection(MindMapNode node) {
    if (_connectionStartNode == null) {
      // اختيار العقدة الأولى
      setState(() {
        _connectionStartNode = node;
        _selectedNode = node;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم اختيار العقدة الأولى - اختر العقدة الثانية'),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.orange,
        ),
      );
    } else if (_connectionStartNode!.id != node.id) {
      // إنشاء الاتصال
      _createConnection(_connectionStartNode!, node);
      setState(() {
        _connectionStartNode = null;
        _selectedNode = null;
        _isConnecting = false;
      });
    }
  }

  void _createConnection(MindMapNode fromNode, MindMapNode toNode) {
    final connection = MindMapConnection(
      fromNodeId: fromNode.id,
      toNodeId: toNode.id,
      label: '',
      color: Colors.blue,
      thickness: 2.0,
      type: ConnectionType.curved,
      arrowStyle: ArrowStyle.simple,
    );

    setState(() {
      _currentMindMap.addConnection(connection);
    });

    // حفظ التغييرات
    context.read<MindMapProvider>().updateMindMap(_currentMindMap);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم ربط "${fromNode.title}" بـ "${toNode.title}"'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _onNodeMoved(MindMapNode node, Offset newPosition) {
    setState(() {
      final updatedNode = node.copyWith(position: newPosition);
      _currentMindMap.updateNode(node.id, updatedNode);
      // تحديث العقدة المحددة أيضاً
      if (_selectedNode?.id == node.id) {
        _selectedNode = updatedNode;
      }
    });

    // تأخير الحفظ لتحسين الأداء أثناء السحب
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 300), () {
      context.read<MindMapProvider>().updateMindMap(_currentMindMap);
    });
  }

  void _onNodeDoubleTap(MindMapNode node) {
    _editNodeText(node);
  }

  void _onCanvasTap(Offset position) {
    if (_isEditing) {
      setState(() {
        _selectedNode = null;
      });
    }
  }

  void _onConnectionCreated(MindMapNode parent, MindMapNode child) {
    setState(() {
      final updatedChild = child.copyWith(parentId: parent.id);
      _currentMindMap.updateNode(child.id, updatedChild);
      parent.addChild(child.id);
    });

    // حفظ التغييرات
    context.read<MindMapProvider>().updateMindMap(_currentMindMap);
  }

  void _addNewNode() {
    final nodeCount = _currentMindMap.nodes.length;

    // مواقع قريبة من المركز لضمان الرؤية بدون تحريك الكاميرا
    late Offset position;

    if (nodeCount == 0) {
      // العقدة الأولى في المركز
      position = const Offset(400, 300);
    } else {
      // العقد التالية في مواقع قريبة ومرئية
      final patterns = [
        const Offset(600, 200),   // يمين أعلى
        const Offset(200, 200),   // يسار أعلى
        const Offset(600, 400),   // يمين أسفل
        const Offset(200, 400),   // يسار أسفل
        const Offset(400, 100),   // أعلى
        const Offset(400, 500),   // أسفل
        const Offset(700, 300),   // يمين
        const Offset(100, 300),   // يسار
      ];

      position = patterns[(nodeCount - 1) % patterns.length];

      // إضافة تباعد صغير للعقد الإضافية
      if (nodeCount > patterns.length) {
        final extraOffset = ((nodeCount - 1) ~/ patterns.length) * 50.0;
        position = Offset(position.dx + extraOffset, position.dy + extraOffset);
      }
    }

    final newNode = MindMapNode(
      title: 'عقدة ${nodeCount + 1}',
      position: position,
      color: _getRandomNodeColor(),
    );

    setState(() {
      _currentMindMap.addNode(newNode);
      _selectedNode = newNode;
    });

    // حفظ التغييرات
    context.read<MindMapProvider>().updateMindMap(_currentMindMap);

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${newNode.title}'),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.green,
      ),
    );
  }

  Color _getRandomNodeColor() {
    final index = DateTime.now().millisecondsSinceEpoch % _availableColors.length;
    return _availableColors[index];
  }

  void _addChildNode(MindMapNode parent) {
    // موقع العقدة الفرعية قريب من الأب ومرئي
    final childPosition = Offset(
      parent.position.dx + 120,
      parent.position.dy + 80,
    );
    
    final childNode = MindMapNode(
      title: 'عقدة فرعية',
      position: childPosition,
      parentId: parent.id,
    );
    
    setState(() {
      _currentMindMap.addChildNode(parent.id, childNode);
      _selectedNode = childNode;
    });

    // حفظ التغييرات تلقائياً
    context.read<MindMapProvider>().updateMindMap(_currentMindMap);
  }

  void _deleteNode(MindMapNode node) async {
    // التحقق من تقييد تعديل المخططات
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.userModel;

    if (user != null) {
      final restriction = await RestrictionService.checkEditingMindMapsRestriction(user.uid);
      if (restriction != null && mounted) {
        await RestrictionDialog.show(context, restriction, 'تعديل المخططات الذهنية');
        return;
      }
    }

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العقدة'),
        content: Text('هل أنت متأكد من حذف العقدة "${node.title}"؟\nسيتم حذف جميع العقد الفرعية أيضاً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _currentMindMap.removeNode(node.id);
                _selectedNode = null;
              });

              // حفظ التغييرات
              context.read<MindMapProvider>().updateMindMap(_currentMindMap);

              Navigator.pop(context);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _editNodeText(MindMapNode node) async {
    // التحقق من تقييد تعديل المخططات
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.userModel;

    if (user != null) {
      final restriction = await RestrictionService.checkEditingMindMapsRestriction(user.uid);
      if (restriction != null && mounted) {
        await RestrictionDialog.show(context, restriction, 'تعديل المخططات الذهنية');
        return;
      }
    }

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => NodeEditorDialog(
        node: node,
        onSave: (updatedNode) {
          setState(() {
            _currentMindMap.updateNode(node.id, updatedNode);
          });
          // حفظ التغييرات
          context.read<MindMapProvider>().updateMindMap(_currentMindMap);
        },
      ),
    );
  }

  void _changeNodeColor(MindMapNode node) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('اختر لون العقدة'),
          content: SizedBox(
            width: 300,
            height: 200,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: _availableColors.length,
              itemBuilder: (context, index) {
                final color = _availableColors[index];
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      final updatedNode = node.copyWith(color: color);
                      _currentMindMap.updateNode(node.id, updatedNode);
                      if (_selectedNode?.id == node.id) {
                        _selectedNode = updatedNode;
                      }
                    });

                    // حفظ التغييرات
                    context.read<MindMapProvider>().updateMindMap(_currentMindMap);

                    Navigator.of(context).pop();

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تغيير لون العقدة'),
                        duration: Duration(seconds: 1),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: color == node.color ? Colors.white : Colors.transparent,
                        width: 3,
                      ),
                    ),
                    child: color == node.color
                        ? const Icon(Icons.check, color: Colors.white)
                        : null,
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  List<Color> get _availableColors => [
    const Color(0xFF2196F3), // أزرق
    const Color(0xFF4CAF50), // أخضر
    const Color(0xFFFF9800), // برتقالي
    const Color(0xFF9C27B0), // بنفسجي
    const Color(0xFFF44336), // أحمر
    const Color(0xFF00BCD4), // سماوي
    const Color(0xFFFFEB3B), // أصفر
    const Color(0xFF795548), // بني
    const Color(0xFF607D8B), // رمادي مزرق
    const Color(0xFFE91E63), // وردي
    const Color(0xFF3F51B5), // أزرق داكن
    const Color(0xFF8BC34A), // أخضر فاتح
    const Color(0xFFFF5722), // برتقالي محمر
    const Color(0xFF673AB7), // بنفسجي داكن
    const Color(0xFF009688), // أخضر مزرق
    const Color(0xFFFFC107), // كهرماني
  ];

  void _saveMindMap() {
    context.read<MindMapProvider>().updateMindMap(_currentMindMap);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ المخطط الذهني بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'info':
        _showMindMapInfo();
        break;
      case 'export':
        _exportMindMap();
        break;
      case 'share':
        _shareMindMap();
        break;
      case 'save_as_template':
        _saveAsTemplate();
        break;
    }
  }

  void _showMindMapInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات المخطط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العنوان: ${_currentMindMap.title}'),
            if (_currentMindMap.description.isNotEmpty)
              Text('الوصف: ${_currentMindMap.description}'),
            if (_currentMindMap.subject.isNotEmpty)
              Text('المادة: ${_currentMindMap.subject}'),
            Text('عدد العقد: ${_currentMindMap.nodeCount}'),
            Text('تاريخ الإنشاء: ${_formatDate(_currentMindMap.createdAt)}'),
            Text('آخر تحديث: ${_formatDate(_currentMindMap.updatedAt)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _exportMindMap() async {
    try {
      // إظهار تأكيد التصدير
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('تصدير المخطط الذهني'),
            content: const Text('سيتم إنشاء ملف PDF وفتحه مباشرة. هل تريد المتابعة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('إنشاء وفتح PDF'),
              ),
            ],
          );
        },
      );

      if (confirmed != true) return;

      // إظهار مؤشر التحميل
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      // إنشاء ملف PDF
      final pdf = await _generatePDF();

      // الحصول على مجلد مؤقت للفتح المباشر
      Directory directory;
      try {
        // استخدام مجلد مؤقت للفتح السريع
        directory = await getTemporaryDirectory();
      } catch (e) {
        // في حالة فشل الوصول للمجلد المؤقت، استخدم مجلد التطبيق
        directory = await getApplicationDocumentsDirectory();
      }

      final fileName = '${_currentMindMap.title}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      // إخفاء مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      // فتح الملف مباشرة
      try {
        final result = await OpenFile.open(file.path);

        if (mounted) {
          if (result.type == ResultType.done) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم فتح ملف PDF بنجاح'),
                duration: Duration(seconds: 2),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // في حالة فشل الفتح، عرض خيارات أخرى
            final action = await showDialog<String>(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('تم إنشاء PDF'),
                  content: Text('لا يمكن فتح الملف تلقائياً.\nتم حفظه في: ${file.path}'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop('ok'),
                      child: const Text('موافق'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop('share'),
                      child: const Text('مشاركة'),
                    ),
                  ],
                );
              },
            );

            if (action == 'share') {
              try {
                await Share.shareXFiles(
                  [XFile(file.path)],
                  text: 'المخطط الذهني: ${_currentMindMap.title}',
                );
              } catch (shareError) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في المشاركة: $shareError'),
                      duration: const Duration(seconds: 3),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            }
          }
        }
      } catch (openError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ الملف لكن فشل في فتحه: $openError'),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التصدير: $e'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }


  Future<Uint8List> _captureCanvasImage() async {
    try {
      // العثور على RenderRepaintBoundary
      RenderRepaintBoundary boundary = _canvasKey.currentContext!.findRenderObject() as RenderRepaintBoundary;

      // أخذ صورة بجودة عالية جداً لوضوح النصوص
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);

      // تحويل الصورة إلى bytes بجودة عالية (PNG بدون ضغط)
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      return byteData!.buffer.asUint8List();
    } catch (e) {
      print('خطأ في أخذ صورة المخطط: $e');
      rethrow;
    }
  }

  // دالة تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  // دالة لبناء رقائق المعلومات
  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String tooltip,
    bool isActive = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.15)
              : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
          border: isActive
              ? Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
                  width: 1,
                )
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دوال إدارة التعليقات
  Future<void> _toggleCommentsPanel() async {
    if (!_showCommentsPanel) {
      // التحقق من تقييد التعليق عند فتح لوحة التعليقات
      final authProvider = context.read<AuthProvider>();
      final user = authProvider.userModel;

      if (user != null) {
        final restriction = await RestrictionService.checkCommentingRestriction(user.uid);
        if (restriction != null && mounted) {
          await RestrictionDialog.show(context, restriction, 'التعليق');
          return;
        }
      }
    }

    if (mounted) {
      setState(() {
        _showCommentsPanel = !_showCommentsPanel;
      });
    }
  }

  void _onCommentAdded(MindMapComment comment) {
    setState(() {
      _currentMindMap.addComment(comment);
    });
    _saveMindMap();
  }

  void _onCommentUpdated(MindMapComment comment) {
    setState(() {
      _currentMindMap.updateComment(comment);
    });
    _saveMindMap();
  }

  void _onCommentDeleted(String commentId) {
    setState(() {
      _currentMindMap.removeComment(commentId);
    });
    _saveMindMap();
  }

  Future<pw.Document> _generatePDF() async {
    final pdf = pw.Document();

    // تحميل الخط العربي
    final fontData = await rootBundle.load("fonts/Amiri-Regular.ttf");
    final boldFontData = await rootBundle.load("fonts/Amiri-Bold.ttf");
    final arabicFont = pw.Font.ttf(fontData);
    final arabicBoldFont = pw.Font.ttf(boldFontData);

    // أخذ صورة من المخطط الذهني
    final mindMapImage = await _captureCanvasImage();

    // إضافة صفحة للمخطط
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10), // تقليل الهوامش
        theme: pw.ThemeData.withFont(
          base: arabicFont,
          bold: arabicBoldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // عنوان المخطط
              pw.Text(
                _currentMindMap.title.isNotEmpty ? _currentMindMap.title : 'المخطط الذهني',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
                textDirection: pw.TextDirection.rtl,
              ),

              pw.SizedBox(height: 5),

              // معلومات مختصرة في سطر واحد
              pw.Text(
                'عدد العقد: ${_currentMindMap.nodes.length} | عدد الروابط: ${_currentMindMap.connections.length} | ${_formatDate(_currentMindMap.updatedAt)}',
                style: const pw.TextStyle(fontSize: 9),
                textDirection: pw.TextDirection.rtl,
                textAlign: pw.TextAlign.center,
              ),

              pw.SizedBox(height: 5),

              // صورة المخطط الذهني
              pw.Expanded(
                child: pw.Container(
                  width: double.infinity,
                  height: double.infinity,
                  child: pw.Image(
                    pw.MemoryImage(mindMapImage),
                    fit: pw.BoxFit.fill, // يملأ كامل المساحة بدون قطع
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }







  void _shareMindMap() async {
    try {
      // إنشاء نص مختصر للمشاركة
      final summary = _generateShareText();

      await Share.share(
        summary,
        subject: 'المخطط الذهني: ${_currentMindMap.title}',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المشاركة: $e'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _generateShareText() {
    final buffer = StringBuffer();
    buffer.writeln('📋 المخطط الذهني: ${_currentMindMap.title}');
    buffer.writeln('📅 تاريخ الإنشاء: ${_formatDate(_currentMindMap.createdAt)}');
    buffer.writeln('🔢 عدد العقد: ${_currentMindMap.nodes.length}');

    if (_currentMindMap.connections.isNotEmpty) {
      buffer.writeln('🔗 عدد الروابط: ${_currentMindMap.connections.length}');
    }

    buffer.writeln('\n📝 العقد الرئيسية:');

    final rootNodes = _currentMindMap.nodes.values
        .where((node) => node.parentId == null)
        .take(5) // أول 5 عقد فقط للمشاركة
        .toList();

    for (int i = 0; i < rootNodes.length; i++) {
      buffer.writeln('${i + 1}. ${rootNodes[i].title}');
    }

    if (_currentMindMap.nodes.length > 5) {
      buffer.writeln('... و ${_currentMindMap.nodes.length - 5} عقدة أخرى');
    }

    buffer.writeln('\n🧠 تم إنشاؤه باستخدام تطبيق المخططات الذهنية');

    return buffer.toString();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _saveAsTemplate() async {
    final template = await showDialog<MindMapTemplate>(
      context: context,
      builder: (context) => SaveAsTemplateDialog(mindMap: _currentMindMap),
    );

    if (template != null) {
      // هنا يمكن حفظ القالب في قاعدة البيانات أو التخزين المحلي
      // في الوقت الحالي سنعرض رسالة نجاح فقط

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ القالب "${template.name}" بنجاح'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'عرض',
              textColor: Colors.white,
              onPressed: () {
                // يمكن إضافة منطق لعرض القالب المحفوظ
              },
            ),
          ),
        );
      }
    }
  }

  // دالة تحرير تسمية الربط
  void _editConnectionLabel(MindMapConnection connection) {
    final TextEditingController labelController = TextEditingController(text: connection.label);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحرير تسمية الربط'),
        content: TextField(
          controller: labelController,
          decoration: const InputDecoration(
            labelText: 'التسمية',
            hintText: 'أدخل تسمية للربط',
            border: OutlineInputBorder(),
          ),
          maxLength: 50,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final newLabel = labelController.text.trim();
              setState(() {
                connection.label = newLabel;
                _currentMindMap.updatedAt = DateTime.now();
              });
              Navigator.of(context).pop();

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث تسمية الربط'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // دالة تغيير لون الربط
  void _changeConnectionColor(MindMapConnection connection) {
    final List<Color> colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.brown,
      Colors.grey,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار لون الربط'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              final isSelected = connection.color == color;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    connection.color = color;
                    _currentMindMap.updatedAt = DateTime.now();
                  });
                  Navigator.of(context).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تغيير لون الربط'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(color: Colors.black, width: 3)
                        : Border.all(color: Colors.grey, width: 1),
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white)
                      : null,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  // دالة اختيار الربط
  void _onConnectionSelected(MindMapConnection? connection) {
    setState(() {
      _selectedConnection = connection;
      _selectedNode = null; // إلغاء تحديد العقدة
    });
  }

  // إرسال التعديل للموافقة
  Future<void> _submitEditForApproval() async {
    if (widget.editRequestId == null) return;

    try {
      // الحصول على المزودات قبل العمليات غير المتزامنة
      final mindMapProvider = context.read<MindMapProvider>();
      final editRequestsProvider = context.read<EditRequestsProvider>();

      // حفظ التغييرات أولاً (حفظ المخطط المعدل مؤقتاً)
      await mindMapProvider.addMindMap(_currentMindMap);

      // إرسال للموافقة مع بيانات المخطط المعدل
      await editRequestsProvider.submitForApproval(
        widget.editRequestId!,
        editedMindMapData: _currentMindMap.toJson(),
      );

      if (mounted) {
        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال التعديل لصاحب المنشور للموافقة'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للشاشة السابقة مع إشارة النجاح
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال التعديل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
