import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart' as firestore;
import 'package:firebase_database/firebase_database.dart';
import '../models/user_model.dart';
import '../services/ban_monitor_service.dart';
import '../screens/banned_user_screen.dart';
import 'mind_map_provider.dart';
import '../utils/admin_helper.dart';
import '../services/restriction_service.dart';

class AuthProvider extends ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final MindMapProvider? _mindMapProvider;



  // Callback للإشعارات
  Function(String targetUserId, UserModel follower)? onUserFollowed;

  User? _user;
  UserModel? _userModel;
  bool _isLoading = false;
  bool _isLoggingOut = false;
  String? _errorMessage;

  // حالات التحميل المنفصلة لكل مستخدم
  final Map<String, bool> _userLoadingStates = {};
  StreamSubscription<DatabaseEvent>? _userDataSubscription;
  BuildContext? _context; // لحفظ context للتنقل والإشعارات

  AuthProvider({MindMapProvider? mindMapProvider}) : _mindMapProvider = mindMapProvider {
    _initializeAuth();
  }

  /// تعيين BuildContext للتنقل والإشعارات
  void setContext(BuildContext context) {
    _context = context;
  }

  /// عرض شاشة الحظر
  void _showBannedScreen(UserModel bannedUser) {
    if (_context != null && _context!.mounted) {
      Navigator.of(_context!).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => BannedUserScreen(
            banReason: bannedUser.banReason,
            bannedAt: bannedUser.bannedAt,
            bannedBy: bannedUser.bannedBy,
          ),
        ),
        (route) => false, // إزالة جميع الشاشات السابقة
      );
    }
  }



  @override
  void dispose() {
    _userDataSubscription?.cancel();
    super.dispose();
  }

  // Getters
  User? get user => _user;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  bool get isLoggingOut => _isLoggingOut;
  String? get errorMessage => _errorMessage;
  bool get isSignedIn => _user != null;
  bool get isAdmin => _userModel?.isAdmin ?? false;

  void _initializeAuth() {
    // التحقق من المستخدم الحالي فور التهيئة
    _user = FirebaseAuth.instance.currentUser;
    if (_user != null && !_user!.isAnonymous) {
      _loadUserData(_user!.uid).then((_) {
        _mindMapProvider?.setCurrentUser(_user!.uid);
        notifyListeners();
      });
    } else {
      notifyListeners();
    }

    // الاستماع لتغييرات حالة المصادقة
    FirebaseAuth.instance.authStateChanges().listen((User? user) async {
      _user = user;
      if (user != null && !user.isAnonymous) {
        // تحميل بيانات المستخدم من Firebase
        await _loadUserData(user.uid);

        // التحقق من حظر المستخدم
        if (_userModel?.isBanned == true) {
          // إيقاف مراقبة الحظر
          BanMonitorService.stopMonitoring();

          // تسجيل خروج المستخدم المحظور تلقائياً
          await FirebaseAuth.instance.signOut();
          _user = null;
          _userModel = null;
          await _mindMapProvider?.setCurrentUser(null);
        } else {
          // تعيين المستخدم في MindMapProvider
          await _mindMapProvider?.setCurrentUser(user.uid);

          // بدء مراقبة حالة الحظر
          BanMonitorService.startMonitoring(user.uid, onUserBanned: _showBannedScreen);
        }
      } else {
        _userModel = null;
        // تعيين null للضيوف
        await _mindMapProvider?.setCurrentUser(null);
      }
      notifyListeners();
    });
  }

  // تسجيل الدخول كضيف
  Future<bool> signInAnonymously() async {
    try {
      _setLoading(true);
      _clearError();

      final userCredential = await FirebaseAuth.instance.signInAnonymously();
      _user = userCredential.user;

      // تعيين المستخدم في MindMapProvider (null للضيوف)
      await _mindMapProvider?.setCurrentUser(null);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في تسجيل الدخول: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تسجيل الخروج
  Future<void> signOut() async {
    try {
      _isLoggingOut = true;
      _clearError();
      notifyListeners();

      // تأخير قصير لإظهار شاشة التحميل
      await Future.delayed(const Duration(milliseconds: 500));

      // إيقاف مراقبة الحظر أولاً
      BanMonitorService.stopMonitoring();

      // إلغاء الاستماع لتحديثات بيانات المستخدم أولاً
      await _userDataSubscription?.cancel();
      _userDataSubscription = null;

      // مسح بيانات المستخدم محلياً أولاً
      _user = null;
      _userModel = null;

      // مسح بيانات المستخدم من MindMapProvider
      await _mindMapProvider?.setCurrentUser(null);

      // تأخير إضافي لضمان إلغاء جميع الاستماعات
      await Future.delayed(const Duration(milliseconds: 500));

      // تسجيل الخروج من Firebase
      await FirebaseAuth.instance.signOut();
      _user = null;
      _userModel = null;

      // تأخير نهائي لإظهار رسالة النجاح
      await Future.delayed(const Duration(seconds: 1));

      notifyListeners();
    } catch (e) {
      _setError('فشل في تسجيل الخروج: ${e.toString()}');
    } finally {
      _isLoggingOut = false;
      notifyListeners();
    }
  }

  // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final userCredential = await FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password);

      _user = userCredential.user;

      // تحميل بيانات المستخدم للتحقق من الحظر
      if (_user != null) {
        await _loadUserData(_user!.uid);

        // التحقق من حظر المستخدم
        if (_userModel?.isBanned == true) {
          final banReason = _userModel?.banReason ?? "غير محدد";

          // إيقاف مراقبة الحظر
          BanMonitorService.stopMonitoring();

          // تسجيل خروج المستخدم المحظور
          await FirebaseAuth.instance.signOut();
          _user = null;
          _userModel = null;

          // رسالة خطأ مخصصة للحظر
          _setError('🚫 حسابك محظور من قبل الإدارة\n\n📋 سبب الحظر: $banReason\n\n📞 للاستفسار، يرجى التواصل مع الإدارة');
          return false;
        }

        // تعيين المستخدم في MindMapProvider
        await _mindMapProvider?.setCurrentUser(_user!.uid);

        // بدء مراقبة حالة الحظر
        BanMonitorService.startMonitoring(_user!.uid, onUserBanned: _showBannedScreen);
      }

      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'لا يوجد مستخدم بهذا البريد الإلكتروني';
          break;
        case 'wrong-password':
          errorMessage = 'كلمة المرور غير صحيحة';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        case 'user-disabled':
          errorMessage = 'تم تعطيل هذا الحساب';
          break;
        default:
          errorMessage = 'خطأ في تسجيل الدخول: ${e.message}';
      }
      _setError(errorMessage);
      return false;
    } catch (e) {
      _setError('خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إنشاء حساب جديد
  Future<bool> createUserWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);

      _user = userCredential.user;

      // تعيين المستخدم في MindMapProvider
      if (_user != null) {
        await _mindMapProvider?.setCurrentUser(_user!.uid);
      }

      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      switch (e.code) {
        case 'weak-password':
          errorMessage = 'كلمة المرور ضعيفة جداً';
          break;
        case 'email-already-in-use':
          errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMessage = 'خطأ في إنشاء الحساب: ${e.message}';
      }
      _setError(errorMessage);
      return false;
    } catch (e) {
      _setError('خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إعادة تعيين كلمة المرور
  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      return true;
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'لا يوجد مستخدم بهذا البريد الإلكتروني';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMessage = 'خطأ في إرسال رابط إعادة التعيين: ${e.message}';
      }
      _setError(errorMessage);
      return false;
    } catch (e) {
      _setError('خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إنشاء حساب جديد مع البيانات الإضافية
  Future<bool> createUserWithCompleteProfile({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String university,
    required String major,
    String bio = '',
    required DateTime birthDate,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // إنشاء الحساب في Firebase Auth
      final userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);

      if (userCredential.user != null) {
        // إنشاء نموذج المستخدم
        final userModel = UserModel(
          uid: userCredential.user!.uid,
          email: email,
          firstName: firstName,
          lastName: lastName,
          university: university,
          major: major,
          bio: bio,
          birthDate: birthDate,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          lastActiveAt: DateTime.now(),
          following: [],
          followers: [],
        );

        // حفظ البيانات في Realtime Database
        await _database
            .ref('users/${userCredential.user!.uid}')
            .set(userModel.toMap());

        _user = userCredential.user;
        _userModel = userModel;
        notifyListeners();
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      switch (e.code) {
        case 'weak-password':
          errorMessage = 'كلمة المرور ضعيفة جداً';
          break;
        case 'email-already-in-use':
          errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMessage = 'خطأ في إنشاء الحساب: ${e.message}';
      }
      _setError(errorMessage);
      return false;
    } catch (e) {
      _setError('خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحميل بيانات المستخدم من Realtime Database
  Future<void> _loadUserData(String uid) async {
    try {
      print('🔄 تحميل بيانات المستخدم من Realtime Database: $uid');

      // التحقق من وجود مستخدم مسجل دخول
      if (FirebaseAuth.instance.currentUser == null) {
        print('⚠️ لا يوجد مستخدم مسجل دخول - إيقاف تحميل البيانات');
        return;
      }

      // إلغاء الاستماع السابق إن وجد
      await _userDataSubscription?.cancel();

      final DatabaseReference userRef = _database.ref('users/$uid');

      // الاستماع للتحديثات الفورية
      _userDataSubscription = userRef.onValue.listen(
        (DatabaseEvent event) {
          final DataSnapshot snapshot = event.snapshot;
          _handleUserDataSnapshot(snapshot);
        },
        onError: (error) {
          print('خطأ في الاستماع لبيانات المستخدم: $error');
          // في حالة خطأ الصلاحيات، أوقف الاستماع
          if (error.toString().contains('permission-denied')) {
            print('⚠️ لا توجد صلاحيات للوصول لبيانات المستخدم - إيقاف الاستماع');
            _userDataSubscription?.cancel();
            _userDataSubscription = null;
          }
        },
      );

      // تحميل البيانات مرة واحدة أولاً
      final DatabaseEvent event = await userRef.once();
      final DataSnapshot snapshot = event.snapshot;

      if (snapshot.exists && snapshot.value != null) {
        final userData = Map<String, dynamic>.from(snapshot.value as Map);
        _userModel = UserModel.fromMap(userData);
        print('✅ تم تحميل بيانات المستخدم: ${_userModel?.email}');

        // فحص وتعيين صلاحيات المدير إذا لزم الأمر
        if (_userModel != null && AdminHelper.isAdminEmail(_userModel!.email) && !_userModel!.isAdmin) {
          await AdminHelper.makeUserAdmin(_userModel!.uid, _userModel!.email);
          // إعادة تحميل البيانات للحصول على التحديث
          await _loadUserData(_userModel!.uid);
          return;
        }

        notifyListeners(); // إضافة notifyListeners
      } else {
        print('⚠️ لا توجد بيانات للمستخدم في Realtime Database');
        print('🔄 محاولة ترحيل البيانات من Firestore...');

        // محاولة جلب البيانات من Firestore وترحيلها
        await _migrateUserFromFirestore(uid);
      }
    } catch (e) {
      print('❌ خطأ في تحميل بيانات المستخدم: $e');
      // في حالة عدم وجود أذونات أو خطأ آخر، نتجاهل الخطأ
      // ونترك _userModel كـ null
      _userModel = null;
      notifyListeners();
    }
  }

  // معالجة تحديثات بيانات المستخدم
  void _handleUserDataSnapshot(DataSnapshot snapshot) {
    try {
      if (snapshot.exists && snapshot.value != null) {
        final userData = Map<String, dynamic>.from(snapshot.value as Map);
        final newUserModel = UserModel.fromMap(userData);

        // التحقق من وجود تغييرات قبل التحديث
        if (_userModel == null ||
            _userModel!.followersCount != newUserModel.followersCount ||
            _userModel!.followingCount != newUserModel.followingCount ||
            _userModel!.followers != newUserModel.followers ||
            _userModel!.following != newUserModel.following ||
            _userModel!.lastActiveAt != newUserModel.lastActiveAt ||
            _userModel!.showOnlineStatus != newUserModel.showOnlineStatus ||
            _userModel!.showLastSeen != newUserModel.showLastSeen) {

          _userModel = newUserModel;
          print('🔄 تم تحديث بيانات المستخدم فورياً - متابعين: ${_userModel!.followersCount}, نشط: ${_userModel!.isActive}');
          notifyListeners();
        }
      }
    } catch (e) {
      print('❌ خطأ في معالجة تحديث بيانات المستخدم: $e');
    }
  }

  // تحديث بيانات المستخدم
  Future<bool> updateUserProfile({
    String? firstName,
    String? lastName,
    String? university,
    String? major,
    String? bio,
    DateTime? birthDate,
  }) async {
    if (_user == null || _userModel == null) return false;

    try {
      _setLoading(true);
      _clearError();

      final updatedUserModel = _userModel!.copyWith(
        firstName: firstName,
        lastName: lastName,
        university: university,
        major: major,
        bio: bio,
        birthDate: birthDate,
        updatedAt: DateTime.now(),
      );

      await _database
          .ref('users/${_user!.uid}')
          .update(updatedUserModel.toMap());

      _userModel = updatedUserModel;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في تحديث البيانات: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث آخر نشاط للمستخدم مع تحسين الأداء
  DateTime? _lastUpdateTime;
  Future<void> updateLastActive() async {
    if (_user == null || _user!.isAnonymous || _userModel == null) return;

    try {
      final now = DateTime.now();

      // تجنب التحديث المتكرر - تحديث كل 30 ثانية فقط
      if (_lastUpdateTime != null &&
          now.difference(_lastUpdateTime!).inSeconds < 30) {
        return;
      }

      _lastUpdateTime = now;

      final updatedUserModel = _userModel!.copyWith(
        lastActiveAt: now,
        updatedAt: now,
      );

      await _database
          .ref('users/${_user!.uid}')
          .update({
        'lastActiveAt': now.toIso8601String(),
        'updatedAt': now.toIso8601String(),
      });

      _userModel = updatedUserModel;
      notifyListeners();
    } catch (e) {
      // تجاهل الأخطاء في تحديث آخر نشاط لعدم إزعاج المستخدم
      print('خطأ في تحديث آخر نشاط: $e');
    }
  }

  // متابعة مستخدم باستخدام Realtime Database
  Future<bool> followUser(String targetUserId) async {
    if (_user == null || _user!.isAnonymous || _userModel == null) return false;
    if (targetUserId == _user!.uid) return false; // لا يمكن متابعة النفس

    // التحقق من تقييد المتابعة
    final restriction = await RestrictionService.checkFollowingRestriction(_user!.uid);
    if (restriction != null) {
      _setError('أنت مقيد من متابعة المستخدمين');
      return false;
    }

    try {
      setUserLoading(targetUserId, true);
      _clearError();

      // التحقق من إعدادات خصوصية المستخدم المستهدف
      final targetUserRef = _database.ref('users/$targetUserId');
      final targetSnapshot = await targetUserRef.once();

      if (targetSnapshot.snapshot.exists) {
        final targetData = Map<String, dynamic>.from(targetSnapshot.snapshot.value as Map);
        final allowFollowing = targetData['allowFollowing'] ?? true;

        if (!allowFollowing) {
          _setError('هذا المستخدم لا يسمح بالمتابعة');
          return false;
        }
      }

      // إضافة المستخدم المستهدف إلى قائمة المتابَعين
      final updatedFollowing = List<String>.from(_userModel!.following);
      if (!updatedFollowing.contains(targetUserId)) {
        updatedFollowing.add(targetUserId);
      }

      // تحديث بيانات المستخدم الحالي
      final currentUserRef = _database.ref('users/${_user!.uid}');
      await currentUserRef.update({
        'following': updatedFollowing,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      // تحديث قائمة متابعي المستخدم المستهدف (استخدام البيانات المحصل عليها مسبقاً)
      if (targetSnapshot.snapshot.exists) {
        final targetData = Map<String, dynamic>.from(targetSnapshot.snapshot.value as Map);
        final targetFollowers = _parseStringList(targetData['followers']);

        if (!targetFollowers.contains(_user!.uid)) {
          targetFollowers.add(_user!.uid);
        }

        // تحديث قائمة متابعي المستخدم المستهدف
        await targetUserRef.update({
          'followers': targetFollowers,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      // تحديث النموذج المحلي
      _userModel = _userModel!.copyWith(
        following: updatedFollowing,
        updatedAt: DateTime.now(),
      );

      notifyListeners();

      // إنشاء إشعار متابعة جديدة
      if (onUserFollowed != null && _userModel != null) {
        try {
          onUserFollowed!(targetUserId, _userModel!);
        } catch (e) {
          print('❌ خطأ في إنشاء إشعار المتابعة: $e');
        }
      }

      return true;
    } catch (e) {
      _setError('خطأ في المتابعة: ${e.toString()}');
      return false;
    } finally {
      clearUserLoading(targetUserId);
    }
  }

  // إلغاء متابعة مستخدم باستخدام Realtime Database
  Future<bool> unfollowUser(String targetUserId) async {
    if (_user == null || _user!.isAnonymous || _userModel == null) return false;

    try {
      setUserLoading(targetUserId, true);
      _clearError();

      // إزالة المستخدم المستهدف من قائمة المتابَعين
      final updatedFollowing = List<String>.from(_userModel!.following);
      updatedFollowing.remove(targetUserId);

      // تحديث بيانات المستخدم الحالي
      final currentUserRef = _database.ref('users/${_user!.uid}');
      await currentUserRef.update({
        'following': updatedFollowing,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      // الحصول على قائمة متابعي المستخدم المستهدف
      final targetUserRef = _database.ref('users/$targetUserId');
      final targetSnapshot = await targetUserRef.once();

      if (targetSnapshot.snapshot.exists) {
        final targetData = Map<String, dynamic>.from(targetSnapshot.snapshot.value as Map);
        final targetFollowers = _parseStringList(targetData['followers']);

        targetFollowers.remove(_user!.uid);

        // تحديث قائمة متابعي المستخدم المستهدف
        await targetUserRef.update({
          'followers': targetFollowers,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      // تحديث النموذج المحلي
      _userModel = _userModel!.copyWith(
        following: updatedFollowing,
        updatedAt: DateTime.now(),
      );

      notifyListeners();

      return true;
    } catch (e) {
      _setError('خطأ في إلغاء المتابعة: ${e.toString()}');
      return false;
    } finally {
      clearUserLoading(targetUserId);
    }
  }

  // البحث عن المستخدمين باستخدام Realtime Database
  Future<List<UserModel>> searchUsers({
    String? query,
    String? university,
    int limit = 50,
  }) async {
    try {
      print('🔍 بدء البحث عن المستخدمين في Realtime Database...');
      print('📝 النص المطلوب: $query');
      print('🏫 الجامعة: $university');

      // جلب جميع المستخدمين من Realtime Database
      final DatabaseReference usersRef = _database.ref('users');

      print('📡 جلب البيانات من Realtime Database...');
      final DatabaseEvent event = await usersRef.once();
      final DataSnapshot snapshot = event.snapshot;

      if (!snapshot.exists || snapshot.value == null) {
        print('⚠️ لا توجد بيانات في users node');
        return [];
      }

      final Map<dynamic, dynamic> usersData = snapshot.value as Map<dynamic, dynamic>;
      print('📊 تم جلب ${usersData.length} مستخدم من Realtime Database');

      List<UserModel> users = [];

      for (var entry in usersData.entries) {
        try {
          final userData = Map<String, dynamic>.from(entry.value as Map);
          print('👤 معالجة مستخدم: ${userData['email'] ?? 'بدون إيميل'}');

          final user = UserModel.fromMap(userData);

          // استبعاد المستخدم الحالي
          if (user.uid != _user?.uid) {
            users.add(user);
          }
        } catch (e) {
          print('❌ خطأ في معالجة مستخدم: $e');
          continue;
        }
      }

      print('✅ تم معالجة ${users.length} مستخدم بنجاح');

      // فلترة حسب الجامعة إذا تم تحديدها
      if (university != null && university.isNotEmpty && university != 'جميع الجامعات') {
        print('🎯 فلترة حسب الجامعة: $university');
        users = users.where((user) => user.university == university).toList();
        print('🏫 بعد فلترة الجامعة: ${users.length} مستخدم');
      }

      // فلترة حسب النص إذا تم تحديده (البحث المحلي)
      if (query != null && query.isNotEmpty) {
        print('🔎 تطبيق فلتر البحث النصي: $query');
        final searchQuery = query.toLowerCase().trim();

        users = users.where((user) {
          final fullName = user.fullName.toLowerCase();
          final email = user.email.toLowerCase();
          final university = user.university.toLowerCase();
          final major = user.major.toLowerCase();

          return fullName.contains(searchQuery) ||
                 email.contains(searchQuery) ||
                 university.contains(searchQuery) ||
                 major.contains(searchQuery);
        }).toList();

        print('🎯 تم العثور على ${users.length} نتيجة مطابقة');
      }

      // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
      users.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // تحديد عدد النتائج
      if (users.length > limit) {
        users = users.take(limit).toList();
      }

      print('📋 إرجاع ${users.length} مستخدم');
      return users;
    } catch (e) {
      print('💥 خطأ في البحث: $e');
      _setError('خطأ في البحث: ${e.toString()}');
      return [];
    }
  }

  // الحصول على بيانات مستخدم معين من Realtime Database
  Future<UserModel?> getUserById(String userId) async {
    try {
      final DatabaseReference userRef = _database.ref('users/$userId');
      final DatabaseEvent event = await userRef.once();
      final DataSnapshot snapshot = event.snapshot;

      if (snapshot.exists && snapshot.value != null) {
        final userData = Map<String, dynamic>.from(snapshot.value as Map);
        return UserModel.fromMap(userData);
      }
      return null;
    } catch (e) {
      _setError('خطأ في جلب بيانات المستخدم: ${e.toString()}');
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // تحديث نموذج المستخدم مباشرة دون تشغيل حالة التحميل العامة
  void updateUserModelDirectly(UserModel updatedUserModel) {
    _userModel = updatedUserModel;
    notifyListeners();
  }

  // التحكم في حالة التحميل لمستخدم معين
  void setUserLoading(String userId, bool loading) {
    _userLoadingStates[userId] = loading;
    notifyListeners();
  }

  // الحصول على حالة التحميل لمستخدم معين
  bool isUserLoading(String userId) {
    return _userLoadingStates[userId] ?? false;
  }

  // مسح حالة التحميل لمستخدم معين
  void clearUserLoading(String userId) {
    _userLoadingStates.remove(userId);
    notifyListeners();
  }



  void clearError() {
    _clearError();
  }

  // إعادة تحميل بيانات المستخدم
  Future<void> reloadUserData() async {
    if (_user != null && !_user!.isAnonymous) {
      await _loadUserData(_user!.uid);
    }
  }

  // ترحيل مستخدم واحد من Firestore إلى Realtime Database
  Future<void> _migrateUserFromFirestore(String uid) async {
    try {
      // جلب بيانات المستخدم من Firestore
      final firestoreDoc = await firestore.FirebaseFirestore.instance
          .collection('users')
          .doc(uid)
          .get();

      if (firestoreDoc.exists && firestoreDoc.data() != null) {
        final userData = firestoreDoc.data()!;
        print('📦 تم العثور على بيانات في Firestore: ${userData['email']}');

        // حفظ البيانات في Realtime Database
        await _database.ref('users/$uid').set(userData);
        print('✅ تم ترحيل البيانات إلى Realtime Database');

        // تحميل البيانات المرحلة
        _userModel = UserModel.fromMap(userData);
        notifyListeners();
      } else {
        print('⚠️ لا توجد بيانات في Firestore أيضاً');
        _userModel = null;
        notifyListeners();
      }
    } catch (e) {
      print('❌ خطأ في ترحيل البيانات: $e');
      _userModel = null;
      notifyListeners();
    }
  }

  // ترحيل البيانات من Firestore إلى Realtime Database
  Future<void> migrateDataFromFirestore() async {
    try {
      print('🔄 بدء ترحيل البيانات من Firestore إلى Realtime Database...');

      // جلب جميع المستخدمين من Firestore
      final firestoreSnapshot = await firestore.FirebaseFirestore.instance
          .collection('users')
          .get();

      if (firestoreSnapshot.docs.isEmpty) {
        print('⚠️ لا توجد بيانات في Firestore للترحيل');
        return;
      }

      print('📊 تم العثور على ${firestoreSnapshot.docs.length} مستخدم في Firestore');

      // ترحيل كل مستخدم إلى Realtime Database
      for (var doc in firestoreSnapshot.docs) {
        try {
          final userData = doc.data();
          final userId = doc.id;

          // حفظ البيانات في Realtime Database
          await _database.ref('users/$userId').set(userData);
          print('✅ تم ترحيل المستخدم: ${userData['email']}');
        } catch (e) {
          print('❌ خطأ في ترحيل المستخدم ${doc.id}: $e');
        }
      }

      print('🎉 تم الانتهاء من ترحيل البيانات بنجاح!');
    } catch (e) {
      print('💥 خطأ في ترحيل البيانات: $e');
      _setError('خطأ في ترحيل البيانات: ${e.toString()}');
    }
  }

  // تغيير كلمة المرور
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _setError('المستخدم غير مسجل الدخول');
        return false;
      }

      // التحقق من كلمة المرور الحالية
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      // إعادة المصادقة
      await user.reauthenticateWithCredential(credential);

      // تغيير كلمة المرور
      await user.updatePassword(newPassword);

      print('✅ تم تغيير كلمة المرور بنجاح');
      return true;
    } on FirebaseAuthException catch (e) {
      print('❌ خطأ في تغيير كلمة المرور: ${e.code}');

      String errorMessage;
      switch (e.code) {
        case 'wrong-password':
          errorMessage = 'كلمة المرور الحالية غير صحيحة';
          break;
        case 'weak-password':
          errorMessage = 'كلمة المرور الجديدة ضعيفة جداً';
          break;
        case 'requires-recent-login':
          errorMessage = 'يجب إعادة تسجيل الدخول لتغيير كلمة المرور';
          break;
        case 'too-many-requests':
          errorMessage = 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً';
          break;
        default:
          errorMessage = 'حدث خطأ أثناء تغيير كلمة المرور: ${e.message}';
      }

      _setError(errorMessage);
      return false;
    } catch (e) {
      print('💥 خطأ غير متوقع في تغيير كلمة المرور: $e');
      _setError('حدث خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث إعداد خصوصية واحد
  void updatePrivacySetting(String setting, bool value) {
    if (_userModel == null) return;

    // إنشاء نسخة محدثة من UserModel
    Map<String, dynamic> updates = {};
    updates[setting] = value;

    _userModel = _userModel!.copyWith(
      isProfilePublic: setting == 'isProfilePublic' ? value : _userModel!.isProfilePublic,
      showEmail: setting == 'showEmail' ? value : _userModel!.showEmail,
      showUniversity: setting == 'showUniversity' ? value : _userModel!.showUniversity,
      showMajor: setting == 'showMajor' ? value : _userModel!.showMajor,
      defaultPublicMindMaps: setting == 'defaultPublicMindMaps' ? value : _userModel!.defaultPublicMindMaps,
      allowComments: setting == 'allowComments' ? value : _userModel!.allowComments,
      allowCopying: setting == 'allowCopying' ? value : _userModel!.allowCopying,
      allowFollowing: setting == 'allowFollowing' ? value : _userModel!.allowFollowing,
      showFollowers: setting == 'showFollowers' ? value : _userModel!.showFollowers,
      showFollowing: setting == 'showFollowing' ? value : _userModel!.showFollowing,
      showFollowersToFollowersOnly: setting == 'showFollowersToFollowersOnly' ? value : _userModel!.showFollowersToFollowersOnly,
      showFollowingToFollowersOnly: setting == 'showFollowingToFollowersOnly' ? value : _userModel!.showFollowingToFollowersOnly,
      notifyOnFollow: setting == 'notifyOnFollow' ? value : _userModel!.notifyOnFollow,
      notifyOnComment: setting == 'notifyOnComment' ? value : _userModel!.notifyOnComment,
      notifyOnReaction: setting == 'notifyOnReaction' ? value : _userModel!.notifyOnReaction,
      notifyFollowersOnNewPost: setting == 'notifyFollowersOnNewPost' ? value : _userModel!.notifyFollowersOnNewPost,
      // إعدادات خصوصية النشاط
      showOnlineStatus: setting == 'showOnlineStatus' ? value : _userModel!.showOnlineStatus,
      showLastSeen: setting == 'showLastSeen' ? value : _userModel!.showLastSeen,
      // إعدادات خصوصية المنشورات
      postsVisibility: setting == 'postsVisibility' ? value.toString() : _userModel!.postsVisibility,
    );

    notifyListeners();
  }

  // حفظ إعدادات الخصوصية
  Future<bool> savePrivacySettings() async {
    try {
      _setLoading(true);
      _clearError();

      if (_user == null || _userModel == null) {
        _setError('المستخدم غير مسجل الدخول');
        return false;
      }

      // تحديث بيانات المستخدم في Firebase
      final userRef = _database.ref('users/${_user!.uid}');
      await userRef.update(_userModel!.toMap());

      print('✅ تم حفظ إعدادات الخصوصية بنجاح');
      return true;
    } catch (e) {
      print('💥 خطأ في حفظ إعدادات الخصوصية: $e');
      _setError('حدث خطأ أثناء حفظ إعدادات الخصوصية: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// دالة مساعدة لتحويل البيانات إلى قائمة نصوص بأمان
  List<String> _parseStringList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.where((item) => item != null).map((item) => item.toString()).toList();
    } else if (data is Map) {
      return data.keys.where((key) => key != null).map((key) => key.toString()).toList();
    } else {
      return [];
    }
  }
}
