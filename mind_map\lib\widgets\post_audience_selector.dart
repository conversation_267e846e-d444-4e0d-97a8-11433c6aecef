import 'package:flutter/material.dart';
import '../models/post.dart';

class PostAudienceSelector extends StatefulWidget {
  final PostAudience initialAudience;
  final Function(PostAudience) onAudienceChanged;
  final Future<int> Function()? getTotalUsersCount;
  final int? followersCount;
  final int? mutualFollowersCount;

  const PostAudienceSelector({
    Key? key,
    this.initialAudience = PostAudience.everyone,
    required this.onAudienceChanged,
    this.getTotalUsersCount,
    this.followersCount,
    this.mutualFollowersCount,
  }) : super(key: key);

  @override
  State<PostAudienceSelector> createState() => _PostAudienceSelectorState();
}

class _PostAudienceSelectorState extends State<PostAudienceSelector> {
  late PostAudience selectedAudience;
  int? totalUsersCount;
  bool isLoadingTotalUsers = false;

  @override
  void initState() {
    super.initState();
    selectedAudience = widget.initialAudience;
    _loadTotalUsersCount();
  }

  Future<void> _loadTotalUsersCount() async {
    if (widget.getTotalUsersCount != null) {
      setState(() {
        isLoadingTotalUsers = true;
      });

      try {
        final count = await widget.getTotalUsersCount!();
        setState(() {
          totalUsersCount = count;
          isLoadingTotalUsers = false;
        });
      } catch (e) {
        setState(() {
          totalUsersCount = null;
          isLoadingTotalUsers = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Icon(Icons.people, color: Colors.blue.shade700, size: 24),
              const SizedBox(width: 12),
              const Text(
                'اختر جمهور المنشور',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // خيارات الجمهور
          _buildAudienceOption(
            audience: PostAudience.everyone,
            icon: Icons.public,
            title: 'للجميع',
            subtitle: 'يمكن لأي شخص رؤية هذا المنشور',
            color: Colors.green,
            count: isLoadingTotalUsers ? null : totalUsersCount,
            isLoading: isLoadingTotalUsers,
          ),
          const SizedBox(height: 12),

          _buildAudienceOption(
            audience: PostAudience.followersOnly,
            icon: Icons.people,
            title: 'لمتابعيني فقط',
            subtitle: 'فقط الأشخاص الذين يتابعونك يمكنهم رؤية هذا المنشور',
            color: Colors.blue,
            count: widget.followersCount,
            isLoading: false,
          ),
          const SizedBox(height: 12),

          _buildAudienceOption(
            audience: PostAudience.mutualFollowers,
            icon: Icons.people_alt,
            title: 'للمتابعين المتبادلين',
            subtitle: 'فقط الأشخاص الذين تتابعهم ويتابعونك',
            color: Colors.purple,
            count: widget.mutualFollowersCount,
            isLoading: false,
          ),
          
          const SizedBox(height: 24),
          
          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('إلغاء'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    widget.onAudienceChanged(selectedAudience);
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade700,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('تأكيد'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAudienceOption({
    required PostAudience audience,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    int? count,
    bool isLoading = false,
  }) {
    final isSelected = selectedAudience == audience;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedAudience = audience;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected ? color : Colors.grey.shade400,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 6),
                  // عداد المشاهدين
                  _buildViewerCount(count, isLoading, color),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: color,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  // بناء عداد المشاهدين
  Widget _buildViewerCount(int? count, bool isLoading, Color color) {
    if (isLoading) {
      return Row(
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ),
          const SizedBox(width: 6),
          Text(
            'جاري الحساب...',
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      );
    }

    if (count == null) {
      return Text(
        'غير متاح',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey.shade500,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.visibility,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            _getCountText(count),
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على نص العداد
  String _getCountText(int count) {
    if (count == 0) {
      return 'لا يوجد مشاهدون';
    } else if (count == 1) {
      return 'مشاهد واحد';
    } else if (count == 2) {
      return 'مشاهدان';
    } else if (count <= 10) {
      return '$count مشاهدين';
    } else {
      return '$count مشاهداً';
    }
  }
}

// دالة مساعدة لعرض الشاشة المنبثقة
Future<PostAudience?> showPostAudienceSelector({
  required BuildContext context,
  PostAudience initialAudience = PostAudience.everyone,
  Future<int> Function()? getTotalUsersCount,
  int? followersCount,
  int? mutualFollowersCount,
}) async {
  PostAudience? selectedAudience;
  
  await showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return PostAudienceSelector(
        initialAudience: initialAudience,
        onAudienceChanged: (audience) {
          selectedAudience = audience;
        },
        getTotalUsersCount: getTotalUsersCount,
        followersCount: followersCount,
        mutualFollowersCount: mutualFollowersCount,
      );
    },
  );
  
  return selectedAudience;
}

// دوال مساعدة للحصول على معلومات الجمهور
extension PostAudienceExtension on PostAudience {
  String get displayName {
    switch (this) {
      case PostAudience.everyone:
        return 'للجميع';
      case PostAudience.followersOnly:
        return 'لمتابعيني فقط';
      case PostAudience.mutualFollowers:
        return 'للمتابعين المتبادلين';
    }
  }

  IconData get icon {
    switch (this) {
      case PostAudience.everyone:
        return Icons.public;
      case PostAudience.followersOnly:
        return Icons.people;
      case PostAudience.mutualFollowers:
        return Icons.people_alt;
    }
  }

  Color get color {
    switch (this) {
      case PostAudience.everyone:
        return Colors.green;
      case PostAudience.followersOnly:
        return Colors.blue;
      case PostAudience.mutualFollowers:
        return Colors.purple;
    }
  }
}
