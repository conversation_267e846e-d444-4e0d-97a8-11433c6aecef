import 'package:flutter/material.dart';
import '../models/mind_map.dart';
import '../models/mind_map_comment.dart';

class AddCommentDialog extends StatefulWidget {
  final MindMap mindMap;
  final Function(MindMapComment) onCommentAdded;
  final String? preSelectedNodeId;

  const AddCommentDialog({
    super.key,
    required this.mindMap,
    required this.onCommentAdded,
    this.preSelectedNodeId,
  });

  @override
  State<AddCommentDialog> createState() => _AddCommentDialogState();
}

class _AddCommentDialogState extends State<AddCommentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _contentController = TextEditingController();
  CommentType _selectedType = CommentType.general;
  String? _selectedNodeId;

  @override
  void initState() {
    super.initState();
    _selectedNodeId = widget.preSelectedNodeId;
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة تعليق جديد'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Content field
                TextFormField(
                  controller: _contentController,
                  decoration: const InputDecoration(
                    labelText: 'محتوى التعليق *',
                    hintText: 'اكتب تعليقك أو ملاحظتك هنا...',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 4,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال محتوى التعليق';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Comment type
                DropdownButtonFormField<CommentType>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع التعليق',
                    border: OutlineInputBorder(),
                  ),
                  items: CommentType.values.map((type) {
                    final typeColor = Color(int.parse(type.color.substring(1), radix: 16) + 0xFF000000);
                    return DropdownMenuItem<CommentType>(
                      value: type,
                      child: Row(
                        children: [
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: typeColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: typeColor.withValues(alpha: 0.5)),
                            ),
                            child: Center(
                              child: Text(
                                type.icon,
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(type.displayName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value!;
                    });
                  },
                ),

                const SizedBox(height: 16),

                // Node attachment
                DropdownButtonFormField<String?>(
                  value: _selectedNodeId,
                  decoration: const InputDecoration(
                    labelText: 'ربط بعقدة (اختياري)',
                    border: OutlineInputBorder(),
                    helperText: 'يمكنك ربط التعليق بعقدة معينة في المخطط',
                  ),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('تعليق عام (غير مرتبط بعقدة)'),
                    ),
                    ...widget.mindMap.nodes.values.map((node) {
                      return DropdownMenuItem<String?>(
                        value: node.id,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.account_tree,
                              size: 16,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                node.title.length > 30
                                    ? '${node.title.substring(0, 30)}...'
                                    : node.title,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedNodeId = value;
                    });
                  },
                ),

                const SizedBox(height: 16),

                // Preview
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.preview,
                            size: 16,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'معاينة التعليق',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      _buildCommentPreview(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _addComment,
          child: const Text('إضافة التعليق'),
        ),
      ],
    );
  }

  Widget _buildCommentPreview() {
    final typeColor = Color(int.parse(_selectedType.color.substring(1), radix: 16) + 0xFF000000);
    final content = _contentController.text.trim();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Type badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: typeColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: typeColor.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(_selectedType.icon),
              const SizedBox(width: 4),
              Text(
                _selectedType.displayName,
                style: TextStyle(
                  color: typeColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Content
        Text(
          content.isEmpty ? 'محتوى التعليق سيظهر هنا...' : content,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: content.isEmpty 
                ? Theme.of(context).colorScheme.outline 
                : null,
            fontStyle: content.isEmpty ? FontStyle.italic : null,
          ),
        ),

        const SizedBox(height: 8),

        // Node attachment info
        if (_selectedNodeId != null) ...[
          Row(
            children: [
              Icon(
                Icons.link,
                size: 14,
                color: Theme.of(context).colorScheme.outline,
              ),
              const SizedBox(width: 4),
              Text(
                'مرتبط بـ: ${_getNodeName(_selectedNodeId!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
            ],
          ),
        ] else ...[
          Row(
            children: [
              Icon(
                Icons.public,
                size: 14,
                color: Theme.of(context).colorScheme.outline,
              ),
              const SizedBox(width: 4),
              Text(
                'تعليق عام',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  String _getNodeName(String nodeId) {
    final node = widget.mindMap.nodes[nodeId];
    if (node == null) return 'عقدة غير موجودة';

    String nodeName = node.title.isEmpty ? 'عقدة بدون عنوان' : node.title;

    // إضافة معلومات عن العقد المرتبطة
    String additionalInfo = '';
    if (node.parentId != null) {
      final parentNode = widget.mindMap.nodes[node.parentId!];
      if (parentNode != null) {
        String parentName = parentNode.title.isEmpty ? 'عقدة بدون عنوان' : parentNode.title;
        if (parentName.length > 8) parentName = '${parentName.substring(0, 8)}...';
        additionalInfo = ' ← $parentName';
      }
    } else if (node.childrenIds.isNotEmpty) {
      additionalInfo = ' (${node.childrenIds.length} فرع)';
    }

    String fullName = nodeName + additionalInfo;
    return fullName.length > 30 ? '${fullName.substring(0, 30)}...' : fullName;
  }

  void _addComment() {
    if (_formKey.currentState!.validate()) {
      final comment = MindMapComment(
        content: _contentController.text.trim(),
        type: _selectedType,
        nodeId: _selectedNodeId,
      );

      widget.onCommentAdded(comment);
      Navigator.pop(context);

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة التعليق بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
