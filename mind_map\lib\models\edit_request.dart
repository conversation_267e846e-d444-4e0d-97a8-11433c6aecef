import 'package:uuid/uuid.dart';

enum EditRequestStatus {
  pending,           // في الانتظار
  accepted,          // مقبول - يمكن للمستخدم التعديل
  rejected,          // مرفوض
  inProgress,        // قيد التعديل
  awaitingApproval,  // في انتظار موافقة صاحب المنشور
  completed,         // مكتمل ومنشور
}

class EditRequest {
  final String id;
  final String fromUserId;      // المستخدم الذي طلب التعديل
  final String fromUserName;
  final String fromUserUniversity;
  final String toUserId;        // المستخدم المطلوب منه التعديل
  final String toUserName;
  final String postId;          // المنشور المطلوب تعديله
  final String mindMapId;       // المخطط الذهني
  final String mindMapTitle;
  final String mindMapSubject;
  final String requestMessage;  // رسالة الطلب
  final String? responseMessage; // رد المستخدم
  final Map<String, dynamic>? editedMindMapData; // بيانات المخطط المعدل
  final EditRequestStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EditRequest({
    required this.id,
    required this.fromUserId,
    required this.fromUserName,
    required this.fromUserUniversity,
    required this.toUserId,
    required this.toUserName,
    required this.postId,
    required this.mindMapId,
    required this.mindMapTitle,
    required this.mindMapSubject,
    required this.requestMessage,
    this.responseMessage,
    this.editedMindMapData,
    this.status = EditRequestStatus.pending,
    required this.createdAt,
    required this.updatedAt,
  });

  // إنشاء طلب تعديل جديد
  factory EditRequest.create({
    required String fromUserId,
    required String fromUserName,
    required String fromUserUniversity,
    required String toUserId,
    required String toUserName,
    required String postId,
    required String mindMapId,
    required String mindMapTitle,
    required String mindMapSubject,
    required String requestMessage,
  }) {
    final now = DateTime.now();
    return EditRequest(
      id: const Uuid().v4(),
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserUniversity: fromUserUniversity,
      toUserId: toUserId,
      toUserName: toUserName,
      postId: postId,
      mindMapId: mindMapId,
      mindMapTitle: mindMapTitle,
      mindMapSubject: mindMapSubject,
      requestMessage: requestMessage,
      createdAt: now,
      updatedAt: now,
    );
  }

  // نسخ مع تعديل
  EditRequest copyWith({
    String? id,
    String? fromUserId,
    String? fromUserName,
    String? fromUserUniversity,
    String? toUserId,
    String? toUserName,
    String? postId,
    String? mindMapId,
    String? mindMapTitle,
    String? mindMapSubject,
    String? requestMessage,
    String? responseMessage,
    Map<String, dynamic>? editedMindMapData,
    EditRequestStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EditRequest(
      id: id ?? this.id,
      fromUserId: fromUserId ?? this.fromUserId,
      fromUserName: fromUserName ?? this.fromUserName,
      fromUserUniversity: fromUserUniversity ?? this.fromUserUniversity,
      toUserId: toUserId ?? this.toUserId,
      toUserName: toUserName ?? this.toUserName,
      postId: postId ?? this.postId,
      mindMapId: mindMapId ?? this.mindMapId,
      mindMapTitle: mindMapTitle ?? this.mindMapTitle,
      mindMapSubject: mindMapSubject ?? this.mindMapSubject,
      requestMessage: requestMessage ?? this.requestMessage,
      responseMessage: responseMessage ?? this.responseMessage,
      editedMindMapData: editedMindMapData ?? this.editedMindMapData,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'fromUserUniversity': fromUserUniversity,
      'toUserId': toUserId,
      'toUserName': toUserName,
      'postId': postId,
      'mindMapId': mindMapId,
      'mindMapTitle': mindMapTitle,
      'mindMapSubject': mindMapSubject,
      'requestMessage': requestMessage,
      'responseMessage': responseMessage,
      'editedMindMapData': editedMindMapData,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // إنشاء من JSON
  factory EditRequest.fromJson(Map<String, dynamic> json) {
    return EditRequest(
      id: json['id']?.toString() ?? '',
      fromUserId: json['fromUserId']?.toString() ?? '',
      fromUserName: json['fromUserName']?.toString() ?? '',
      fromUserUniversity: json['fromUserUniversity']?.toString() ?? '',
      toUserId: json['toUserId']?.toString() ?? '',
      toUserName: json['toUserName']?.toString() ?? '',
      postId: json['postId']?.toString() ?? '',
      mindMapId: json['mindMapId']?.toString() ?? '',
      mindMapTitle: json['mindMapTitle']?.toString() ?? '',
      mindMapSubject: json['mindMapSubject']?.toString() ?? '',
      requestMessage: json['requestMessage']?.toString() ?? '',
      responseMessage: json['responseMessage']?.toString(),
      editedMindMapData: json['editedMindMapData'] != null
          ? Map<String, dynamic>.from(json['editedMindMapData'])
          : null,
      status: EditRequestStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => EditRequestStatus.pending,
      ),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'].toString())
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'].toString())
          : DateTime.now(),
    );
  }

  // الحصول على نص الحالة
  String get statusText {
    switch (status) {
      case EditRequestStatus.pending:
        return 'في الانتظار';
      case EditRequestStatus.accepted:
        return 'مقبول';
      case EditRequestStatus.rejected:
        return 'مرفوض';
      case EditRequestStatus.inProgress:
        return 'قيد التعديل';
      case EditRequestStatus.awaitingApproval:
        return 'في انتظار الموافقة';
      case EditRequestStatus.completed:
        return 'مكتمل';
    }
  }

  // الحصول على لون الحالة
  String get statusColor {
    switch (status) {
      case EditRequestStatus.pending:
        return 'orange';
      case EditRequestStatus.accepted:
        return 'blue';
      case EditRequestStatus.rejected:
        return 'red';
      case EditRequestStatus.inProgress:
        return 'purple';
      case EditRequestStatus.awaitingApproval:
        return 'teal';
      case EditRequestStatus.completed:
        return 'green';
    }
  }

  @override
  String toString() {
    return 'EditRequest(id: $id, from: $fromUserName, to: $toUserName, mindMap: $mindMapTitle, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EditRequest && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
