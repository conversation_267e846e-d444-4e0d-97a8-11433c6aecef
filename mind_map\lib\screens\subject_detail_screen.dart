import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/subject.dart';
import '../models/mind_map.dart';
import '../widgets/mind_map_card.dart';
import '../widgets/create_mind_map_dialog.dart';
import '../widgets/edit_mind_map_dialog.dart';
import '../widgets/edit_subject_dialog.dart';
import 'mind_map_editor_screen.dart';

class SubjectDetailScreen extends StatefulWidget {
  final Subject subject;

  const SubjectDetailScreen({
    super.key,
    required this.subject,
  });

  @override
  State<SubjectDetailScreen> createState() => _SubjectDetailScreenState();
}

class _SubjectDetailScreenState extends State<SubjectDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.subject.name),
        backgroundColor: widget.subject.color,
        foregroundColor: _getTextColor(widget.subject.color),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editSubject,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'statistics',
                child: ListTile(
                  leading: Icon(Icons.analytics),
                  title: Text('الإحصائيات'),
                ),
              ),
              const PopupMenuItem(
                value: 'export_all',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('تصدير جميع المخططات'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          final mindMaps = provider.getMindMapsForSubject(widget.subject.name);
          
          return Column(
            children: [
              // Subject info header
              _buildSubjectHeader(),
              
              // Mind maps section
              Expanded(
                child: mindMaps.isEmpty
                    ? _buildEmptyState()
                    : _buildMindMapsList(mindMaps),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewMindMap,
        icon: const Icon(Icons.add),
        label: const Text('مخطط جديد'),
        backgroundColor: widget.subject.color,
        foregroundColor: _getTextColor(widget.subject.color),
      ),
    );
  }

  Widget _buildSubjectHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: widget.subject.color.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: widget.subject.color.withOpacity(0.3),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subject icon and name
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: widget.subject.color,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  _getSubjectIcon(),
                  color: _getTextColor(widget.subject.color),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.subject.name,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: widget.subject.color,
                      ),
                    ),
                    if (widget.subject.description.isNotEmpty)
                      Text(
                        widget.subject.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Subject details
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              if (widget.subject.professor != null)
                _buildInfoChip(
                  icon: Icons.person,
                  label: widget.subject.professor!,
                ),
              _buildInfoChip(
                icon: Icons.schedule,
                label: widget.subject.creditHours == widget.subject.creditHours.toInt()
                    ? '${widget.subject.creditHours.toInt()} ساعة معتمدة'
                    : '${widget.subject.creditHours} ساعة معتمدة',
              ),
              _buildInfoChip(
                icon: Icons.calendar_today,
                label: 'الفصل ${widget.subject.semester}',
              ),
              Consumer<MindMapProvider>(
                builder: (context, provider, child) {
                  final mindMapCount = provider.getMindMapsForSubject(widget.subject.name).length;
                  return _buildInfoChip(
                    icon: Icons.account_tree,
                    label: '$mindMapCount مخطط ذهني',
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: widget.subject.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: widget.subject.color.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: widget.subject.color,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: widget.subject.color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_tree,
            size: 80,
            color: widget.subject.color.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد مخططات ذهنية',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإنشاء مخططك الذهني الأول لهذه المادة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _createNewMindMap,
            icon: const Icon(Icons.add),
            label: const Text('إنشاء مخطط ذهني'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.subject.color,
              foregroundColor: _getTextColor(widget.subject.color),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMindMapsList(List<MindMap> mindMaps) {
    return RefreshIndicator(
      onRefresh: () => context.read<MindMapProvider>().loadData(),
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: mindMaps.length,
        itemBuilder: (context, index) {
          final mindMap = mindMaps[index];
          return MindMapCard(
            mindMap: mindMap,
            onTap: () => _openMindMapEditor(mindMap),
            onEdit: () => _editMindMap(mindMap),
            onDelete: () => _deleteMindMap(mindMap),
          );
        },
      ),
    );
  }

  void _createNewMindMap() {
    showDialog(
      context: context,
      builder: (context) => const CreateMindMapDialog(),
    );
  }

  void _openMindMapEditor(MindMap mindMap) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MindMapEditorScreen(mindMap: mindMap),
      ),
    );
  }

  void _editMindMap(MindMap mindMap) {
    showDialog(
      context: context,
      builder: (context) => EditMindMapDialog(mindMap: mindMap),
    );
  }

  void _deleteMindMap(MindMap mindMap) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المخطط الذهني'),
        content: Text('هل أنت متأكد من حذف "${mindMap.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<MindMapProvider>().deleteMindMap(mindMap.id);
              Navigator.pop(context);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _editSubject() {
    showDialog(
      context: context,
      builder: (context) => EditSubjectDialog(subject: widget.subject),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'statistics':
        _showStatistics();
        break;
      case 'export_all':
        _exportAllMindMaps();
        break;
    }
  }

  void _showStatistics() {
    final provider = context.read<MindMapProvider>();
    final mindMaps = provider.getMindMapsForSubject(widget.subject.name);
    final totalNodes = mindMaps.fold<int>(0, (sum, mindMap) => sum + mindMap.nodeCount);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات المادة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('عدد المخططات الذهنية: ${mindMaps.length}'),
            Text('إجمالي العقد: $totalNodes'),
            Text('متوسط العقد لكل مخطط: ${mindMaps.isEmpty ? 0 : (totalNodes / mindMaps.length).toStringAsFixed(1)}'),
            if (mindMaps.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text('آخر مخطط: ${mindMaps.last.title}'),
              Text('تاريخ آخر تحديث: ${_formatDate(mindMaps.last.updatedAt)}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _exportAllMindMaps() {
    // TODO: تنفيذ تصدير جميع المخططات
  }

  IconData _getSubjectIcon() {
    // نفس منطق الأيقونات من SubjectCard
    final subjectName = widget.subject.name.toLowerCase();
    
    if (subjectName.contains('رياضيات') || subjectName.contains('حساب')) {
      return Icons.calculate;
    } else if (subjectName.contains('فيزياء')) {
      return Icons.science;
    } else if (subjectName.contains('كيمياء')) {
      return Icons.biotech;
    } else if (subjectName.contains('أحياء') || subjectName.contains('بيولوجيا')) {
      return Icons.eco;
    } else if (subjectName.contains('تاريخ')) {
      return Icons.history_edu;
    } else if (subjectName.contains('جغرافيا')) {
      return Icons.public;
    } else if (subjectName.contains('عربية') || subjectName.contains('عربي')) {
      return Icons.translate;
    } else if (subjectName.contains('إنجليزي') || subjectName.contains('انجليزي')) {
      return Icons.language;
    } else if (subjectName.contains('حاسوب') || subjectName.contains('برمجة')) {
      return Icons.computer;
    } else if (subjectName.contains('هندسة')) {
      return Icons.engineering;
    } else if (subjectName.contains('طب')) {
      return Icons.medical_services;
    } else if (subjectName.contains('قانون')) {
      return Icons.gavel;
    } else if (subjectName.contains('اقتصاد')) {
      return Icons.trending_up;
    } else if (subjectName.contains('فلسفة')) {
      return Icons.psychology;
    } else if (subjectName.contains('فن')) {
      return Icons.palette;
    } else if (subjectName.contains('موسيقى')) {
      return Icons.music_note;
    } else if (subjectName.contains('رياضة')) {
      return Icons.sports;
    } else {
      return Icons.school;
    }
  }

  Color _getTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
