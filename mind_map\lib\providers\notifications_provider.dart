import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/notification.dart';
import '../models/reaction.dart';
import '../models/user_model.dart';
import '../models/post.dart';
import '../widgets/post_audience_selector.dart';

class NotificationsProvider extends ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  List<AppNotification> _notifications = [];
  bool _isLoading = false;
  String? _error;
  String? _currentUserId;
  StreamSubscription<DatabaseEvent>? _notificationsSubscription;

  // Getters
  List<AppNotification> get notifications => _notifications;
  List<AppNotification> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasUnreadNotifications => unreadCount > 0;

  // تحديد المستخدم الحالي
  void setCurrentUser(String? userId) {
    if (_currentUserId != userId) {
      // إلغاء الاستماع السابق أولاً
      _notificationsSubscription?.cancel();
      _notificationsSubscription = null;

      _currentUserId = userId;
      _notifications.clear();

      if (userId != null) {
        // بدء الاستماع للتحديثات الفورية
        _startListeningToNotifications();
        loadNotifications();
      }
      notifyListeners();
    }
  }

  // تحديد حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحديد رسالة الخطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحميل الإشعارات
  Future<void> loadNotifications() async {
    if (_currentUserId == null) return;

    _setLoading(true);
    _setError(null);

    try {
      final notificationsRef = _database.ref('notifications/$_currentUserId');
      final snapshot = await notificationsRef.get();

      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        _notifications = data.entries
            .map((entry) {
              try {
                final notificationData = Map<String, dynamic>.from(entry.value);
                return AppNotification.fromJson(notificationData);
              } catch (e) {
                print('خطأ في تحويل إشعار: $e');
                return null;
              }
            })
            .where((notification) => notification != null)
            .cast<AppNotification>()
            .toList();

        // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
        _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      } else {
        _notifications = [];
      }

      print('✅ تم تحميل ${_notifications.length} إشعار');
    } catch (e) {
      print('❌ خطأ في تحميل الإشعارات: $e');
      _setError('خطأ في تحميل الإشعارات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // إضافة إشعار جديد
  Future<void> addNotification(AppNotification notification) async {
    try {
      // حفظ في Firebase
      final notificationRef = _database.ref('notifications/${notification.userId}/${notification.id}');
      await notificationRef.set(notification.toJson());

      // إضافة للقائمة المحلية إذا كان للمستخدم الحالي
      if (notification.userId == _currentUserId) {
        _notifications.insert(0, notification);
        notifyListeners();
      }

      print('✅ تم إضافة إشعار: ${notification.title}');
    } catch (e) {
      print('❌ خطأ في إضافة الإشعار: $e');
      rethrow;
    }
  }

  // تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    if (_currentUserId == null) return;

    try {
      // تحديث في Firebase
      final notificationRef = _database.ref('notifications/$_currentUserId/$notificationId/isRead');
      await notificationRef.set(true);

      // تحديث في القائمة المحلية
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        notifyListeners();
      }

      print('✅ تم تحديد الإشعار كمقروء: $notificationId');
    } catch (e) {
      print('❌ خطأ في تحديث الإشعار: $e');
      rethrow;
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    if (_currentUserId == null) return;

    try {
      final batch = <String, dynamic>{};
      
      for (final notification in _notifications.where((n) => !n.isRead)) {
        batch['notifications/$_currentUserId/${notification.id}/isRead'] = true;
      }

      if (batch.isNotEmpty) {
        await _database.ref().update(batch);

        // تحديث القائمة المحلية
        for (int i = 0; i < _notifications.length; i++) {
          if (!_notifications[i].isRead) {
            _notifications[i] = _notifications[i].copyWith(isRead: true);
          }
        }
        notifyListeners();
      }

      print('✅ تم تحديد جميع الإشعارات كمقروءة');
    } catch (e) {
      print('❌ خطأ في تحديث الإشعارات: $e');
      rethrow;
    }
  }

  // حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    if (_currentUserId == null) return;

    try {
      // حذف من Firebase
      final notificationRef = _database.ref('notifications/$_currentUserId/$notificationId');
      await notificationRef.remove();

      // حذف من القائمة المحلية
      _notifications.removeWhere((n) => n.id == notificationId);
      notifyListeners();

      print('✅ تم حذف الإشعار: $notificationId');
    } catch (e) {
      print('❌ خطأ في حذف الإشعار: $e');
      rethrow;
    }
  }

  // حذف جميع الإشعارات
  Future<void> clearAllNotifications() async {
    if (_currentUserId == null) return;

    try {
      // حذف من Firebase
      final notificationsRef = _database.ref('notifications/$_currentUserId');
      await notificationsRef.remove();

      // مسح القائمة المحلية
      _notifications.clear();
      notifyListeners();

      print('✅ تم حذف جميع الإشعارات');
    } catch (e) {
      print('❌ خطأ في حذف الإشعارات: $e');
      rethrow;
    }
  }

  // إنشاء إشعار متابعة جديدة
  Future<void> createFollowNotification({
    required String targetUserId,
    required UserModel follower,
  }) async {
    try {
      // التحقق من إعدادات خصوصية المستخدم المستهدف
      final targetUserRef = _database.ref('users/$targetUserId');
      final targetSnapshot = await targetUserRef.once();

      if (targetSnapshot.snapshot.exists) {
        final targetData = Map<String, dynamic>.from(targetSnapshot.snapshot.value as Map);
        final notifyOnFollow = targetData['notifyOnFollow'] ?? true;

        // إذا كان المستخدم لا يريد إشعارات المتابعة، لا نرسل الإشعار
        if (!notifyOnFollow) {
          print('🔕 المستخدم $targetUserId لا يريد إشعارات المتابعة');
          return;
        }
      }

      final notification = NotificationFactory.createFollowNotification(
        userId: targetUserId,
        fromUserId: follower.uid,
        fromUserName: '${follower.firstName} ${follower.lastName}',
        fromUserAvatar: null, // TODO: إضافة صورة المستخدم لاحقاً
      );

      await addNotification(notification);
    } catch (e) {
      print('❌ خطأ في إنشاء إشعار المتابعة: $e');
    }
  }

  // إنشاء إشعار منشور جديد
  Future<void> createNewPostNotification({
    required List<String> followerIds,
    required UserModel author,
    required String postId,
    required String postTitle,
    required PostAudience audience,
  }) async {
    try {
      // التحقق من إعدادات خصوصية المؤلف
      if (!(author.notifyFollowersOnNewPost ?? true)) {
        print('🔕 المؤلف ${author.fullName} لا يريد إشعار المتابعين عند النشر');
        return;
      }

      // فلترة المتابعين حسب نوع الجمهور
      final eligibleFollowers = await _filterFollowersByAudience(
        followerIds: followerIds,
        author: author,
        audience: audience,
      );

      print('🎯 فلترة المتابعين: ${followerIds.length} → ${eligibleFollowers.length} (${audience.displayName})');

      // إرسال إشعار للمتابعين المؤهلين
      for (final followerId in eligibleFollowers) {
        // تجنب إرسال إشعار للمؤلف نفسه
        if (followerId == author.uid) continue;

        final notification = NotificationFactory.createNewPostNotification(
          userId: followerId,
          fromUserId: author.uid,
          fromUserName: '${author.firstName} ${author.lastName}',
          postId: postId,
          postTitle: postTitle,
          fromUserAvatar: null, // TODO: إضافة صورة المستخدم لاحقاً
        );

        await addNotification(notification);
      }

      print('✅ تم إرسال إشعارات المنشور الجديد لـ ${eligibleFollowers.length} متابع');
    } catch (e) {
      print('❌ خطأ في إنشاء إشعارات المنشور الجديد: $e');
    }
  }

  // إنشاء إشعار رد فعل
  Future<void> createReactionNotification({
    required String postAuthorId,
    required UserModel reactor,
    required String postId,
    required String postTitle,
    required ReactionType reactionType,
  }) async {
    // لا نرسل إشعار إذا كان المستخدم يتفاعل مع منشوره الخاص
    if (postAuthorId == reactor.uid) return;

    try {
      // التحقق من إعدادات خصوصية المستخدم المستهدف
      final targetUserRef = _database.ref('users/$postAuthorId');
      final targetSnapshot = await targetUserRef.once();

      if (targetSnapshot.snapshot.exists) {
        final targetData = Map<String, dynamic>.from(targetSnapshot.snapshot.value as Map);
        final notifyOnReaction = targetData['notifyOnReaction'] ?? true;

        // إذا كان المستخدم لا يريد إشعارات ردود الفعل، لا نرسل الإشعار
        if (!notifyOnReaction) {
          print('🔕 المستخدم $postAuthorId لا يريد إشعارات ردود الفعل');
          return;
        }
      }

      final notification = NotificationFactory.createReactionNotification(
        userId: postAuthorId,
        fromUserId: reactor.uid,
        fromUserName: '${reactor.firstName} ${reactor.lastName}',
        postId: postId,
        postTitle: postTitle,
        reactionType: reactionType,
        fromUserAvatar: null, // TODO: إضافة صورة المستخدم لاحقاً
      );

      await addNotification(notification);
    } catch (e) {
      print('❌ خطأ في إنشاء إشعار رد الفعل: $e');
    }
  }

  // إنشاء إشعار تعليق
  Future<void> createCommentNotification({
    required String postAuthorId,
    required UserModel commenter,
    required String postId,
    required String postTitle,
    required String commentId,
  }) async {
    // لا نرسل إشعار إذا كان المستخدم يعلق على منشوره الخاص
    if (postAuthorId == commenter.uid) return;

    try {
      // التحقق من إعدادات خصوصية المستخدم المستهدف
      final targetUserRef = _database.ref('users/$postAuthorId');
      final targetSnapshot = await targetUserRef.once();

      if (targetSnapshot.snapshot.exists) {
        final targetData = Map<String, dynamic>.from(targetSnapshot.snapshot.value as Map);
        final notifyOnComment = targetData['notifyOnComment'] ?? true;

        // إذا كان المستخدم لا يريد إشعارات التعليقات، لا نرسل الإشعار
        if (!notifyOnComment) {
          print('🔕 المستخدم $postAuthorId لا يريد إشعارات التعليقات');
          return;
        }
      }

      final notification = NotificationFactory.createCommentNotification(
        userId: postAuthorId,
        fromUserId: commenter.uid,
        fromUserName: '${commenter.firstName} ${commenter.lastName}',
        postId: postId,
        postTitle: postTitle,
        commentId: commentId,
        fromUserAvatar: null, // TODO: إضافة صورة المستخدم لاحقاً
      );

      await addNotification(notification);
    } catch (e) {
      print('❌ خطأ في إنشاء إشعار التعليق: $e');
    }
  }

  // إنشاء إشعار رد
  Future<void> createReplyNotification({
    required String commentAuthorId,
    required UserModel replier,
    required String postId,
    required String commentId,
  }) async {
    // لا نرسل إشعار إذا كان المستخدم يرد على تعليقه الخاص
    if (commentAuthorId == replier.uid) return;

    final notification = NotificationFactory.createReplyNotification(
      userId: commentAuthorId,
      fromUserId: replier.uid,
      fromUserName: '${replier.firstName} ${replier.lastName}',
      postId: postId,
      commentId: commentId,
      fromUserAvatar: null, // TODO: إضافة صورة المستخدم لاحقاً
    );

    await addNotification(notification);
  }

  // إنشاء إشعار رد فعل على تعليق
  Future<void> createCommentReactionNotification({
    required String commentAuthorId,
    required UserModel reactor,
    required String postId,
    required String commentId,
    required ReactionType reactionType,
  }) async {
    // لا نرسل إشعار إذا كان المستخدم يتفاعل مع تعليقه الخاص
    if (commentAuthorId == reactor.uid) return;

    final notification = NotificationFactory.createReactionNotification(
      userId: commentAuthorId,
      fromUserId: reactor.uid,
      fromUserName: '${reactor.firstName} ${reactor.lastName}',
      postId: postId,
      postTitle: 'تعليقك', // يمكن تحسين هذا لاحقاً
      reactionType: reactionType,
      fromUserAvatar: null, // TODO: إضافة صورة المستخدم لاحقاً
    );

    await addNotification(notification);
  }



  // مسح رسالة الخطأ
  void clearError() {
    _setError(null);
  }

  // بدء الاستماع للتحديثات الفورية
  void _startListeningToNotifications() {
    if (_currentUserId == null) return;

    final notificationsRef = _database.ref('notifications/$_currentUserId');
    _notificationsSubscription = notificationsRef.onValue.listen((DatabaseEvent event) {
      _handleNotificationsUpdate(event.snapshot);
    });
  }

  // معالجة تحديثات الإشعارات
  void _handleNotificationsUpdate(DataSnapshot snapshot) {
    try {
      if (snapshot.exists && snapshot.value != null) {
        final notificationsData = Map<String, dynamic>.from(snapshot.value as Map);
        final List<AppNotification> newNotifications = [];

        notificationsData.forEach((key, value) {
          try {
            final notificationData = Map<String, dynamic>.from(value as Map);
            notificationData['id'] = key;
            final notification = AppNotification.fromJson(notificationData);
            newNotifications.add(notification);
          } catch (e) {
            print('❌ خطأ في تحويل إشعار: $e');
          }
        });

        // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
        newNotifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        // التحقق من وجود تغييرات قبل التحديث
        if (_notifications.length != newNotifications.length ||
            !_areNotificationListsEqual(_notifications, newNotifications)) {
          _notifications = newNotifications;
          print('🔔 تم تحديث الإشعارات فورياً - العدد: ${_notifications.length}');
          notifyListeners();
        }
      } else {
        if (_notifications.isNotEmpty) {
          _notifications.clear();
          notifyListeners();
        }
      }
    } catch (e) {
      print('❌ خطأ في معالجة تحديث الإشعارات: $e');
    }
  }

  // مقارنة قوائم الإشعارات
  bool _areNotificationListsEqual(List<AppNotification> list1, List<AppNotification> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id || list1[i].isRead != list2[i].isRead) {
        return false;
      }
    }
    return true;
  }

  // إلغاء جميع الاستماعات
  Future<void> clearSubscriptions() async {
    await _notificationsSubscription?.cancel();
    _notificationsSubscription = null;
    _currentUserId = null;
    _notifications.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _notificationsSubscription?.cancel();
    super.dispose();
  }

  // إضافة إشعارات تجريبية للاختبار
  Future<void> addTestNotifications() async {
    if (_currentUserId == null) return;

    try {
      // إشعار متابعة تجريبي
      final followNotification = NotificationFactory.createFollowNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_1',
        fromUserName: 'أحمد محمد',
      );
      await addNotification(followNotification);

      // إشعار منشور جديد تجريبي
      final postNotification = NotificationFactory.createNewPostNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_2',
        fromUserName: 'فاطمة علي',
        postId: 'test_post_1',
        postTitle: 'مخطط ذهني لمادة الرياضيات',
      );
      await addNotification(postNotification);

      // إشعار إعجاب تجريبي
      final likeNotification = NotificationFactory.createLikeNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_3',
        fromUserName: 'محمد سالم',
        postId: 'test_post_2',
        postTitle: 'مخطط ذهني للفيزياء',
      );
      await addNotification(likeNotification);

      // إشعار تعليق تجريبي
      final commentNotification = NotificationFactory.createCommentNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_4',
        fromUserName: 'سارة أحمد',
        postId: 'test_post_3',
        postTitle: 'مخطط ذهني للكيمياء',
        commentId: 'test_comment_1',
      );
      await addNotification(commentNotification);

      // إشعار رد تجريبي
      final replyNotification = NotificationFactory.createReplyNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_5',
        fromUserName: 'عبدالله خالد',
        postId: 'test_post_4',
        commentId: 'test_comment_2',
      );
      await addNotification(replyNotification);

      // إشعارات ردود فعل تجريبية
      final heartReactionNotification = NotificationFactory.createReactionNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_6',
        fromUserName: 'ليلى أحمد',
        postId: 'test_post_5',
        postTitle: 'مخطط ذهني للأحياء',
        reactionType: ReactionType.love,
      );
      await addNotification(heartReactionNotification);

      final excellentReactionNotification = NotificationFactory.createReactionNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_7',
        fromUserName: 'يوسف محمد',
        postId: 'test_post_6',
        postTitle: 'مخطط ذهني للتاريخ',
        reactionType: ReactionType.excellent,
      );
      await addNotification(excellentReactionNotification);

      final supportReactionNotification = NotificationFactory.createReactionNotification(
        userId: _currentUserId!,
        fromUserId: 'test_user_8',
        fromUserName: 'مريم سالم',
        postId: 'test_post_7',
        postTitle: 'مخطط ذهني للجغرافيا',
        reactionType: ReactionType.support,
      );
      await addNotification(supportReactionNotification);

      print('✅ تم إضافة الإشعارات التجريبية مع ردود الفعل');
    } catch (e) {
      print('❌ خطأ في إضافة الإشعارات التجريبية: $e');
    }
  }

  // فلترة المتابعين حسب نوع الجمهور
  Future<List<String>> _filterFollowersByAudience({
    required List<String> followerIds,
    required UserModel author,
    required PostAudience audience,
  }) async {
    switch (audience) {
      case PostAudience.everyone:
        // المنشورات العامة - لا توجد فلترة، لكن لا نرسل إشعارات للعامة
        // فقط للمتابعين
        return followerIds;

      case PostAudience.followersOnly:
        // للمتابعين فقط - جميع المتابعين مؤهلون
        return followerIds;

      case PostAudience.mutualFollowers:
        // للمتابعين المتبادلين - فلترة المتابعين الذين يتابعهم المؤلف أيضاً
        final mutualFollowers = <String>[];

        for (final followerId in followerIds) {
          // فحص إذا كان المؤلف يتابع هذا المتابع أيضاً
          if (author.following.contains(followerId)) {
            mutualFollowers.add(followerId);
          }
        }

        return mutualFollowers;
    }
  }
}
