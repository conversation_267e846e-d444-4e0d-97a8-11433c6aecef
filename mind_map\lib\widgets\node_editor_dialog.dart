import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../models/mind_map_node.dart';

class NodeEditorDialog extends StatefulWidget {
  final MindMapNode node;
  final Function(MindMapNode) onSave;

  const NodeEditorDialog({
    super.key,
    required this.node,
    required this.onSave,
  });

  @override
  State<NodeEditorDialog> createState() => _NodeEditorDialogState();
}

class _NodeEditorDialogState extends State<NodeEditorDialog> {
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late Color _selectedColor;
  late double _width;
  late double _height;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.node.title);
    _descriptionController = TextEditingController(text: widget.node.description);
    _selectedColor = widget.node.color;
    _width = widget.node.width;
    _height = widget.node.height;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحرير العقدة'),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title field
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان العقدة',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              
              const SizedBox(height: 16),
              
              // Description field
              TextField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 16),
              
              // Color picker section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'لون العقدة',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: _selectedColor,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: _showColorPicker,
                        child: const Text('اختيار اللون'),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Predefined colors
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _predefinedColors.map((color) {
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedColor = color;
                          });
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                            border: _selectedColor == color
                                ? Border.all(
                                    color: Theme.of(context).colorScheme.primary,
                                    width: 3,
                                  )
                                : Border.all(color: Colors.grey.withOpacity(0.3)),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Size controls
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حجم العقدة',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  
                  // Width slider
                  Row(
                    children: [
                      const Text('العرض:'),
                      Expanded(
                        child: Slider(
                          value: _width,
                          min: 80,
                          max: 200,
                          divisions: 12,
                          label: _width.round().toString(),
                          onChanged: (value) {
                            setState(() {
                              _width = value;
                            });
                          },
                        ),
                      ),
                      Text('${_width.round()}'),
                    ],
                  ),
                  
                  // Height slider
                  Row(
                    children: [
                      const Text('الارتفاع:'),
                      Expanded(
                        child: Slider(
                          value: _height,
                          min: 40,
                          max: 120,
                          divisions: 8,
                          label: _height.round().toString(),
                          onChanged: (value) {
                            setState(() {
                              _height = value;
                            });
                          },
                        ),
                      ),
                      Text('${_height.round()}'),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Preview
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    const Text(
                      'معاينة العقدة',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: _width,
                      height: _height,
                      decoration: BoxDecoration(
                        color: _selectedColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.withOpacity(0.3)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Center(
                          child: Text(
                            _titleController.text.isEmpty 
                                ? 'عنوان العقدة' 
                                : _titleController.text,
                            style: TextStyle(
                              color: _getTextColor(_selectedColor),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveNode,
          child: const Text('حفظ'),
        ),
      ],
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار اللون'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _selectedColor,
            onColorChanged: (color) {
              setState(() {
                _selectedColor = color;
              });
            },
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  void _saveNode() {
    final updatedNode = widget.node.copyWith(
      title: _titleController.text.trim().isEmpty 
          ? 'عقدة بدون عنوان' 
          : _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      color: _selectedColor,
      width: _width,
      height: _height,
    );
    
    widget.onSave(updatedNode);
    Navigator.pop(context);
  }

  Color _getTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  static const List<Color> _predefinedColors = [
    Color(0xFF2196F3), // أزرق
    Color(0xFF4CAF50), // أخضر
    Color(0xFFFF9800), // برتقالي
    Color(0xFF9C27B0), // بنفسجي
    Color(0xFFF44336), // أحمر
    Color(0xFF00BCD4), // سماوي
    Color(0xFF795548), // بني
    Color(0xFF607D8B), // رمادي مزرق
    Color(0xFFE91E63), // وردي
    Color(0xFF8BC34A), // أخضر فاتح
    Color(0xFFFF5722), // برتقالي محمر
    Color(0xFF3F51B5), // أزرق داكن
    Color(0xFFFFEB3B), // أصفر
    Color(0xFF9E9E9E), // رمادي
    Color(0xFF673AB7), // بنفسجي داكن
    Color(0xFFCDDC39), // أخضر مصفر
  ];
}
