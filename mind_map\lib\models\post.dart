import 'package:uuid/uuid.dart';
import 'reaction.dart';

// أنواع جمهور المنشور
enum PostAudience {
  everyone,           // للجميع
  followersOnly,      // للمتابعين فقط
  mutualFollowers,    // للمتابعين المتبادلين (الذين يتابعوني وأتابعهم)
}

class Post {
  final String id;
  final String authorId;
  final String authorName;
  final String authorUniversity;
  final String mindMapId;
  final String mindMapTitle;
  final String mindMapSubject;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> likes; // قائمة معرفات المستخدمين الذين أعجبوا بالمنشور (للتوافق مع النسخة القديمة)
  final ReactionSummary reactions; // ردود الفعل على المنشور
  final List<PostComment> comments; // التعليقات على المنشور
  final Map<String, dynamic> mindMapData; // بيانات المخطط الذهني المشارك
  final String? editedByUserId;      // معرف المستخدم الذي عدل المنشور
  final String? editedByUserName;    // اسم المستخدم الذي عدل المنشور
  final String? editRequestId;       // معرف طلب التعديل
  final PostAudience audience;       // جمهور المنشور
  final int viewsCount;              // عدد المشاهدات
  final List<String> viewers;        // قائمة معرفات المستخدمين الذين شاهدوا المنشور

  Post({
    String? id,
    required this.authorId,
    required this.authorName,
    required this.authorUniversity,
    required this.mindMapId,
    required this.mindMapTitle,
    required this.mindMapSubject,
    this.description = '',
    this.editedByUserId,
    this.editedByUserName,
    this.editRequestId,
    this.audience = PostAudience.everyone, // القيمة الافتراضية للجميع
    this.viewsCount = 0, // القيمة الافتراضية للمشاهدات
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? likes,
    ReactionSummary? reactions,
    List<PostComment>? comments,
    List<String>? viewers,
    required this.mindMapData,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       likes = likes ?? [],
       reactions = reactions ?? ReactionSummary(),
       comments = comments ?? [],
       viewers = viewers ?? [];

  // إنشاء نسخة مع تعديل بعض الخصائص
  Post copyWith({
    String? description,
    String? editedByUserId,
    String? editedByUserName,
    String? editRequestId,
    PostAudience? audience,
    int? viewsCount,
    List<String>? viewers,
    DateTime? updatedAt,
    List<String>? likes,
    ReactionSummary? reactions,
    List<PostComment>? comments,
    Map<String, dynamic>? mindMapData,
  }) {
    return Post(
      id: id,
      authorId: authorId,
      authorName: authorName,
      authorUniversity: authorUniversity,
      mindMapId: mindMapId,
      mindMapTitle: mindMapTitle,
      mindMapSubject: mindMapSubject,
      description: description ?? this.description,
      editedByUserId: editedByUserId ?? this.editedByUserId,
      editedByUserName: editedByUserName ?? this.editedByUserName,
      editRequestId: editRequestId ?? this.editRequestId,
      audience: audience ?? this.audience,
      viewsCount: viewsCount ?? this.viewsCount,
      viewers: viewers ?? List.from(this.viewers),
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      likes: likes ?? List.from(this.likes),
      reactions: reactions ?? this.reactions,
      comments: comments ?? List.from(this.comments),
      mindMapData: mindMapData ?? this.mindMapData,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'authorId': authorId,
      'authorName': authorName,
      'authorUniversity': authorUniversity,
      'mindMapId': mindMapId,
      'mindMapTitle': mindMapTitle,
      'mindMapSubject': mindMapSubject,
      'description': description,
      'editedByUserId': editedByUserId,
      'editedByUserName': editedByUserName,
      'editRequestId': editRequestId,
      'audience': audience.name,
      'viewsCount': viewsCount,
      'viewers': viewers,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'likes': likes,
      'reactions': reactions.toJson(),
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'mindMapData': mindMapData,
    };
  }

  // إنشاء من JSON
  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
      authorId: json['authorId']?.toString() ?? '',
      authorName: json['authorName']?.toString() ?? 'مستخدم غير معروف',
      authorUniversity: json['authorUniversity']?.toString() ?? '',
      mindMapId: json['mindMapId']?.toString() ?? '',
      mindMapTitle: json['mindMapTitle']?.toString() ?? 'مخطط ذهني',
      mindMapSubject: json['mindMapSubject']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      editedByUserId: json['editedByUserId']?.toString(),
      editedByUserName: json['editedByUserName']?.toString(),
      editRequestId: json['editRequestId']?.toString(),
      audience: _parseAudience(json['audience']),
      viewsCount: json['viewsCount'] is int ? json['viewsCount'] : 0,
      viewers: json['viewers'] != null
          ? List<String>.from(json['viewers'])
          : [],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'].toString())
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'].toString())
          : DateTime.now(),
      likes: json['likes'] != null
          ? List<String>.from(json['likes'])
          : [],
      reactions: json['reactions'] != null
          ? ReactionSummary.fromJson(json['reactions'])
          : ReactionSummary(),
      comments: _convertCommentsFromJson(json['comments']),
      mindMapData: json['mindMapData'] != null
          ? Map<String, dynamic>.from(json['mindMapData'])
          : {},
    );
  }

  // تحويل نوع الجمهور من JSON
  static PostAudience _parseAudience(dynamic audienceData) {
    if (audienceData == null) return PostAudience.everyone;

    final audienceString = audienceData.toString();
    switch (audienceString) {
      case 'followersOnly':
        return PostAudience.followersOnly;
      case 'mutualFollowers':
        return PostAudience.mutualFollowers;
      default:
        return PostAudience.everyone;
    }
  }

  // تحويل التعليقات من JSON (قد تكون Map أو List)
  static List<PostComment> _convertCommentsFromJson(dynamic commentsData) {
    if (commentsData == null) return [];

    try {
      List<dynamic> commentsList;

      if (commentsData is List) {
        commentsList = commentsData;
      } else if (commentsData is Map) {
        // إذا كانت البيانات Map، نحولها إلى List
        commentsList = commentsData.values.toList();
      } else {
        print('⚠️ نوع بيانات التعليقات غير متوقع: ${commentsData.runtimeType}');
        return [];
      }

      return commentsList
          .map((commentData) {
            try {
              // تحويل البيانات إلى Map<String, dynamic>
              Map<String, dynamic> convertedData;
              if (commentData is Map<String, dynamic>) {
                convertedData = commentData;
              } else if (commentData is Map) {
                convertedData = Map<String, dynamic>.from(commentData);
              } else {
                print('❌ نوع بيانات التعليق غير صحيح: ${commentData.runtimeType}');
                return null;
              }

              return PostComment.fromJson(convertedData);
            } catch (e) {
              print('❌ خطأ في تحويل تعليق: $e');
              return null;
            }
          })
          .where((comment) => comment != null)
          .cast<PostComment>()
          .toList();
    } catch (e) {
      print('❌ خطأ في تحويل التعليقات: $e');
      return [];
    }
  }

  // الحصول على عدد الإعجابات
  int get likesCount => likes.length;

  // الحصول على عدد التعليقات
  int get commentsCount => comments.length;

  // التحقق من إعجاب المستخدم
  bool isLikedBy(String userId) => likes.contains(userId);

  // إضافة إعجاب
  Post addLike(String userId) {
    if (!likes.contains(userId)) {
      final newLikes = List<String>.from(likes)..add(userId);
      return copyWith(likes: newLikes);
    }
    return this;
  }

  // إزالة إعجاب
  Post removeLike(String userId) {
    final newLikes = List<String>.from(likes)..remove(userId);
    return copyWith(likes: newLikes);
  }

  // إضافة تعليق
  Post addComment(PostComment comment) {
    final newComments = List<PostComment>.from(comments)..add(comment);
    return copyWith(comments: newComments);
  }

  // إزالة تعليق
  Post removeComment(String commentId) {
    final newComments = List<PostComment>.from(comments)
      ..removeWhere((comment) => comment.id == commentId);
    return copyWith(comments: newComments);
  }

  @override
  String toString() {
    return 'Post(id: $id, title: $mindMapTitle, author: $authorName)';
  }
}

// نموذج تعليق المنشور
class PostComment {
  final String id;
  final String authorId;
  final String authorName;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<String> likes; // للتوافق مع النسخة القديمة
  final ReactionSummary reactions; // ردود الفعل على التعليق
  final String? parentCommentId; // للردود على التعليقات
  final List<PostComment> replies; // الردود على هذا التعليق

  PostComment({
    String? id,
    required this.authorId,
    required this.authorName,
    required this.content,
    DateTime? createdAt,
    this.updatedAt,
    List<String>? likes,
    ReactionSummary? reactions,
    this.parentCommentId,
    List<PostComment>? replies,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       likes = likes ?? [],
       reactions = reactions ?? ReactionSummary(),
       replies = replies ?? [];

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'authorId': authorId,
      'authorName': authorName,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'likes': likes,
      'reactions': reactions.toJson(),
      'parentCommentId': parentCommentId,
      'replies': replies.map((reply) => reply.toJson()).toList(),
    };
  }

  // إنشاء من JSON
  factory PostComment.fromJson(Map<String, dynamic> json) {
    return PostComment(
      id: json['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
      authorId: json['authorId']?.toString() ?? '',
      authorName: json['authorName']?.toString() ?? 'مستخدم غير معروف',
      content: json['content']?.toString() ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'].toString())
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'].toString())
          : null,
      likes: json['likes'] != null
          ? List<String>.from(json['likes'])
          : [],
      reactions: json['reactions'] != null
          ? ReactionSummary.fromJson(json['reactions'])
          : ReactionSummary(),
      parentCommentId: json['parentCommentId']?.toString(),
      replies: _convertRepliesFromJson(json['replies']),
    );
  }

  // تحويل الردود من JSON (قد تكون Map أو List)
  static List<PostComment> _convertRepliesFromJson(dynamic repliesData) {
    if (repliesData == null) return [];

    try {
      List<dynamic> repliesList;

      if (repliesData is List) {
        repliesList = repliesData;
      } else if (repliesData is Map) {
        // إذا كانت البيانات Map، نحولها إلى List
        repliesList = repliesData.values.toList();
      } else {
        return [];
      }

      return repliesList
          .map((replyData) {
            try {
              // تحويل البيانات إلى Map<String, dynamic>
              Map<String, dynamic> convertedData;
              if (replyData is Map<String, dynamic>) {
                convertedData = replyData;
              } else if (replyData is Map) {
                convertedData = Map<String, dynamic>.from(replyData);
              } else {
                return null;
              }

              return PostComment.fromJson(convertedData);
            } catch (e) {
              return null;
            }
          })
          .where((reply) => reply != null)
          .cast<PostComment>()
          .toList();
    } catch (e) {
      return [];
    }
  }

  // إنشاء نسخة محدثة
  PostComment copyWith({
    String? content,
    DateTime? updatedAt,
    List<String>? likes,
    ReactionSummary? reactions,
    List<PostComment>? replies,
  }) {
    return PostComment(
      id: id,
      authorId: authorId,
      authorName: authorName,
      content: content ?? this.content,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likes: likes ?? this.likes,
      reactions: reactions ?? this.reactions,
      parentCommentId: parentCommentId,
      replies: replies ?? this.replies,
    );
  }

  // إضافة أو إزالة إعجاب
  PostComment toggleLike(String userId) {
    final newLikes = List<String>.from(likes);
    if (newLikes.contains(userId)) {
      newLikes.remove(userId);
    } else {
      newLikes.add(userId);
    }
    return copyWith(likes: newLikes);
  }

  // التحقق من الإعجاب
  bool isLikedBy(String userId) {
    return likes.contains(userId);
  }

  // عدد الإعجابات
  int get likesCount => likes.length;

  // عدد الردود
  int get repliesCount => replies.length;

  // إضافة رد
  PostComment addReply(PostComment reply) {
    final newReplies = List<PostComment>.from(replies);
    newReplies.add(reply);
    return copyWith(replies: newReplies);
  }

  // إزالة رد
  PostComment removeReply(String replyId) {
    final newReplies = replies.where((reply) => reply.id != replyId).toList();
    return copyWith(replies: newReplies);
  }

  // تحديث رد
  PostComment updateReply(PostComment updatedReply) {
    final newReplies = replies.map((reply) {
      return reply.id == updatedReply.id ? updatedReply : reply;
    }).toList();
    return copyWith(replies: newReplies);
  }

  // التحقق من وجود ردود
  bool get hasReplies => replies.isNotEmpty;

  // الحصول على إجمالي الإعجابات (التعليق + الردود)
  int get totalLikesCount {
    int total = likesCount;
    for (final reply in replies) {
      total += reply.totalLikesCount;
    }
    return total;
  }

  @override
  String toString() {
    return 'PostComment(id: $id, author: $authorName, content: $content)';
  }
}
