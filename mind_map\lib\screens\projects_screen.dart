import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/mind_map.dart';
import '../models/subject.dart';
import 'mind_map_editor_screen.dart';
import 'subject_detail_screen.dart';
import '../widgets/mind_map_card.dart';
import '../widgets/subject_card.dart';
import '../widgets/create_mind_map_dialog.dart';
import '../widgets/create_subject_dialog.dart';
import '../widgets/edit_mind_map_dialog.dart';
import '../widgets/edit_subject_dialog.dart';
import '../widgets/connectivity_wrapper.dart';

class ProjectsScreen extends StatefulWidget {
  const ProjectsScreen({super.key});

  @override
  State<ProjectsScreen> createState() => _ProjectsScreenState();
}

class _ProjectsScreenState extends State<ProjectsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'مشاريعي',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.blue.shade700,
          foregroundColor: Colors.white,
          elevation: 0,
          automaticallyImplyLeading: false, // إزالة سهم الرجوع
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(48),
            child: Column(
              children: [
                // شريط التبويب
                TabBar(
                  controller: _tabController,
                  indicatorColor: Colors.white,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white70,
                  tabs: const [
                    Tab(
                      icon: Icon(Icons.account_tree),
                      text: 'المخططات الذهنية',
                    ),
                    Tab(
                      icon: Icon(Icons.favorite),
                      text: 'المفضلة',
                    ),
                    Tab(
                      icon: Icon(Icons.school),
                      text: 'المواد الدراسية',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        body: LayoutBuilder(
          builder: (context, constraints) {
            return Consumer<MindMapProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (provider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'حدث خطأ: ${provider.error}',
                          style: const TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => provider.loadData(),
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                return TabBarView(
                  controller: _tabController,
                  children: [
                    _buildMindMapsTab(provider, constraints),
                    _buildFavoritesTab(provider, constraints),
                    _buildSubjectsTab(provider, constraints),
                  ],
                );
              },
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _showCreateDialog,
          backgroundColor: Colors.blue.shade600,
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildMindMapsTab(MindMapProvider provider, BoxConstraints constraints) {
    final mindMaps = provider.mindMaps;

    if (mindMaps.isEmpty) {
      return _buildEmptyState(
        icon: Icons.account_tree,
        title: 'لا توجد مخططات ذهنية',
        subtitle: 'ابدأ بإنشاء أول مخطط ذهني لك',
        buttonText: 'إنشاء مخطط ذهني',
        onPressed: _showCreateMindMapDialog,
      );
    }

    return _buildResponsiveGrid(
      constraints: constraints,
      itemCount: mindMaps.length,
      itemBuilder: (context, index) {
        final mindMap = mindMaps[index];
        return MindMapCard(
          mindMap: mindMap,
          onTap: () => _navigateToMindMapEditor(mindMap),
          onEdit: () => _showEditMindMapDialog(mindMap),
          onDelete: () => _deleteMindMap(mindMap),
        );
      },
    );
  }

  Widget _buildFavoritesTab(MindMapProvider provider, BoxConstraints constraints) {
    final favoriteMindMaps = provider.mindMaps.where((m) => m.isFavorite).toList();

    if (favoriteMindMaps.isEmpty) {
      return _buildEmptyState(
        icon: Icons.favorite_border,
        title: 'لا توجد مخططات مفضلة',
        subtitle: 'أضف مخططاتك المهمة إلى المفضلة للوصول إليها بسرعة',
        buttonText: 'تصفح المخططات',
        onPressed: () => _tabController.animateTo(0),
      );
    }

    return _buildResponsiveGrid(
      constraints: constraints,
      itemCount: favoriteMindMaps.length,
      itemBuilder: (context, index) {
        final mindMap = favoriteMindMaps[index];
        return MindMapCard(
          mindMap: mindMap,
          onTap: () => _navigateToMindMapEditor(mindMap),
          onEdit: () => _showEditMindMapDialog(mindMap),
          onDelete: () => _deleteMindMap(mindMap),
        );
      },
    );
  }

  Widget _buildSubjectsTab(MindMapProvider provider, BoxConstraints constraints) {
    final subjects = provider.subjects;

    if (subjects.isEmpty) {
      return _buildEmptyState(
        icon: Icons.school,
        title: 'لا توجد مواد دراسية',
        subtitle: 'أنشئ مواد دراسية لتنظيم مخططاتك الذهنية',
        buttonText: 'إنشاء مادة دراسية',
        onPressed: _showCreateSubjectDialog,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: subjects.length,
      itemBuilder: (context, index) {
        final subject = subjects[index];
        // حساب عدد المخططات الذهنية لهذه المادة
        final mindMapCount = provider.mindMaps
            .where((mindMap) => mindMap.subject == subject.name)
            .length;

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: SubjectCard(
            subject: subject,
            mindMapCount: mindMapCount,
            onTap: () => _navigateToSubjectDetail(subject),
            onEdit: () => _showEditSubjectDialog(subject),
            onDelete: () => _deleteSubject(subject),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required String buttonText,
    required VoidCallback onPressed,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: Colors.blue.shade300,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: onPressed,
              icon: const Icon(Icons.add),
              label: Text(buttonText),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveGrid({
    required BoxConstraints constraints,
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
  }) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
    );
  }



  // دوال التنقل والإجراءات
  void _navigateToMindMapEditor(MindMap mindMap) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MindMapEditorScreen(mindMap: mindMap),
      ),
    );
  }

  void _navigateToSubjectDetail(Subject subject) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectDetailScreen(subject: subject),
      ),
    );
  }

  void _showCreateDialog() {
    switch (_tabController.index) {
      case 0:
        _showCreateMindMapDialog();
        break;
      case 2:
        _showCreateSubjectDialog();
        break;
      default:
        _showCreateMindMapDialog();
    }
  }

  void _showCreateMindMapDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateMindMapDialog(),
    );
  }

  void _showCreateSubjectDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateSubjectDialog(),
    );
  }

  void _showEditMindMapDialog(MindMap mindMap) {
    showDialog(
      context: context,
      builder: (context) => EditMindMapDialog(mindMap: mindMap),
    );
  }

  void _showEditSubjectDialog(Subject subject) {
    showDialog(
      context: context,
      builder: (context) => EditSubjectDialog(subject: subject),
    );
  }

  void _deleteMindMap(MindMap mindMap) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المخطط الذهني'),
        content: Text('هل أنت متأكد من حذف "${mindMap.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<MindMapProvider>().deleteMindMap(mindMap.id);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deleteSubject(Subject subject) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المادة الدراسية'),
        content: Text('هل أنت متأكد من حذف "${subject.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<MindMapProvider>().deleteSubject(subject.id);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }


}
