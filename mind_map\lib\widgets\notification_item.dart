import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/notification.dart';
import '../providers/auth_provider.dart';
import '../screens/users_search_screen.dart';
import '../screens/user_profile_screen.dart';
import '../screens/home_screen.dart';
import '../services/restriction_service.dart';

class NotificationItem extends StatelessWidget {
  final AppNotification notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const NotificationItem({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (_) => onDismiss?.call(),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
          size: 28,
        ),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        elevation: notification.isRead ? 1 : 3,
        color: notification.isRead ? null : Theme.of(context).primaryColor.withValues(alpha: 0.05),
        child: ListTile(
          onTap: () {
            onTap?.call();
            _handleNotificationTap(context);
          },
          leading: _buildLeadingIcon(context),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                notification.message,
                style: TextStyle(
                  color: notification.isRead ? Colors.grey[600] : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _formatTime(notification.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!notification.isRead)
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
              const SizedBox(height: 8),
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    IconData iconData;
    Color iconColor;
    String? reactionEmoji;

    switch (notification.type) {
      case NotificationType.follow:
        iconData = Icons.person_add;
        iconColor = Colors.blue;
        break;
      case NotificationType.newPost:
        iconData = Icons.post_add;
        iconColor = Colors.green;
        break;
      case NotificationType.like:
        iconData = Icons.favorite;
        iconColor = Colors.red;
        break;
      case NotificationType.comment:
        iconData = Icons.comment;
        iconColor = Colors.orange;
        break;
      case NotificationType.reply:
        iconData = Icons.reply;
        iconColor = Colors.purple;
        break;
      case NotificationType.reaction:
        // استخراج الإيموجي من العنوان
        reactionEmoji = _extractEmojiFromTitle();
        iconData = Icons.emoji_emotions;
        iconColor = Colors.amber;
        break;
      case NotificationType.reportResolved:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case NotificationType.restriction:
        iconData = Icons.warning;
        iconColor = Colors.amber.shade400;
        break;
      case NotificationType.unrestriction:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case NotificationType.warning:
        iconData = Icons.warning;
        iconColor = Colors.yellow.shade700;
        break;
      case NotificationType.adminMessage:
        iconData = Icons.admin_panel_settings;
        iconColor = Colors.purple;
        break;
    }

    // إذا كان هناك إيموجي رد فعل، اعرضه بدلاً من الأيقونة
    if (reactionEmoji != null) {
      return CircleAvatar(
        backgroundColor: iconColor.withValues(alpha: 0.1),
        child: Text(
          reactionEmoji,
          style: const TextStyle(fontSize: 20),
        ),
      );
    }

    // عرض الأيقونة العادية
    return CircleAvatar(
      backgroundColor: iconColor.withValues(alpha: 0.1),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String? _extractEmojiFromTitle() {
    // استخراج الإيموجي من العنوان
    final title = notification.title;
    final emojiRegex = RegExp(r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]', unicode: true);
    final match = emojiRegex.firstMatch(title);
    return match?.group(0);
  }

  Widget _buildLeadingIcon(BuildContext context) {
    return Stack(
      children: [
        _buildNotificationIcon(),
        // صورة المستخدم إذا كانت متوفرة
        if (notification.fromUserAvatar != null && notification.fromUserAvatar!.isNotEmpty)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
                image: DecorationImage(
                  image: NetworkImage(notification.fromUserAvatar!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  void _handleNotificationTap(BuildContext context) {
    switch (notification.type) {
      case NotificationType.follow:
        _navigateToUserProfile(context, notification.fromUserId);
        break;
      case NotificationType.newPost:
      case NotificationType.like:
      case NotificationType.reaction:
        _navigateToPost(context, notification.relatedPostId);
        break;
      case NotificationType.comment:
      case NotificationType.reply:
        _navigateToComment(context, notification.relatedPostId, notification.relatedCommentId);
        break;
      case NotificationType.reportResolved:
        _showReportResolvedDialog(context);
        break;
      case NotificationType.restriction:
        _showRestrictionDialog(context);
        break;
      case NotificationType.unrestriction:
        _showUnrestrictionDialog(context);
        break;
      case NotificationType.warning:
        _showWarningDialog(context);
        break;
      case NotificationType.adminMessage:
        _showAdminMessageDialog(context);
        break;
    }
  }

  void _navigateToUserProfile(BuildContext context, String? userId) async {
    if (userId == null) return;

    try {
      // البحث عن المستخدم في AuthProvider
      final authProvider = context.read<AuthProvider>();
      final users = await authProvider.searchUsers(limit: 100);

      final user = users.where((u) => u.uid == userId).firstOrNull;

      if (context.mounted && user != null) {
        // التنقل إلى ملف المستخدم الشخصي
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UserProfileScreen(user: user),
          ),
        );
      } else if (context.mounted) {
        // إذا لم يتم العثور على المستخدم، اذهب لشاشة البحث
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UsersSearchScreen(),
          ),
        );
      }
    } catch (error) {
      if (context.mounted) {
        // في حالة الخطأ، اذهب لشاشة البحث
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UsersSearchScreen(),
          ),
        );
      }
    }
  }

  void _navigateToPost(BuildContext context, String? postId) {
    if (postId == null) return;

    // العودة إلى الشاشة الرئيسية مع التركيز على تبويب المنشورات
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => const HomeScreen(
          initialIndex: 0, // فهرس تبويب الرئيسية (المنشورات)
        ),
      ),
      (route) => false, // إزالة جميع الشاشات السابقة
    );
  }

  void _navigateToComment(BuildContext context, String? postId, String? commentId) {
    if (postId == null) return;

    // العودة إلى الشاشة الرئيسية مع التركيز على تبويب المنشورات
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => const HomeScreen(
          initialIndex: 0, // فهرس تبويب الرئيسية (المنشورات)
        ),
      ),
      (route) => false, // إزالة جميع الشاشات السابقة
    );
  }

  // عرض حوار تفاصيل البلاغ المحلول
  void _showReportResolvedDialog(BuildContext context) {
    final additionalData = notification.additionalData ?? {};
    final reportReason = additionalData['reportReason']?.toString() ?? 'غير محدد';
    final postTitle = additionalData['postTitle']?.toString() ?? 'المنشور';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('تم مراجعة بلاغك'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات البلاغ
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل البلاغ:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow('المنشور المبلغ عنه:', postTitle),
                    _buildDetailRow('سبب البلاغ:', _getReasonDisplayName(reportReason)),
                    _buildDetailRow('تاريخ البلاغ:', _formatNotificationDate(notification.createdAt)),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // رسالة الإدارة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.admin_panel_settings,
                             color: Colors.green.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'رد الإدارة:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'تم مراجعة بلاغك من قبل فريق الإدارة واتخاذ الإجراء المناسب. نشكرك على مساهمتك في الحفاظ على جودة المحتوى في التطبيق.',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle_outline,
                               color: Colors.green.shade700, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            'تم حل البلاغ',
                            style: TextStyle(
                              color: Colors.green.shade700,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // ملاحظة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange.shade700, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'إذا كان لديك أي استفسارات أخرى، يمكنك التواصل مع فريق الدعم.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToPost(context, notification.relatedPostId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('عرض المنشور'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  String _getReasonDisplayName(String reason) {
    switch (reason) {
      case 'spam':
        return 'محتوى مزعج';
      case 'inappropriate':
        return 'محتوى غير مناسب';
      case 'harassment':
        return 'تحرش أو إساءة';
      case 'falseInfo':
        return 'معلومات خاطئة';
      case 'copyright':
        return 'انتهاك حقوق الطبع';
      case 'violence':
        return 'عنف أو تهديد';
      case 'other':
        return 'أخرى';
      default:
        return reason;
    }
  }

  String _formatNotificationDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم مضى';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة مضت';
    } else {
      return 'الآن';
    }
  }

  // عرض نافذة تفاصيل التقييد
  void _showRestrictionDialog(BuildContext context) {
    final data = notification.additionalData;
    final reason = data?['restrictionReason'] ?? 'غير محدد';
    final days = data?['restrictionDays'] ?? 0;
    final restrictionTypes = data?['restrictionTypes'] as List<dynamic>? ?? [];

    final restrictedBy = data?['restrictedBy'] ?? 'الإدارة';
    final endDate = data?['restrictionEndDate'] != null
        ? DateTime.parse(data!['restrictionEndDate'])
        : null;

    // تحويل أنواع التقييد إلى نص عربي
    final restrictionTypesText = restrictionTypes.map((type) {
      return RestrictionService.getRestrictionTypeName(type.toString());
    }).join('، ');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.amber.shade400),
            SizedBox(width: 8),
            Text('تفاصيل التقييد'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.yellow.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.yellow.withValues(alpha: 0.4)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('📋 السبب:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
                    SizedBox(height: 4),
                    Text(reason, style: TextStyle(fontSize: 14)),
                    SizedBox(height: 12),
                    Text('🚫 نوع التقييد:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
                    SizedBox(height: 4),
                    Text(restrictionTypesText.isNotEmpty ? restrictionTypesText : 'غير محدد', style: TextStyle(fontSize: 14)),
                    SizedBox(height: 12),
                    Text('⏰ مدة التقييد:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
                    SizedBox(height: 4),
                    Text('$days ${days == 1 ? 'يوم' : 'أيام'}', style: TextStyle(fontSize: 14)),
                    if (endDate != null) ...[
                      SizedBox(height: 12),
                      Text('📅 تاريخ الانتهاء:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
                      SizedBox(height: 4),
                      Text(_formatEndDate(endDate), style: TextStyle(fontSize: 14)),
                    ],
                    SizedBox(height: 12),
                    Text('👤 تم التقييد بواسطة:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
                    SizedBox(height: 4),
                    Text(restrictedBy, style: TextStyle(fontSize: 14)),
                  ],
                ),
              ),
              SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يمكنك التواصل مع الإدارة إذا كنت تعتقد أن هذا خطأ.',
                        style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('فهمت'),
          ),
        ],
      ),
    );
  }

  // عرض نافذة إلغاء التقييد
  void _showUnrestrictionDialog(BuildContext context) {
    final data = notification.additionalData;
    final unrestrictedBy = data?['unrestrictedBy'] ?? 'الإدارة';
    final isAutomatic = data?['isAutomatic'] ?? false;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('إلغاء التقييد'),
          ],
        ),
        content: Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.celebration, color: Colors.green.shade700, size: 24),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'مرحباً بعودتك!',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18, color: Colors.green.shade700),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                isAutomatic
                    ? '⏰ تم إلغاء تقييد حسابك تلقائياً بعد انتهاء المدة المحددة.'
                    : '👤 تم إلغاء تقييد حسابك من قبل الإدارة.',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text('✅ بواسطة: $unrestrictedBy', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green.shade700)),
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '🎉 يمكنك الآن استخدام جميع ميزات التطبيق بحرية!\n📝 نتمنى منك الالتزام بقوانين المجتمع لتجنب أي تقييدات مستقبلية.',
                  style: TextStyle(fontSize: 12, color: Colors.green.shade800),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('شكراً'),
          ),
        ],
      ),
    );
  }

  // عرض نافذة التحذير
  void _showWarningDialog(BuildContext context) {
    final data = notification.additionalData;
    final reason = data?['warningReason'] ?? 'غير محدد';
    final warnedBy = data?['warnedBy'] ?? 'الإدارة';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.yellow.shade700),
            SizedBox(width: 8),
            Text('تفاصيل التحذير'),
          ],
        ),
        content: Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.yellow.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.yellow.withValues(alpha: 0.4)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('📋 السبب:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
              SizedBox(height: 4),
              Text(reason, style: TextStyle(fontSize: 14)),
              SizedBox(height: 12),
              Text('👤 التحذير من:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.yellow.shade700)),
              SizedBox(height: 4),
              Text(warnedBy, style: TextStyle(fontSize: 14)),
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.yellow.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '⚠️ يرجى الانتباه إلى سلوكك في التطبيق والالتزام بقوانين المجتمع.\n📝 تراكم التحذيرات قد يؤدي إلى تقييد حسابك.',
                  style: TextStyle(fontSize: 12, color: Colors.yellow.shade800),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('فهمت'),
          ),
        ],
      ),
    );
  }

  // عرض نافذة رسالة الإدارة
  void _showAdminMessageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.admin_panel_settings, color: Colors.purple),
            SizedBox(width: 8),
            Text('رسالة من الإدارة'),
          ],
        ),
        content: Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
          ),
          child: Text(
            notification.message,
            style: TextStyle(fontSize: 14, height: 1.5),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('شكراً'),
          ),
        ],
      ),
    );
  }

  // تنسيق تاريخ انتهاء التقييد مع الوقت واسم اليوم
  String _formatEndDate(DateTime endDate) {
    // أسماء الأيام بالعربية
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];

    // أسماء الأشهر بالعربية
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    final dayName = dayNames[endDate.weekday - 1];
    final monthName = monthNames[endDate.month - 1];

    // تنسيق الوقت (12 ساعة)
    final hour = endDate.hour == 0 ? 12 : (endDate.hour > 12 ? endDate.hour - 12 : endDate.hour);
    final minute = endDate.minute.toString().padLeft(2, '0');
    final period = endDate.hour >= 12 ? 'مساءً' : 'صباحاً';

    return '$dayName، ${endDate.day} $monthName ${endDate.year} - $hour:$minute $period';
  }
}
