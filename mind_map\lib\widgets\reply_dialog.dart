import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../providers/auth_provider.dart';
import '../providers/posts_provider.dart';

class ReplyDialog extends StatefulWidget {
  final Post post;
  final PostComment parentComment;

  const ReplyDialog({
    super.key,
    required this.post,
    required this.parentComment,
  });

  @override
  State<ReplyDialog> createState() => _ReplyDialogState();
}

class _ReplyDialogState extends State<ReplyDialog> {
  final TextEditingController _replyController = TextEditingController();
  bool _isLoading = false;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _replyController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    final hasText = _replyController.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
      print('📝 تغيير النص: hasText=$hasText, isLoading=$_isLoading');
    }
  }

  @override
  void dispose() {
    _replyController.removeListener(_onTextChanged);
    _replyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('🏗️ بناء ReplyDialog - hasText: $_hasText, isLoading: $_isLoading');
    return AlertDialog(
      title: const Text(
        'الرد على التعليق',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض التعليق الأصلي
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.grey.shade300,
                        child: Text(
                          _getAuthorInitial(widget.parentComment.authorName),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          widget.parentComment.authorName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.parentComment.content,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // حقل الرد
            const Text(
              'ردك:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _replyController,
              decoration: InputDecoration(
                hintText: 'اكتب ردك هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 3,
              autofocus: true,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading || !_hasText
              ? () {
                  print('❌ الزر معطل - isLoading: $_isLoading, hasText: $_hasText');
                }
              : () {
                  print('🔘 تم الضغط على زر الإرسال');
                  _submitReply();
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('إرسال الرد'),
        ),
      ],
    );
  }

  void _submitReply() async {
    print('🔄 بدء إرسال الرد...');
    final replyText = _replyController.text.trim();
    if (replyText.isEmpty) {
      print('❌ النص فارغ');
      return;
    }

    print('📝 النص: $replyText');
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = context.read<AuthProvider>();
      final postsProvider = context.read<PostsProvider>();
      final userModel = authProvider.userModel;

      print('👤 المستخدم: ${userModel?.firstName} ${userModel?.lastName}');
      print('📄 المنشور: ${widget.post.id}');
      print('💬 التعليق الأصلي: ${widget.parentComment.id}');

      if (userModel == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      print('🚀 إرسال الرد إلى PostsProvider...');
      await postsProvider.addReplyToComment(
        postId: widget.post.id,
        parentCommentId: widget.parentComment.id,
        content: replyText,
        authorId: userModel.uid,
        authorName: '${userModel.firstName} ${userModel.lastName}'.trim(),
        user: userModel,
      );
      print('✅ تم إرسال الرد بنجاح من ReplyDialog');

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال الرد بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الرد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }
}
