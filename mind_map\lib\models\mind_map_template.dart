import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'mind_map.dart';
import 'mind_map_node.dart';
import 'mind_map_connection.dart';

// إضافة extension للحصول على الخصائص المرئية للفئات
extension TemplateCategoryExtension on TemplateCategory {
  String get displayName {
    switch (this) {
      case TemplateCategory.mathematics:
        return 'الرياضيات';
      case TemplateCategory.physics:
        return 'الفيزياء';
      case TemplateCategory.chemistry:
        return 'الكيمياء';
      case TemplateCategory.history:
        return 'التاريخ';
      case TemplateCategory.arabic:
        return 'اللغة العربية';
      case TemplateCategory.biology:
        return 'الأحياء';
      case TemplateCategory.geography:
        return 'الجغرافيا';
      case TemplateCategory.literature:
        return 'الأدب';
      case TemplateCategory.philosophy:
        return 'الفلسفة';
      case TemplateCategory.computer:
        return 'علوم الحاسوب';
      case TemplateCategory.sociology:
        return 'علم الاجتماع';
      case TemplateCategory.psychology:
        return 'علم النفس';
      case TemplateCategory.education:
        return 'علوم التربية';
      case TemplateCategory.general:
        return 'عام';
    }
  }

  IconData get icon {
    switch (this) {
      case TemplateCategory.mathematics:
        return Icons.calculate;
      case TemplateCategory.physics:
        return Icons.science;
      case TemplateCategory.chemistry:
        return Icons.biotech;
      case TemplateCategory.history:
        return Icons.history_edu;
      case TemplateCategory.arabic:
        return Icons.translate;
      case TemplateCategory.biology:
        return Icons.eco;
      case TemplateCategory.geography:
        return Icons.public;
      case TemplateCategory.literature:
        return Icons.menu_book;
      case TemplateCategory.philosophy:
        return Icons.psychology;
      case TemplateCategory.computer:
        return Icons.computer;
      case TemplateCategory.sociology:
        return Icons.groups;
      case TemplateCategory.psychology:
        return Icons.psychology_alt;
      case TemplateCategory.education:
        return Icons.school;
      case TemplateCategory.general:
        return Icons.category;
    }
  }

  Color get color {
    switch (this) {
      case TemplateCategory.mathematics:
        return const Color(0xFF2196F3);
      case TemplateCategory.physics:
        return const Color(0xFF9C27B0);
      case TemplateCategory.chemistry:
        return const Color(0xFF4CAF50);
      case TemplateCategory.history:
        return const Color(0xFFFF9800);
      case TemplateCategory.arabic:
        return const Color(0xFFE91E63);
      case TemplateCategory.biology:
        return const Color(0xFF8BC34A);
      case TemplateCategory.geography:
        return const Color(0xFF00BCD4);
      case TemplateCategory.literature:
        return const Color(0xFF795548);
      case TemplateCategory.philosophy:
        return const Color(0xFF607D8B);
      case TemplateCategory.computer:
        return const Color(0xFF3F51B5);
      case TemplateCategory.sociology:
        return const Color(0xFFFF5722);
      case TemplateCategory.psychology:
        return const Color(0xFF9C27B0);
      case TemplateCategory.education:
        return const Color(0xFF4CAF50);
      case TemplateCategory.general:
        return const Color(0xFF757575);
    }
  }
}

// تصنيفات القوالب
enum TemplateCategory {
  mathematics('الرياضيات', Icons.calculate, Color(0xFF2196F3)),
  physics('الفيزياء', Icons.science, Color(0xFF9C27B0)),
  chemistry('الكيمياء', Icons.biotech, Color(0xFF4CAF50)),
  history('التاريخ', Icons.history_edu, Color(0xFFFF9800)),
  arabic('اللغة العربية', Icons.translate, Color(0xFFE91E63)),
  biology('الأحياء', Icons.eco, Color(0xFF8BC34A)),
  geography('الجغرافيا', Icons.public, Color(0xFF00BCD4)),
  literature('الأدب', Icons.menu_book, Color(0xFF795548)),
  philosophy('الفلسفة', Icons.psychology, Color(0xFF607D8B)),
  computer('علوم الحاسوب', Icons.computer, Color(0xFF3F51B5)),
  sociology('علم الاجتماع', Icons.groups, Color(0xFFFF5722)),
  psychology('علم النفس', Icons.psychology_alt, Color(0xFF9C27B0)),
  education('علوم التربية', Icons.school, Color(0xFF4CAF50)),
  general('عام', Icons.category, Color(0xFF757575));

  const TemplateCategory(this.displayName, this.icon, this.color);
  final String displayName;
  final IconData icon;
  final Color color;
}

// نموذج القالب
class MindMapTemplate {
  final String id;
  final String name;
  final String description;
  final TemplateCategory category;
  final String subject;
  final List<TemplateNode> nodes;
  final List<TemplateConnection> connections;
  final String? rootNodeId;
  final DateTime createdAt;
  final String? previewImagePath;
  final List<String> tags;
  final bool isCustom; // هل هو قالب مخصص من المستخدم
  final String? authorName;

  const MindMapTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.subject,
    required this.nodes,
    required this.connections,
    this.rootNodeId,
    required this.createdAt,
    this.previewImagePath,
    this.tags = const [],
    this.isCustom = false,
    this.authorName,
  });



  // إنشاء مخطط ذهني من القالب
  MindMap createMindMap({String? customTitle, String? customSubject}) {
    final uuid = const Uuid();
    final mindMapId = uuid.v4();
    final now = DateTime.now();

    // إنشاء العقد
    final Map<String, MindMapNode> mindMapNodes = {};
    final Map<String, String> oldToNewNodeIds = {};

    for (final templateNode in nodes) {
      final newNodeId = uuid.v4();
      oldToNewNodeIds[templateNode.id] = newNodeId;
      
      mindMapNodes[newNodeId] = MindMapNode(
        id: newNodeId,
        title: templateNode.title,
        description: templateNode.description,
        position: templateNode.position,
        color: templateNode.color,
        shape: templateNode.shape,
        fontSize: templateNode.fontSize,
        isRoot: templateNode.isRoot,
        createdAt: now,
        updatedAt: now,
      );
    }

    // إنشاء الروابط
    final Map<String, MindMapConnection> mindMapConnections = {};
    for (final templateConnection in connections) {
      final newConnectionId = uuid.v4();
      final newFromNodeId = oldToNewNodeIds[templateConnection.fromNodeId]!;
      final newToNodeId = oldToNewNodeIds[templateConnection.toNodeId]!;
      
      mindMapConnections[newConnectionId] = MindMapConnection(
        id: newConnectionId,
        fromNodeId: newFromNodeId,
        toNodeId: newToNodeId,
        label: templateConnection.label,
        color: templateConnection.color,
        style: templateConnection.style,
        createdAt: now,
      );
    }

    // تحديد العقدة الجذر الجديدة
    String? newRootNodeId;
    if (rootNodeId != null) {
      newRootNodeId = oldToNewNodeIds[rootNodeId];
    }

    return MindMap(
      id: mindMapId,
      title: customTitle ?? name,
      description: description,
      subject: customSubject ?? subject,
      nodes: mindMapNodes,
      connections: mindMapConnections,
      rootNodeId: newRootNodeId,
      createdAt: now,
      updatedAt: now,
      tags: List.from(tags),
      comments: {},
      isFromTemplate: true,
      sourceTemplateId: id,
      hasBeenModifiedFromTemplate: false,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'subject': subject,
      'nodes': nodes.map((node) => node.toJson()).toList(),
      'connections': connections.map((conn) => conn.toJson()).toList(),
      'rootNodeId': rootNodeId,
      'createdAt': createdAt.toIso8601String(),
      'previewImagePath': previewImagePath,
      'tags': tags,
      'isCustom': isCustom,
      'authorName': authorName,
    };
  }

  // إنشاء من JSON
  factory MindMapTemplate.fromJson(Map<String, dynamic> json) {
    return MindMapTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      category: TemplateCategory.values.firstWhere(
        (cat) => cat.name == json['category'],
        orElse: () => TemplateCategory.general,
      ),
      subject: json['subject'],
      nodes: (json['nodes'] as List)
          .map((nodeJson) => TemplateNode.fromJson(nodeJson))
          .toList(),
      connections: (json['connections'] as List)
          .map((connJson) => TemplateConnection.fromJson(connJson))
          .toList(),
      rootNodeId: json['rootNodeId'],
      createdAt: DateTime.parse(json['createdAt']),
      previewImagePath: json['previewImagePath'],
      tags: List<String>.from(json['tags'] ?? []),
      isCustom: json['isCustom'] ?? false,
      authorName: json['authorName'],
    );
  }

  // إنشاء قالب من مخطط ذهني موجود
  factory MindMapTemplate.fromMindMap(
    MindMap mindMap, {
    required String templateName,
    required String templateDescription,
    required TemplateCategory category,
    String? authorName,
  }) {
    final uuid = const Uuid();
    
    // تحويل العقد
    final List<TemplateNode> templateNodes = mindMap.nodes.values
        .map((node) => TemplateNode.fromMindMapNode(node))
        .toList();

    // تحويل الروابط
    final List<TemplateConnection> templateConnections = mindMap.connections.values
        .map((conn) => TemplateConnection.fromMindMapConnection(conn))
        .toList();

    return MindMapTemplate(
      id: uuid.v4(),
      name: templateName,
      description: templateDescription,
      category: category,
      subject: mindMap.subject,
      nodes: templateNodes,
      connections: templateConnections,
      rootNodeId: mindMap.rootNodeId,
      createdAt: DateTime.now(),
      tags: List.from(mindMap.tags),
      isCustom: true,
      authorName: authorName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MindMapTemplate && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// عقدة القالب
class TemplateNode {
  final String id;
  final String title;
  final String description;
  final Offset position;
  final Color color;
  final NodeShape shape;
  final double fontSize;
  final bool isRoot;

  const TemplateNode({
    required this.id,
    required this.title,
    this.description = '',
    required this.position,
    this.color = Colors.blue,
    this.shape = NodeShape.rectangle,
    this.fontSize = 14.0,
    this.isRoot = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'positionX': position.dx,
      'positionY': position.dy,
      'color': color.value,
      'shape': shape.name,
      'fontSize': fontSize,
      'isRoot': isRoot,
    };
  }

  factory TemplateNode.fromJson(Map<String, dynamic> json) {
    return TemplateNode(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      position: Offset(json['positionX'], json['positionY']),
      color: Color(json['color']),
      shape: NodeShape.values.firstWhere(
        (shape) => shape.name == json['shape'],
        orElse: () => NodeShape.rectangle,
      ),
      fontSize: json['fontSize']?.toDouble() ?? 14.0,
      isRoot: json['isRoot'] ?? false,
    );
  }

  factory TemplateNode.fromMindMapNode(MindMapNode node) {
    return TemplateNode(
      id: node.id,
      title: node.title,
      description: node.description,
      position: node.position,
      color: node.color,
      shape: node.shape,
      fontSize: node.fontSize,
      isRoot: node.isRoot,
    );
  }
}

// رابط القالب
class TemplateConnection {
  final String id;
  final String fromNodeId;
  final String toNodeId;
  final String label;
  final Color color;
  final ConnectionStyle style;

  const TemplateConnection({
    required this.id,
    required this.fromNodeId,
    required this.toNodeId,
    this.label = '',
    this.color = Colors.grey,
    this.style = ConnectionStyle.solid,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromNodeId': fromNodeId,
      'toNodeId': toNodeId,
      'label': label,
      'color': color.value,
      'style': style.name,
    };
  }

  factory TemplateConnection.fromJson(Map<String, dynamic> json) {
    return TemplateConnection(
      id: json['id'],
      fromNodeId: json['fromNodeId'],
      toNodeId: json['toNodeId'],
      label: json['label'] ?? '',
      color: Color(json['color']),
      style: ConnectionStyle.values.firstWhere(
        (style) => style.name == json['style'],
        orElse: () => ConnectionStyle.solid,
      ),
    );
  }

  factory TemplateConnection.fromMindMapConnection(MindMapConnection connection) {
    return TemplateConnection(
      id: connection.id,
      fromNodeId: connection.fromNodeId,
      toNodeId: connection.toNodeId,
      label: connection.label,
      color: connection.color,
      style: connection.style,
    );
  }
}
