import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/edit_request.dart';
import '../models/user_model.dart';

class EditRequestsProvider with ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  
  List<EditRequest> _sentRequests = [];      // الطلبات المرسلة
  List<EditRequest> _receivedRequests = [];  // الطلبات المستلمة
  bool _isLoading = false;
  String? _error;

  // Getters
  List<EditRequest> get sentRequests => _sentRequests;
  List<EditRequest> get receivedRequests => _receivedRequests;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل الطلبات المرسلة
  Future<void> loadSentRequests(String userId) async {
    _setLoading(true);
    try {
      final ref = _database.ref('editRequests');
      final query = ref.orderByChild('fromUserId').equalTo(userId);
      final snapshot = await query.once();

      _sentRequests = [];
      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);
        
        for (var entry in data.entries) {
          try {
            final requestData = _convertToStringDynamicMap(entry.value);
            final request = EditRequest.fromJson(requestData);
            _sentRequests.add(request);
          } catch (e) {
            print('❌ خطأ في تحويل طلب مرسل: $e');
          }
        }
        
        // ترتيب حسب التاريخ (الأحدث أولاً)
        _sentRequests.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }
      
      print('✅ تم تحميل ${_sentRequests.length} طلب مرسل');
    } catch (e) {
      print('❌ خطأ في تحميل الطلبات المرسلة: $e');
      _setError('خطأ في تحميل الطلبات المرسلة: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الطلبات المستلمة
  Future<void> loadReceivedRequests(String userId) async {
    _setLoading(true);
    try {
      final ref = _database.ref('editRequests');
      final query = ref.orderByChild('toUserId').equalTo(userId);
      final snapshot = await query.once();

      _receivedRequests = [];
      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);
        
        for (var entry in data.entries) {
          try {
            final requestData = _convertToStringDynamicMap(entry.value);
            final request = EditRequest.fromJson(requestData);
            _receivedRequests.add(request);
          } catch (e) {
            print('❌ خطأ في تحويل طلب مستلم: $e');
          }
        }
        
        // ترتيب حسب التاريخ (الأحدث أولاً)
        _receivedRequests.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }
      
      print('✅ تم تحميل ${_receivedRequests.length} طلب مستلم');
    } catch (e) {
      print('❌ خطأ في تحميل الطلبات المستلمة: $e');
      _setError('خطأ في تحميل الطلبات المستلمة: $e');
    } finally {
      _setLoading(false);
    }
  }

  // إرسال طلب تعديل جديد
  Future<EditRequest> sendEditRequest({
    required UserModel fromUser,
    required String toUserId,
    required String toUserName,
    required String postId,
    required String mindMapId,
    required String mindMapTitle,
    required String mindMapSubject,
    required String requestMessage,
  }) async {
    try {
      print('📝 إرسال طلب تعديل جديد...');

      final request = EditRequest.create(
        fromUserId: fromUser.uid,
        fromUserName: '${fromUser.firstName} ${fromUser.lastName}',
        fromUserUniversity: fromUser.university,
        toUserId: toUserId,
        toUserName: toUserName,
        postId: postId,
        mindMapId: mindMapId,
        mindMapTitle: mindMapTitle,
        mindMapSubject: mindMapSubject,
        requestMessage: requestMessage,
      );

      // حفظ في Firebase
      final ref = _database.ref('editRequests/${request.id}');
      await ref.set(request.toJson());

      // إضافة للقائمة المحلية
      _sentRequests.insert(0, request);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });

      print('✅ تم إرسال طلب التعديل بنجاح: ${request.id}');
      return request;
    } catch (e) {
      print('❌ خطأ في إرسال طلب التعديل: $e');
      _setError('خطأ في إرسال طلب التعديل: $e');
      rethrow;
    }
  }

  // قبول طلب التعديل
  Future<void> acceptRequest(String requestId, {String? responseMessage}) async {
    try {
      await _updateRequestStatus(requestId, EditRequestStatus.accepted, responseMessage);
      print('✅ تم قبول طلب التعديل: $requestId');
    } catch (e) {
      print('❌ خطأ في قبول طلب التعديل: $e');
      _setError('خطأ في قبول طلب التعديل: $e');
      rethrow;
    }
  }

  // رفض طلب التعديل
  Future<void> rejectRequest(String requestId, {String? responseMessage}) async {
    try {
      await _updateRequestStatus(requestId, EditRequestStatus.rejected, responseMessage);
      print('✅ تم رفض طلب التعديل: $requestId');
    } catch (e) {
      print('❌ خطأ في رفض طلب التعديل: $e');
      _setError('خطأ في رفض طلب التعديل: $e');
      rethrow;
    }
  }

  // بدء التعديل
  Future<void> startEditing(String requestId) async {
    try {
      await _updateRequestStatus(requestId, EditRequestStatus.inProgress, null);
      print('✅ تم بدء التعديل: $requestId');
    } catch (e) {
      print('❌ خطأ في بدء التعديل: $e');
      _setError('خطأ في بدء التعديل: $e');
      rethrow;
    }
  }

  // إرسال التعديل للموافقة مع حفظ بيانات المخطط المعدل
  Future<void> submitForApproval(String requestId, {Map<String, dynamic>? editedMindMapData}) async {
    try {
      print('🔄 إرسال التعديل للموافقة: $requestId');
      print('📊 هل توجد بيانات مخطط معدل؟ ${editedMindMapData != null}');

      final updates = <String, dynamic>{
        'status': EditRequestStatus.awaitingApproval.name,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // إضافة بيانات المخطط المعدل إذا تم توفيرها
      if (editedMindMapData != null) {
        updates['editedMindMapData'] = editedMindMapData;
        print('✅ تم إضافة بيانات المخطط المعدل للتحديث');
        print('📊 عدد العقد في المخطط المعدل: ${editedMindMapData['nodes']?.length ?? 0}');
        print('📊 عدد الروابط في المخطط المعدل: ${editedMindMapData['connections']?.length ?? 0}');
        print('📊 عنوان المخطط المعدل: ${editedMindMapData['title']}');
      } else {
        print('⚠️ لا توجد بيانات مخطط معدل لإضافتها');
      }

      print('📤 إرسال التحديث إلى Firebase...');
      await _database.ref('editRequests/$requestId').update(updates);

      // تحديث القائمة المحلية
      final index = _sentRequests.indexWhere((r) => r.id == requestId);
      if (index != -1) {
        _sentRequests[index] = _sentRequests[index].copyWith(
          status: EditRequestStatus.awaitingApproval,
        );
      }

      notifyListeners();
      print('✅ تم إرسال التعديل للموافقة: $requestId');
    } catch (e) {
      print('❌ خطأ في إرسال التعديل للموافقة: $e');
      _setError('خطأ في إرسال التعديل للموافقة: $e');
      rethrow;
    }
  }

  // إكمال التعديل ونشره
  Future<void> completeRequest(String requestId) async {
    try {
      await _updateRequestStatus(requestId, EditRequestStatus.completed, null);
      print('✅ تم إكمال طلب التعديل: $requestId');
    } catch (e) {
      print('❌ خطأ في إكمال طلب التعديل: $e');
      _setError('خطأ في إكمال طلب التعديل: $e');
      rethrow;
    }
  }

  // تحديث حالة الطلب
  Future<void> _updateRequestStatus(String requestId, EditRequestStatus status, String? responseMessage) async {
    final ref = _database.ref('editRequests/$requestId');
    
    final updates = {
      'status': status.name,
      'updatedAt': DateTime.now().toIso8601String(),
    };
    
    if (responseMessage != null) {
      updates['responseMessage'] = responseMessage;
    }
    
    await ref.update(updates);

    // تحديث القائمة المحلية
    final index = _receivedRequests.indexWhere((r) => r.id == requestId);
    if (index != -1) {
      _receivedRequests[index] = _receivedRequests[index].copyWith(
        status: status,
        responseMessage: responseMessage,
        updatedAt: DateTime.now(),
      );
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  // تحويل البيانات إلى Map<String, dynamic>
  Map<String, dynamic> _convertToStringDynamicMap(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is Map) {
      return Map<String, dynamic>.from(data);
    } else {
      throw Exception('Invalid data format');
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  void _setError(String? error) {
    _error = error;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // الحصول على عدد الطلبات المستلمة غير المقروءة
  int get unreadReceivedRequestsCount {
    try {
      return _receivedRequests.where((r) => r.status == EditRequestStatus.pending).length;
    } catch (e) {
      return 0;
    }
  }

  // الحصول على عدد الطلبات المرسلة في الانتظار
  int get pendingSentRequestsCount {
    try {
      return _sentRequests.where((r) => r.status == EditRequestStatus.pending).length;
    } catch (e) {
      return 0;
    }
  }

  // إكمال طلب التعديل (بعد الموافقة على التعديل النهائي)
  Future<void> completeEditRequest(String requestId) async {
    try {
      await _database.ref('editRequests/$requestId').update({
        'status': EditRequestStatus.completed.name,
        'completedAt': DateTime.now().toIso8601String(),
      });

      print('✅ تم إكمال طلب التعديل: $requestId');
      notifyListeners();
    } catch (e) {
      print('❌ خطأ في إكمال طلب التعديل: $e');
      rethrow;
    }
  }

  // إعادة نشر المنشور مع التعديل
  Future<void> republishWithEdit(String requestId) async {
    try {
      print('🔄 بدء إعادة النشر مع التعديل: $requestId');
      final request = _receivedRequests.firstWhere((r) => r.id == requestId);

      // الحصول على بيانات المخطط المعدل من طلب التعديل
      final requestRef = _database.ref('editRequests/$requestId');
      final requestSnapshot = await requestRef.once();

      if (!requestSnapshot.snapshot.exists) {
        print('❌ طلب التعديل غير موجود: $requestId');
        throw Exception('طلب التعديل غير موجود');
      }

      final requestData = requestSnapshot.snapshot.value as Map<dynamic, dynamic>;
      print('📊 بيانات طلب التعديل: ${requestData.keys}');

      final editedMindMapData = requestData['editedMindMapData'] as Map<dynamic, dynamic>?;

      if (editedMindMapData == null) {
        print('❌ بيانات المخطط المعدل غير موجودة في طلب التعديل');
        print('📊 البيانات المتاحة: ${requestData.keys}');
        throw Exception('بيانات المخطط المعدل غير موجودة');
      }

      print('✅ تم العثور على بيانات المخطط المعدل');
      print('📊 عدد العقد في المخطط المعدل: ${editedMindMapData['nodes']?.length ?? 0}');
      print('📊 عدد الروابط في المخطط المعدل: ${editedMindMapData['connections']?.length ?? 0}');
      print('📊 عنوان المخطط المعدل: ${editedMindMapData['title']}');

      // استبدال المخطط الذهني الأصلي بالكامل بالمخطط المعدل
      final cleanedData = Map<String, dynamic>.from(editedMindMapData);
      // التأكد من أن المعرف يبقى كما هو (المعرف الأصلي)
      cleanedData['id'] = request.mindMapId;
      // التأكد من أن حالة النشر تبقى كما هي
      cleanedData['isPublished'] = true;
      cleanedData['updatedAt'] = DateTime.now().toIso8601String();

      print('🔄 استبدال المخطط الذهني الأصلي...');
      print('📍 المسار: userProjects/${request.toUserId}/mindMaps/${request.mindMapId}');
      await _database.ref('userProjects/${request.toUserId}/mindMaps/${request.mindMapId}').set(cleanedData);
      print('✅ تم استبدال المخطط الذهني بنجاح');

      // تحديث المنشور مع معلومات المحرر وبيانات المخطط الجديدة
      await _database.ref('posts/${request.postId}').update({
        'editedByUserId': request.fromUserId,
        'editedByUserName': request.fromUserName,
        'editedAt': DateTime.now().toIso8601String(),
        'lastModified': DateTime.now().toIso8601String(),
        'mindMapData': cleanedData, // تحديث بيانات المخطط في المنشور
        'mindMapTitle': cleanedData['title'], // تحديث العنوان
        'mindMapSubject': cleanedData['subject'], // تحديث المادة
      });
      print('✅ تم تحديث المنشور مع البيانات الجديدة');

      // إكمال طلب التعديل
      await completeEditRequest(requestId);

      print('✅ تم إعادة نشر المنشور مع التعديل');

      // إشعار جميع المستمعين بالتحديث
      notifyListeners();
    } catch (e) {
      print('❌ خطأ في إعادة النشر مع التعديل: $e');
      rethrow;
    }
  }

  // مسح جميع الطلبات الواردة
  Future<void> clearReceivedRequests(String userId) async {
    try {
      _setLoading(true);

      // الحصول على جميع الطلبات الواردة للمستخدم
      final ref = _database.ref('editRequests');
      final query = ref.orderByChild('toUserId').equalTo(userId);
      final snapshot = await query.once();

      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);

        // حذف كل طلب واحد تلو الآخر
        for (var requestId in data.keys) {
          await _database.ref('editRequests/$requestId').remove();
        }
      }

      // تحديث القائمة المحلية
      _receivedRequests.clear();
      notifyListeners();

      print('✅ تم مسح جميع الطلبات الواردة');
    } catch (e) {
      print('❌ خطأ في مسح الطلبات الواردة: $e');
      _setError('خطأ في مسح الطلبات الواردة: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // مسح جميع الطلبات المرسلة
  Future<void> clearSentRequests(String userId) async {
    try {
      _setLoading(true);

      // الحصول على جميع الطلبات المرسلة من المستخدم
      final ref = _database.ref('editRequests');
      final query = ref.orderByChild('fromUserId').equalTo(userId);
      final snapshot = await query.once();

      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);

        // حذف كل طلب واحد تلو الآخر
        for (var requestId in data.keys) {
          await _database.ref('editRequests/$requestId').remove();
        }
      }

      // تحديث القائمة المحلية
      _sentRequests.clear();
      notifyListeners();

      print('✅ تم مسح جميع الطلبات المرسلة');
    } catch (e) {
      print('❌ خطأ في مسح الطلبات المرسلة: $e');
      _setError('خطأ في مسح الطلبات المرسلة: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // حذف طلب واحد
  Future<void> deleteRequest(String requestId) async {
    try {
      // حذف الطلب من Firebase
      await _database.ref('editRequests/$requestId').remove();

      // إزالة الطلب من القوائم المحلية
      _receivedRequests.removeWhere((request) => request.id == requestId);
      _sentRequests.removeWhere((request) => request.id == requestId);

      notifyListeners();

      print('✅ تم حذف الطلب: $requestId');
    } catch (e) {
      print('❌ خطأ في حذف الطلب: $e');
      rethrow;
    }
  }
}
