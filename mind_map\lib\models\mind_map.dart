import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'mind_map_node.dart';
import 'mind_map_connection.dart';
import 'mind_map_comment.dart';

class MindMap {
  final String id;
  String title;
  String description;
  String subject; // المادة الدراسية
  Map<String, MindMapNode> nodes;
  Map<String, MindMapConnection> connections;
  String? rootNodeId;
  DateTime createdAt;
  DateTime updatedAt;
  List<String> tags;
  String? filePath;
  Map<String, MindMapComment> comments; // التعليقات والملاحظات
  bool isFavorite; // هل المخطط مفضل
  bool isPublished; // هل تم نشر المخطط كمنشور
  String? publishedPostId; // معرف المنشور إذا تم النشر
  DateTime? publishedAt; // تاريخ النشر
  DateTime? lastModifiedAfterPublish; // آخر تعديل بعد النشر
  bool isFromTemplate; // هل تم إنشاء المخطط من قالب
  String? sourceTemplateId; // معرف القالب المصدر
  bool hasBeenModifiedFromTemplate; // هل تم تعديل المخطط بعد إنشائه من القالب

  MindMap({
    String? id,
    required this.title,
    this.description = '',
    this.subject = '',
    Map<String, MindMapNode>? nodes,
    Map<String, MindMapConnection>? connections,
    this.rootNodeId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    this.filePath,
    Map<String, MindMapComment>? comments,
    this.isFavorite = false,
    this.isPublished = false,
    this.publishedPostId,
    this.publishedAt,
    this.lastModifiedAfterPublish,
    this.isFromTemplate = false,
    this.sourceTemplateId,
    this.hasBeenModifiedFromTemplate = false,
  }) : id = id ?? const Uuid().v4(),
       nodes = nodes ?? {},
       connections = connections ?? {},
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       tags = tags ?? [],
       comments = comments ?? {};

  // إنشاء نسخة من المخطط مع تعديل بعض الخصائص
  MindMap copyWith({
    String? title,
    String? description,
    String? subject,
    Map<String, MindMapNode>? nodes,
    Map<String, MindMapConnection>? connections,
    String? rootNodeId,
    List<String>? tags,
    String? filePath,
    bool? isFavorite,
    bool? isPublished,
    String? publishedPostId,
    DateTime? publishedAt,
    DateTime? lastModifiedAfterPublish,
    bool? isFromTemplate,
    String? sourceTemplateId,
    bool? hasBeenModifiedFromTemplate,
  }) {
    final now = DateTime.now();
    final hasContentChanged = title != null || description != null ||
                             subject != null || nodes != null || connections != null;

    return MindMap(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      subject: subject ?? this.subject,
      nodes: nodes ?? Map.from(this.nodes),
      connections: connections ?? Map.from(this.connections),
      rootNodeId: rootNodeId ?? this.rootNodeId,
      createdAt: createdAt,
      updatedAt: now,
      tags: tags ?? List.from(this.tags),
      filePath: filePath ?? this.filePath,
      isFavorite: isFavorite ?? this.isFavorite,
      isPublished: isPublished ?? this.isPublished,
      publishedPostId: publishedPostId ?? this.publishedPostId,
      publishedAt: publishedAt ?? this.publishedAt,
      lastModifiedAfterPublish: hasContentChanged && this.isPublished
          ? now
          : (lastModifiedAfterPublish ?? this.lastModifiedAfterPublish),
      isFromTemplate: isFromTemplate ?? this.isFromTemplate,
      sourceTemplateId: sourceTemplateId ?? this.sourceTemplateId,
      hasBeenModifiedFromTemplate: hasContentChanged && this.isFromTemplate
          ? true
          : (hasBeenModifiedFromTemplate ?? this.hasBeenModifiedFromTemplate),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'subject': subject,
      'nodes': nodes.map((key, value) => MapEntry(key, value.toJson())),
      'connections': connections.map((key, value) => MapEntry(key, value.toJson())),
      'comments': comments.map((key, value) => MapEntry(key, value.toJson())),
      'rootNodeId': rootNodeId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'tags': tags,
      'filePath': filePath,
      'isFavorite': isFavorite,
      'isPublished': isPublished,
      'publishedPostId': publishedPostId,
      'publishedAt': publishedAt?.toIso8601String(),
      'lastModifiedAfterPublish': lastModifiedAfterPublish?.toIso8601String(),
      'isFromTemplate': isFromTemplate,
      'sourceTemplateId': sourceTemplateId,
      'hasBeenModifiedFromTemplate': hasBeenModifiedFromTemplate,
    };
  }

  // إنشاء من JSON
  factory MindMap.fromJson(Map<String, dynamic> json) {
    Map<String, MindMapNode> nodesMap = {};
    if (json['nodes'] != null) {
      (json['nodes'] as Map<String, dynamic>).forEach((key, value) {
        nodesMap[key] = MindMapNode.fromJson(value);
      });
    }

    Map<String, MindMapConnection> connectionsMap = {};
    if (json['connections'] != null) {
      (json['connections'] as Map<String, dynamic>).forEach((key, value) {
        connectionsMap[key] = MindMapConnection.fromJson(value);
      });
    }

    Map<String, MindMapComment> commentsMap = {};
    if (json['comments'] != null) {
      (json['comments'] as Map<String, dynamic>).forEach((key, value) {
        commentsMap[key] = MindMapComment.fromJson(value);
      });
    }

    return MindMap(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      subject: json['subject'] ?? '',
      nodes: nodesMap,
      connections: connectionsMap,
      comments: commentsMap,
      rootNodeId: json['rootNodeId'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      tags: List<String>.from(json['tags'] ?? []),
      filePath: json['filePath'],
      isFavorite: json['isFavorite'] ?? false,
      isPublished: json['isPublished'] ?? false,
      publishedPostId: json['publishedPostId'],
      publishedAt: json['publishedAt'] != null
          ? DateTime.parse(json['publishedAt'])
          : null,
      lastModifiedAfterPublish: json['lastModifiedAfterPublish'] != null
          ? DateTime.parse(json['lastModifiedAfterPublish'])
          : null,
      isFromTemplate: json['isFromTemplate'] ?? false,
      sourceTemplateId: json['sourceTemplateId'],
      hasBeenModifiedFromTemplate: json['hasBeenModifiedFromTemplate'] ?? false,
    );
  }

  // تحويل إلى JSON string
  String toJsonString() {
    return jsonEncode(toJson());
  }

  // إنشاء من JSON string
  factory MindMap.fromJsonString(String jsonString) {
    return MindMap.fromJson(jsonDecode(jsonString));
  }

  // إضافة عقدة جديدة
  void addNode(MindMapNode node) {
    nodes[node.id] = node;
    _markAsModified();

    // إذا لم تكن هناك عقدة جذر، اجعل هذه العقدة هي الجذر
    rootNodeId ??= node.id;
  }

  // إزالة عقدة
  void removeNode(String nodeId) {
    final node = nodes[nodeId];
    if (node == null) return;

    // إزالة العقدة من العقدة الأب
    if (node.parentId != null) {
      final parent = nodes[node.parentId];
      parent?.removeChild(nodeId);
    }

    // إزالة جميع العقد الفرعية
    final childrenToRemove = List<String>.from(node.childrenIds);
    for (String childId in childrenToRemove) {
      removeNode(childId);
    }

    // إزالة جميع الاتصالات المرتبطة بهذه العقدة
    connections.removeWhere((key, connection) =>
      connection.fromNodeId == nodeId || connection.toNodeId == nodeId);

    // إزالة العقدة نفسها
    nodes.remove(nodeId);

    // إذا كانت العقدة المحذوفة هي الجذر، ابحث عن جذر جديد
    if (rootNodeId == nodeId) {
      rootNodeId = nodes.isNotEmpty ? nodes.keys.first : null;
    }

    _markAsModified();
  }

  // الحصول على عقدة بواسطة المعرف
  MindMapNode? getNode(String nodeId) {
    return nodes[nodeId];
  }

  // الحصول على العقدة الجذر
  MindMapNode? getRootNode() {
    return rootNodeId != null ? nodes[rootNodeId] : null;
  }

  // الحصول على العقد الفرعية لعقدة معينة
  List<MindMapNode> getChildren(String nodeId) {
    final node = nodes[nodeId];
    if (node == null) return [];
    
    return node.childrenIds
        .map((id) => nodes[id])
        .where((node) => node != null)
        .cast<MindMapNode>()
        .toList();
  }

  // إضافة عقدة فرعية
  void addChildNode(String parentId, MindMapNode childNode) {
    childNode.parentId = parentId;
    addNode(childNode);
    
    final parent = nodes[parentId];
    parent?.addChild(childNode.id);
  }

  // تحديث عقدة
  void updateNode(String nodeId, MindMapNode updatedNode) {
    if (nodes.containsKey(nodeId)) {
      nodes[nodeId] = updatedNode;
      _markAsModified();
    }
  }

  // إضافة تاغ
  void addTag(String tag) {
    if (!tags.contains(tag)) {
      tags.add(tag);
      _markAsModified();
    }
  }

  // إزالة تاغ
  void removeTag(String tag) {
    tags.remove(tag);
    _markAsModified();
  }

  // تحديث المعلومات الأساسية
  void updateInfo({
    String? newTitle,
    String? newDescription,
    String? newSubject,
  }) {
    if (newTitle != null) title = newTitle;
    if (newDescription != null) description = newDescription;
    if (newSubject != null) subject = newSubject;
    _markAsModified();
  }

  // تبديل حالة المفضلة
  void toggleFavorite() {
    isFavorite = !isFavorite;
    updatedAt = DateTime.now();
  }

  // تحديد المخطط كمنشور
  void markAsPublished(String postId) {
    isPublished = true;
    publishedPostId = postId;
    publishedAt = DateTime.now();
    lastModifiedAfterPublish = null; // إعادة تعيين عداد التعديل
    updatedAt = DateTime.now();
  }

  // إلغاء حالة النشر (عند حذف المنشور)
  void unmarkAsPublished() {
    isPublished = false;
    publishedPostId = null;
    publishedAt = null;
    lastModifiedAfterPublish = null;
    updatedAt = DateTime.now();
  }

  // التحقق من إمكانية النشر
  bool get canBePublished {
    // إذا كان من قالب ولم يتم تعديله، لا يمكن نشره
    if (isFromTemplate && !hasBeenModifiedFromTemplate) {
      return false;
    }

    if (!isPublished) return true; // لم يتم نشره من قبل
    if (lastModifiedAfterPublish != null) return true; // تم تعديله بعد النشر
    return false; // منشور ولم يتم تعديله
  }

  // الحصول على حالة النشر كنص
  String get publishStatus {
    if (isFromTemplate && !hasBeenModifiedFromTemplate) {
      return 'قالب غير معدل';
    }
    if (!isPublished) return 'غير منشور';
    if (lastModifiedAfterPublish != null) return 'تم التعديل بعد النشر';
    return 'منشور';
  }

  // التحقق من وجود تعديلات بعد النشر
  bool get hasModificationsAfterPublish {
    return isPublished && lastModifiedAfterPublish != null;
  }

  // دالة مساعدة لتحديث وقت التعديل وتتبع التعديلات بعد النشر
  void _markAsModified() {
    final now = DateTime.now();
    updatedAt = now;

    // إذا كان المخطط منشوراً، سجل وقت التعديل
    if (isPublished) {
      lastModifiedAfterPublish = now;
    }

    // إذا كان المخطط من قالب، سجل أنه تم تعديله
    if (isFromTemplate) {
      hasBeenModifiedFromTemplate = true;
    }
  }

  // تعيين حالة المفضلة
  void setFavorite(bool favorite) {
    isFavorite = favorite;
    updatedAt = DateTime.now();
  }

  // الحصول على عدد العقد
  int get nodeCount => nodes.length;

  // الحصول على عدد الروابط
  int get connectionCount => connections.length;

  // التحقق من وجود عقد
  bool get isEmpty => nodes.isEmpty;

  // التحقق من عدم وجود عقد
  bool get isNotEmpty => nodes.isNotEmpty;

  @override
  String toString() {
    return 'MindMap(id: $id, title: $title, nodeCount: $nodeCount)';
  }

  // إضافة اتصال
  void addConnection(MindMapConnection connection) {
    connections[connection.id] = connection;
    updatedAt = DateTime.now();
  }

  // إزالة اتصال
  void removeConnection(String connectionId) {
    connections.remove(connectionId);
    updatedAt = DateTime.now();
  }

  // الحصول على اتصالات عقدة معينة
  List<MindMapConnection> getNodeConnections(String nodeId) {
    return connections.values.where((connection) =>
      connection.fromNodeId == nodeId || connection.toNodeId == nodeId).toList();
  }

  // الحصول على الاتصالات الصادرة من عقدة
  List<MindMapConnection> getOutgoingConnections(String nodeId) {
    return connections.values.where((connection) =>
      connection.fromNodeId == nodeId).toList();
  }

  // الحصول على الاتصالات الواردة إلى عقدة
  List<MindMapConnection> getIncomingConnections(String nodeId) {
    return connections.values.where((connection) =>
      connection.toNodeId == nodeId).toList();
  }

  // إدارة التعليقات

  // إضافة تعليق جديد
  void addComment(MindMapComment comment) {
    comments[comment.id] = comment;
    updatedAt = DateTime.now();
  }

  // حذف تعليق
  void removeComment(String commentId) {
    comments.remove(commentId);
    updatedAt = DateTime.now();
  }

  // تحديث تعليق
  void updateComment(MindMapComment updatedComment) {
    if (comments.containsKey(updatedComment.id)) {
      comments[updatedComment.id] = updatedComment;
      updatedAt = DateTime.now();
    }
  }

  // الحصول على تعليق بالمعرف
  MindMapComment? getComment(String commentId) {
    return comments[commentId];
  }

  // الحصول على جميع التعليقات
  List<MindMapComment> getAllComments() {
    return comments.values.toList();
  }

  // الحصول على التعليقات المرتبطة بعقدة معينة
  List<MindMapComment> getCommentsForNode(String nodeId) {
    return comments.values.where((comment) => comment.nodeId == nodeId).toList();
  }

  // الحصول على التعليقات العامة (غير مرتبطة بعقدة)
  List<MindMapComment> getGeneralComments() {
    return comments.values.where((comment) => comment.nodeId == null).toList();
  }

  // الحصول على التعليقات حسب النوع
  List<MindMapComment> getCommentsByType(CommentType type) {
    return comments.values.where((comment) => comment.type == type).toList();
  }

  // عدد التعليقات
  int get commentCount => comments.length;

  // التحقق من وجود تعليقات
  bool get hasComments => comments.isNotEmpty;

  // التحقق من وجود تعليقات لعقدة معينة
  bool hasCommentsForNode(String nodeId) {
    return comments.values.any((comment) => comment.nodeId == nodeId);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MindMap && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
