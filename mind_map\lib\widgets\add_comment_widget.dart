import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../providers/auth_provider.dart';
import '../providers/posts_provider.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';

class AddCommentWidget extends StatefulWidget {
  final Post post;

  const AddCommentWidget({
    super.key,
    required this.post,
  });

  @override
  State<AddCommentWidget> createState() => _AddCommentWidgetState();
}

class _AddCommentWidgetState extends State<AddCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isExpanded = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _commentController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _addComment() async {
    if (_commentController.text.trim().isEmpty) return;

    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userModel = authProvider.userModel;

    if (userModel == null) return;

    // التحقق من تقييد التعليق
    final restriction = await RestrictionService.checkCommentingRestriction(userModel.uid);
    if (restriction != null && mounted) {
      await RestrictionDialog.show(context, restriction, 'التعليق');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await postsProvider.addComment(
        postId: widget.post.id,
        content: _commentController.text.trim(),
        author: userModel,
      );

      _commentController.clear();
      _focusNode.unfocus();
      setState(() {
        _isExpanded = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إضافة التعليق: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم الحالي
          CircleAvatar(
            radius: 18,
            backgroundColor: Colors.blue.shade100,
            child: Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final userModel = authProvider.userModel;
                final userName = userModel != null
                    ? '${userModel.firstName} ${userModel.lastName}'.trim()
                    : authProvider.user?.displayName ?? 'المستخدم';
                return Text(
                  _getAuthorInitial(userName),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 10),
          
          // مربع النص
          Expanded(
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _isExpanded ? Colors.blue.shade300 : Colors.grey.shade300,
                    ),
                  ),
                  child: TextField(
                    controller: _commentController,
                    focusNode: _focusNode,
                    maxLines: _isExpanded ? 3 : 1,
                    decoration: InputDecoration(
                      hintText: 'اكتب تعليقاً...',
                      hintStyle: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                    ),
                    onTap: () async {
                      // التحقق من تقييد التعليق عند النقر
                      final authProvider = context.read<AuthProvider>();
                      final userModel = authProvider.userModel;

                      if (userModel != null) {
                        final restriction = await RestrictionService.checkCommentingRestriction(userModel.uid);
                        if (restriction != null && mounted) {
                          _focusNode.unfocus();
                          await RestrictionDialog.show(context, restriction, 'التعليق');
                          return;
                        }
                      }

                      setState(() {
                        _isExpanded = true;
                      });
                    },
                    onChanged: (value) {
                      setState(() {});
                    },
                  ),
                ),
                
                // أزرار الإجراءات
                if (_isExpanded)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {
                            _commentController.clear();
                            _focusNode.unfocus();
                            setState(() {
                              _isExpanded = false;
                            });
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _commentController.text.trim().isEmpty || _isLoading
                              ? null
                              : _addComment,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text(
                                  'نشر',
                                  style: TextStyle(fontSize: 14),
                                ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }
}
