import 'package:flutter/material.dart';
import '../models/reaction.dart';

class ReactionPicker extends StatelessWidget {
  final Function(ReactionType) onReactionSelected;
  final ReactionType? currentReaction;

  const ReactionPicker({
    super.key,
    required this.onReactionSelected,
    this.currentReaction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: ReactionType.values.map((type) {
          final isSelected = currentReaction == type;
          return GestureDetector(
            onTap: () => onReactionSelected(type),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 3),
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue.shade50 : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                border: isSelected
                    ? Border.all(color: Colors.blue.shade300, width: 1.5)
                    : null,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    type.emoji,
                    style: TextStyle(
                      fontSize: isSelected ? 20 : 16,
                    ),
                  ),
                  const SizedBox(height: 1),
                  Text(
                    type.label,
                    style: TextStyle(
                      fontSize: 9,
                      color: isSelected ? Colors.blue.shade700 : Colors.grey.shade600,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

class ReactionButton extends StatelessWidget {
  final ReactionSummary reactions;
  final String? currentUserId;
  final Function(ReactionType) onReactionTap;
  final VoidCallback? onLongPress;

  const ReactionButton({
    super.key,
    required this.reactions,
    this.currentUserId,
    required this.onReactionTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final userReaction = currentUserId != null 
        ? reactions.getUserReaction(currentUserId!) 
        : null;
    final hasReacted = userReaction != null;

    return GestureDetector(
      onTap: () {
        if (hasReacted) {
          // إذا كان المستخدم قد تفاعل، قم بإزالة التفاعل
          onReactionTap(userReaction);
        } else {
          // إذا لم يتفاعل، افتح منتقي ردود الفعل
          if (onLongPress != null) {
            onLongPress!();
          }
        }
      },
      onLongPress: onLongPress,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: hasReacted ? Colors.blue.shade50 : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (hasReacted) ...[
              Text(
                userReaction.emoji,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(width: 3),
            ] else ...[
              Icon(
                Icons.favorite_border,
                color: Colors.grey.shade600,
                size: 16,
              ),
              const SizedBox(width: 3),
            ],
            if (reactions.totalCount > 0)
              Text(
                '${reactions.totalCount}',
                style: TextStyle(
                  color: hasReacted ? Colors.blue.shade700 : Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class ReactionSummaryWidget extends StatelessWidget {
  final ReactionSummary reactions;
  final VoidCallback? onTap;

  const ReactionSummaryWidget({
    super.key,
    required this.reactions,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (reactions.totalCount == 0) {
      return const SizedBox.shrink();
    }

    final sortedReactions = reactions.counts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // عرض أول 3 ردود فعل
            ...sortedReactions.take(3).map((entry) => Padding(
              padding: const EdgeInsets.only(left: 1),
              child: Text(
                entry.key.emoji,
                style: const TextStyle(fontSize: 12),
              ),
            )),
            const SizedBox(width: 3),
            Text(
              '${reactions.totalCount}',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void showReactionPicker({
  required BuildContext context,
  required Function(ReactionType) onReactionSelected,
  ReactionType? currentReaction,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    builder: (context) => Container(
      margin: const EdgeInsets.all(16),
      child: ReactionPicker(
        onReactionSelected: (type) {
          Navigator.pop(context);
          onReactionSelected(type);
        },
        currentReaction: currentReaction,
      ),
    ),
  );
}
