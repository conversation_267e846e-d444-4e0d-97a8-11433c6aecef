import 'package:firebase_database/firebase_database.dart';
import '../models/user_model.dart';
import '../services/restriction_notification_service.dart';

class RestrictionService {
  static final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // التحقق من تقييد معين للمستخدم
  static Future<Map<String, dynamic>?> checkUserRestriction(String userId, String restrictionType) async {
    try {
      final userSnapshot = await _database.child('users/$userId').get();
      if (!userSnapshot.exists) return null;

      final userData = userSnapshot.value as Map<dynamic, dynamic>;
      final user = UserModel.fromMap(Map<String, dynamic>.from(userData));

      // التحقق من وجود تقييد نشط
      if (user.isRestricted == true) {
        final restrictionEndDate = user.restrictionEndDate;
        if (restrictionEndDate != null && restrictionEndDate.isAfter(DateTime.now())) {
          final restrictionTypes = user.restrictionTypes ?? [];
          
          // التحقق من نوع التقييد المحدد
          if (restrictionTypes.contains(restrictionType)) {
            return {
              'isRestricted': true,
              'reason': user.restrictionReason ?? 'تقييد من الإدارة',
              'endDate': restrictionEndDate,
              'restrictionTypes': restrictionTypes,
              'restrictedAt': user.restrictedAt,
            };
          }
        }
      }

      return null;
    } catch (e) {
      print('❌ خطأ في التحقق من التقييد: $e');
      return null;
    }
  }

  // التحقق من تقييد النشر
  static Future<Map<String, dynamic>?> checkPostingRestriction(String userId) async {
    return await checkUserRestriction(userId, 'posting');
  }

  // التحقق من تقييد التعليق
  static Future<Map<String, dynamic>?> checkCommentingRestriction(String userId) async {
    return await checkUserRestriction(userId, 'commenting');
  }

  // التحقق من تقييد إنشاء المخططات
  static Future<Map<String, dynamic>?> checkCreatingMindMapsRestriction(String userId) async {
    return await checkUserRestriction(userId, 'creating_mindmaps');
  }

  // التحقق من تقييد تعديل المخططات
  static Future<Map<String, dynamic>?> checkEditingMindMapsRestriction(String userId) async {
    return await checkUserRestriction(userId, 'editing_mindmaps');
  }

  // التحقق من تقييد المتابعة
  static Future<Map<String, dynamic>?> checkFollowingRestriction(String userId) async {
    return await checkUserRestriction(userId, 'following');
  }

  // التحقق من تقييد تعديل الملف الشخصي
  static Future<Map<String, dynamic>?> checkProfileEditingRestriction(String userId) async {
    return await checkUserRestriction(userId, 'profile_editing');
  }

  // التحقق من تقييد تغيير كلمة السر
  static Future<Map<String, dynamic>?> checkPasswordChangeRestriction(String userId) async {
    return await checkUserRestriction(userId, 'password_change');
  }

  // التحقق من تقييد إعدادات الخصوصية
  static Future<Map<String, dynamic>?> checkPrivacySettingsRestriction(String userId) async {
    return await checkUserRestriction(userId, 'privacy_settings');
  }

  // التحقق من تقييد طلب التعديل والإبلاغ
  static Future<Map<String, dynamic>?> checkReportingAndEditRequestRestriction(String userId) async {
    return await checkUserRestriction(userId, 'reporting_edit_request');
  }

  // عرض رسالة التقييد
  static String getRestrictionMessage(Map<String, dynamic> restriction) {
    final reason = restriction['reason'] as String;
    final endDate = restriction['endDate'] as DateTime;
    final restrictedAt = restriction['restrictedAt'] as DateTime?;
    
    final daysRemaining = endDate.difference(DateTime.now()).inDays;
    final hoursRemaining = endDate.difference(DateTime.now()).inHours % 24;
    
    String timeRemaining;
    if (daysRemaining > 0) {
      timeRemaining = '$daysRemaining يوم';
      if (hoursRemaining > 0) {
        timeRemaining += ' و $hoursRemaining ساعة';
      }
    } else if (hoursRemaining > 0) {
      timeRemaining = '$hoursRemaining ساعة';
    } else {
      final minutesRemaining = endDate.difference(DateTime.now()).inMinutes;
      timeRemaining = '$minutesRemaining دقيقة';
    }

    return '''أنت مقيد من هذا الإجراء

السبب: $reason

المدة المتبقية: $timeRemaining

تاريخ انتهاء التقييد: ${_formatDate(endDate)}''';
  }

  // تنسيق التاريخ
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} - ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  // الحصول على اسم نوع التقييد بالعربية
  static String getRestrictionTypeName(String restrictionType) {
    switch (restrictionType) {
      case 'posting': return 'النشر';
      case 'commenting': return 'التعليق';
      case 'creating_mindmaps': return 'إنشاء المخططات الذهنية';
      case 'editing_mindmaps': return 'تعديل المخططات الذهنية';
      case 'following': return 'متابعة المستخدمين';
      case 'profile_editing': return 'تعديل المعلومات الشخصية';
      case 'password_change': return 'تغيير كلمة السر';
      case 'privacy_settings': return 'تعديل إعدادات الخصوصية';
      case 'reporting_edit_request': return 'طلب التعديل والإبلاغ';
      default: return restrictionType;
    }
  }

  // التحقق من انتهاء صلاحية التقييد وإزالته تلقائياً
  static Future<void> checkAndRemoveExpiredRestrictions(String userId) async {
    try {
      final userSnapshot = await _database.child('users/$userId').get();
      if (!userSnapshot.exists) return;

      final userData = userSnapshot.value as Map<dynamic, dynamic>;
      final user = UserModel.fromMap(Map<String, dynamic>.from(userData));

      if (user.isRestricted == true) {
        final restrictionEndDate = user.restrictionEndDate;
        if (restrictionEndDate != null && restrictionEndDate.isBefore(DateTime.now())) {
          // إزالة التقييد المنتهي الصلاحية
          final updateData = {
            'isRestricted': false,
            'restrictionReason': null,
            'restrictedAt': null,
            'restrictionEndDate': null,
            'restrictionDays': null,
            'restrictionTypes': null,
            'restrictedBy': null,
            'autoUnrestrictedAt': DateTime.now().toIso8601String(),
          };

          await _database.child('users/$userId').update(updateData);

          // تسجيل الإجراء
          final actionData = {
            'actionType': 'auto_unrestrict',
            'actionDate': DateTime.now().toIso8601String(),
            'actionBy': 'system',
            'details': 'تم إلغاء التقييد تلقائياً بعد انتهاء المدة المحددة',
          };

          await _database.child('user_actions/$userId').push().set(actionData);

          // إرسال إشعار إلغاء التقييد التلقائي
          await RestrictionNotificationService.sendUnrestrictionNotification(
            userId: userId,
            unrestrictedBy: 'النظام',
            isAutomatic: true,
          );

          print('✅ تم إلغاء تقييد المستخدم $userId تلقائياً بعد انتهاء المدة');
        }
      }
    } catch (e) {
      print('❌ خطأ في التحقق من انتهاء التقييد: $e');
    }
  }
}
