class FollowSuggestionModel {
  final String id;
  final String userId;
  final String userName;
  final String userEmail;
  final String university;
  final String major;
  final int followersCount;
  final int postsCount;
  final String? profileImageUrl;
  final List<String> commonInterests;
  final String suggestionReason;
  final bool isFollowing;
  final DateTime createdAt;

  FollowSuggestionModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.university,
    required this.major,
    required this.followersCount,
    required this.postsCount,
    this.profileImageUrl,
    required this.commonInterests,
    required this.suggestionReason,
    required this.isFollowing,
    required this.createdAt,
  });

  factory FollowSuggestionModel.fromJson(Map<String, dynamic> json) {
    return FollowSuggestionModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userEmail: json['userEmail'] ?? '',
      university: json['university'] ?? '',
      major: json['major'] ?? '',
      followersCount: json['followersCount'] ?? 0,
      postsCount: json['postsCount'] ?? 0,
      profileImageUrl: json['profileImageUrl'],
      commonInterests: List<String>.from(json['commonInterests'] ?? []),
      suggestionReason: json['suggestionReason'] ?? '',
      isFollowing: json['isFollowing'] ?? false,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'university': university,
      'major': major,
      'followersCount': followersCount,
      'postsCount': postsCount,
      'profileImageUrl': profileImageUrl,
      'commonInterests': commonInterests,
      'suggestionReason': suggestionReason,
      'isFollowing': isFollowing,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  FollowSuggestionModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userEmail,
    String? university,
    String? major,
    int? followersCount,
    int? postsCount,
    String? profileImageUrl,
    List<String>? commonInterests,
    String? suggestionReason,
    bool? isFollowing,
    DateTime? createdAt,
  }) {
    return FollowSuggestionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      university: university ?? this.university,
      major: major ?? this.major,
      followersCount: followersCount ?? this.followersCount,
      postsCount: postsCount ?? this.postsCount,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      commonInterests: commonInterests ?? this.commonInterests,
      suggestionReason: suggestionReason ?? this.suggestionReason,
      isFollowing: isFollowing ?? this.isFollowing,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'FollowSuggestionModel(id: $id, userName: $userName, university: $university, major: $major)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FollowSuggestionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أسباب اقتراح المتابعة
enum SuggestionReason {
  sameUniversity('نفس الجامعة'),
  sameMajor('نفس التخصص'),
  commonInterests('اهتمامات مشتركة'),
  popularUser('مستخدم شائع'),
  activeUser('مستخدم نشط'),
  mutualFollowers('متابعون مشتركون'),
  recentJoined('انضم حديثاً'),
  topContributor('مساهم متميز');

  const SuggestionReason(this.displayName);
  final String displayName;
}
