import 'package:flutter/material.dart';
import 'dart:math';
import '../models/mind_map.dart';
import '../models/mind_map_node.dart';
import '../models/mind_map_connection.dart';
import '../models/mind_map_comment.dart';
import 'connections_painter.dart';

class MindMapCanvas extends StatefulWidget {
  final MindMap mindMap;
  final bool isEditing;
  final MindMapNode? selectedNode;
  final MindMapConnection? selectedConnection;
  final Function(MindMapNode?) onNodeSelected;
  final Function(MindMapConnection?) onConnectionSelected;
  final Function(MindMapNode, Offset) onNodeMoved;
  final Function(MindMapNode) onNodeDoubleTap;
  final Function(Offset) onCanvasTap;
  final Function(MindMapNode, MindMapNode) onConnectionCreated;
  final TransformationController? transformationController;
  final bool showComments;

  const MindMapCanvas({
    super.key,
    required this.mindMap,
    required this.isEditing,
    required this.selectedNode,
    this.selectedConnection,
    required this.onNodeSelected,
    required this.onConnectionSelected,
    required this.onNodeMoved,
    required this.onNodeDoubleTap,
    required this.onCanvasTap,
    required this.onConnectionCreated,
    this.transformationController,
    this.showComments = false,
  });

  @override
  State<MindMapCanvas> createState() => _MindMapCanvasState();
}

class _MindMapCanvasState extends State<MindMapCanvas> {
  late final TransformationController _transformationController;
  MindMapNode? _draggedNode;

  @override
  void initState() {
    super.initState();
    _transformationController = widget.transformationController ?? TransformationController();
  }

  @override
  void dispose() {
    // فقط تحرير controller إذا كان محلي
    if (widget.transformationController == null) {
      _transformationController.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(MindMapCanvas oldWidget) {
    super.didUpdateWidget(oldWidget);
    // إزالة الحركة التلقائية للكاميرا - الكاميرا ستبقى ثابتة
  }

  // تقييد التحويل لمنع تغطية شريط الأدوات
  void _constrainTransformation() {
    final matrix = _transformationController.value;
    final translation = matrix.getTranslation();
    final scale = matrix.getMaxScaleOnAxis();

    // الحد الأدنى والأقصى للترجمة
    const double minY = -50.0;  // منع الصعود أكثر من اللازم
    const double maxY = 100.0;  // منع النزول أكثر من اللازم

    bool needsUpdate = false;

    // تقييد الحركة العمودية
    if (translation.y < minY) {
      translation.y = minY;
      needsUpdate = true;
    } else if (translation.y > maxY) {
      translation.y = maxY;
      needsUpdate = true;
    }

    if (needsUpdate) {
      _transformationController.value = Matrix4.identity()
        ..translate(translation.x, translation.y)
        ..scale(scale);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[300], // خلفية رمادية فاتحة
      child: InteractiveViewer(
        transformationController: _transformationController,
        boundaryMargin: const EdgeInsets.only(
          top: 140,    // مساحة أكبر للشريط العلوي
          bottom: 120, // مساحة للشريط السفلي
          left: 30,
          right: 30,
        ),
        minScale: 0.3,
        maxScale: 3.0, // تقليل التكبير الأقصى
        constrained: false,
        // تحسين سرعة التنقل والتكبير
        scaleEnabled: true,
        panEnabled: true,
        // تقليل المقاومة لجعل الحركة أسرع
        clipBehavior: Clip.hardEdge, // منع المحتوى من الخروج خارج الحدود
        onInteractionUpdate: (details) {
          // التأكد من عدم تجاوز الحدود المسموحة
          _constrainTransformation();
        },
        child: GestureDetector(
          onTap: () => widget.onCanvasTap(Offset.zero),
          child: CustomPaint(
            painter: MindMapPainter(
              mindMap: widget.mindMap,
              selectedNode: widget.selectedNode,
              isEditing: widget.isEditing,
            ),
            child: SizedBox(
              width: 2000,
              height: 2000,
              child: Stack(
                children: [
                  // رسم الخطوط أولاً (تحت العقد)
                  GestureDetector(
                    onTapDown: (details) => _handleConnectionTap(details.localPosition),
                    child: CustomPaint(
                      size: const Size(2000, 2000),
                      painter: ConnectionsPainter(
                        connections: widget.mindMap.connections,
                        nodes: widget.mindMap.nodes,
                        selectedConnection: widget.selectedConnection,
                      ),
                    ),
                  ),
                  // رسالة عندما لا توجد عقد
                  if (widget.mindMap.nodes.isEmpty)
                    const Positioned(
                      left: 950,
                      top: 950,
                      child: Text(
                        'اضغط على + لإضافة عقدة جديدة',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ),
                  // عرض جميع العقد
                  for (final node in widget.mindMap.nodes.values)
                    _buildNodeWidget(node),

                  // عرض التعليقات العامة (غير المرتبطة بعقد)
                  if (widget.showComments)
                    ..._buildGeneralComments(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNodeWidget(MindMapNode node) {
    final isSelected = widget.selectedNode?.id == node.id;

    // التأكد من أن العقدة داخل حدود الكانفاس
    final left = (node.position.dx - node.width / 2).clamp(0.0, 2000.0 - node.width);
    final top = (node.position.dy - node.height / 2).clamp(0.0, 2000.0 - node.height);

    return Positioned(
      left: left,
      top: top,
      width: node.width,
      height: node.height,
      child: GestureDetector(
        onTap: () => widget.onNodeSelected(node),
        onDoubleTap: () => widget.onNodeDoubleTap(node),
        onPanStart: widget.isEditing ? (details) {
          _draggedNode = node;
          // تحسين الأداء أثناء السحب
          setState(() {});
        } : null,
        onPanUpdate: widget.isEditing ? (details) {
          if (_draggedNode != null) {
            // تحسين السرعة بضرب الحركة في معامل تسريع
            final speedMultiplier = 1.5;
            final newPosition = Offset(
              node.position.dx + (details.delta.dx * speedMultiplier),
              node.position.dy + (details.delta.dy * speedMultiplier),
            );
            widget.onNodeMoved(node, newPosition);
          }
        } : null,
        onPanEnd: widget.isEditing ? (details) {
          _draggedNode = null;
          // تحديث نهائي للواجهة
          setState(() {});
        } : null,
        child: Stack(
          children: [
            Container(
              width: node.width,
              height: node.height,
              decoration: BoxDecoration(
                color: node.color,
                borderRadius: BorderRadius.circular(12),
                border: isSelected
                    ? Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 3,
                      )
                    : Border.all(
                        color: Colors.grey.withValues(alpha: 0.5),
                        width: 2,
                      ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                  if (isSelected)
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.4),
                      blurRadius: 12,
                      offset: const Offset(0, 0),
                    ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Center(
                  child: Text(
                    node.title,
                    style: TextStyle(
                      color: _getTextColor(node.color),
                      fontSize: 18, // زيادة حجم الخط لوضوح أفضل في PDF
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.5),
                          offset: const Offset(1, 1),
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
            // مؤشر التعليقات
            if (widget.showComments && _hasCommentsForNode(node.id))
              Positioned(
                top: -8,
                right: -8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      '${_getCommentsCountForNode(node.id)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getTextColor(Color backgroundColor) {
    // حساب اللون المناسب للنص بناءً على لون الخلفية
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  bool _hasCommentsForNode(String nodeId) {
    return widget.mindMap.comments.values.any((comment) => comment.nodeId == nodeId);
  }

  int _getCommentsCountForNode(String nodeId) {
    return widget.mindMap.comments.values.where((comment) => comment.nodeId == nodeId).length;
  }

  List<Widget> _buildGeneralComments() {
    final generalComments = widget.mindMap.comments.values
        .where((comment) => !comment.isAttachedToNode)
        .toList();

    return generalComments.map((comment) {
      final typeColor = Color(int.parse(comment.type.color.substring(1), radix: 16) + 0xFF000000);

      return Positioned(
        left: comment.position?.dx ?? 50,
        top: comment.position?.dy ?? 50,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 200),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: typeColor, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(comment.type.icon, style: const TextStyle(fontSize: 14)),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      comment.type.displayName,
                      style: TextStyle(
                        color: typeColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Content
              Text(
                comment.content.length > 50
                    ? '${comment.content.substring(0, 50)}...'
                    : comment.content,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  // دالة للتعامل مع النقر على الروابط
  void _handleConnectionTap(Offset position) {
    if (!widget.isEditing) return;

    // البحث عن الربط الأقرب للنقطة المنقورة
    MindMapConnection? tappedConnection;
    double minDistance = double.infinity;
    const double tapThreshold = 20.0; // المسافة القصوى للنقر على الربط

    for (final connection in widget.mindMap.connections.values) {
      final fromNode = widget.mindMap.nodes[connection.fromNodeId];
      final toNode = widget.mindMap.nodes[connection.toNodeId];

      if (fromNode == null || toNode == null) continue;

      // حساب المسافة من النقطة المنقورة إلى الخط
      final distance = _distanceToLine(
        position,
        Offset(fromNode.position.dx + fromNode.width / 2,
               fromNode.position.dy + fromNode.height / 2),
        Offset(toNode.position.dx + toNode.width / 2,
               toNode.position.dy + toNode.height / 2),
      );

      if (distance < tapThreshold && distance < minDistance) {
        minDistance = distance;
        tappedConnection = connection;
      }
    }

    // إذا تم العثور على ربط، اختره
    if (tappedConnection != null) {
      widget.onConnectionSelected(tappedConnection);
    } else {
      widget.onConnectionSelected(null);
    }
  }

  // حساب المسافة من نقطة إلى خط
  double _distanceToLine(Offset point, Offset lineStart, Offset lineEnd) {
    final A = point.dx - lineStart.dx;
    final B = point.dy - lineStart.dy;
    final C = lineEnd.dx - lineStart.dx;
    final D = lineEnd.dy - lineStart.dy;

    final dot = A * C + B * D;
    final lenSq = C * C + D * D;

    if (lenSq == 0) {
      // النقطتان متطابقتان
      return sqrt(A * A + B * B);
    }

    var param = dot / lenSq;

    Offset closestPoint;
    if (param < 0) {
      closestPoint = lineStart;
    } else if (param > 1) {
      closestPoint = lineEnd;
    } else {
      closestPoint = Offset(
        lineStart.dx + param * C,
        lineStart.dy + param * D,
      );
    }

    final dx = point.dx - closestPoint.dx;
    final dy = point.dy - closestPoint.dy;
    return sqrt(dx * dx + dy * dy);
  }
}

class MindMapPainter extends CustomPainter {
  final MindMap mindMap;
  final MindMapNode? selectedNode;
  final bool isEditing;

  MindMapPainter({
    required this.mindMap,
    required this.selectedNode,
    required this.isEditing,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.6)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // رسم الخطوط بين العقد
    for (final node in mindMap.nodes.values) {
      for (final childId in node.childrenIds) {
        final child = mindMap.getNode(childId);
        if (child != null) {
          _drawConnection(canvas, node, child, paint);
        }
      }
    }

    // رسم الشبكة في وضع التحرير
    if (isEditing) {
      _drawGrid(canvas, size);
    }
  }

  void _drawConnection(Canvas canvas, MindMapNode parent, MindMapNode child, Paint paint) {
    final startPoint = parent.position;
    final endPoint = child.position;

    // رسم خط منحني بين العقدتين
    final path = Path();
    path.moveTo(startPoint.dx, startPoint.dy);
    
    final controlPoint1 = Offset(
      startPoint.dx + (endPoint.dx - startPoint.dx) * 0.5,
      startPoint.dy,
    );
    final controlPoint2 = Offset(
      startPoint.dx + (endPoint.dx - startPoint.dx) * 0.5,
      endPoint.dy,
    );
    
    path.cubicTo(
      controlPoint1.dx, controlPoint1.dy,
      controlPoint2.dx, controlPoint2.dy,
      endPoint.dx, endPoint.dy,
    );

    canvas.drawPath(path, paint);

    // رسم سهم في نهاية الخط
    _drawArrow(canvas, controlPoint2, endPoint, paint);
  }

  void _drawArrow(Canvas canvas, Offset start, Offset end, Paint paint) {
    const arrowSize = 8.0;
    final direction = atan2(end.dy - start.dy, end.dx - start.dx);

    final arrowPoint1 = Offset(
      end.dx - arrowSize * cos(direction - 0.5),
      end.dy - arrowSize * sin(direction - 0.5),
    );

    final arrowPoint2 = Offset(
      end.dx - arrowSize * cos(direction + 0.5),
      end.dy - arrowSize * sin(direction + 0.5),
    );

    final arrowPath = Path();
    arrowPath.moveTo(end.dx, end.dy);
    arrowPath.lineTo(arrowPoint1.dx, arrowPoint1.dy);
    arrowPath.lineTo(arrowPoint2.dx, arrowPoint2.dy);
    arrowPath.close();

    canvas.drawPath(arrowPath, paint..style = PaintingStyle.fill);
    paint.style = PaintingStyle.stroke;
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    const gridSize = 50.0;

    // رسم الخطوط العمودية
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }

    // رسم الخطوط الأفقية
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}


