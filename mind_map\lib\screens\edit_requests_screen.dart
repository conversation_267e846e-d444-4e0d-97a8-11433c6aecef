import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/edit_requests_provider.dart';
import '../providers/auth_provider.dart';
import '../models/edit_request.dart';
import '../widgets/edit_request_card.dart';

class EditRequestsScreen extends StatefulWidget {
  const EditRequestsScreen({super.key});

  @override
  State<EditRequestsScreen> createState() => _EditRequestsScreenState();
}

class _EditRequestsScreenState extends State<EditRequestsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isSelectionMode = false;
  Set<String> _selectedRequestIds = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadRequests() {
    final authProvider = context.read<AuthProvider>();
    final editRequestsProvider = context.read<EditRequestsProvider>();
    final userId = authProvider.user?.uid;

    if (userId != null) {
      editRequestsProvider.loadReceivedRequests(userId);
      editRequestsProvider.loadSentRequests(userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isSelectionMode
            ? '${_selectedRequestIds.length} محدد'
            : 'طلبات التعديل'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: _isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: _exitSelectionMode,
              )
            : null,
        actions: _isSelectionMode
            ? [
                if (_selectedRequestIds.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: _deleteSelectedRequests,
                  ),
                IconButton(
                  icon: const Icon(Icons.select_all),
                  onPressed: _selectAllRequests,
                ),
              ]
            : [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    if (value == 'select_mode') {
                      _enterSelectionMode();
                    } else if (value == 'clear_all_received') {
                      _showClearAllDialog(true);
                    } else if (value == 'clear_all_sent') {
                      _showClearAllDialog(false);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'select_mode',
                      child: Row(
                        children: [
                          Icon(Icons.checklist, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('وضع التحديد'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'clear_all_received',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.red),
                          SizedBox(width: 8),
                          Text('مسح كل الطلبات الواردة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'clear_all_sent',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('مسح كل الطلبات المرسلة'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              child: Consumer<EditRequestsProvider>(
                builder: (context, provider, child) {
                  final count = provider.unreadReceivedRequestsCount;
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.inbox),
                      const SizedBox(width: 8),
                      const Text('الواردة'),
                      if (count > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            count.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
            Tab(
              child: Consumer<EditRequestsProvider>(
                builder: (context, provider, child) {
                  final count = provider.pendingSentRequestsCount;
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.send),
                      const SizedBox(width: 8),
                      const Text('المرسلة'),
                      if (count > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            count.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildReceivedRequestsTab(),
          _buildSentRequestsTab(),
        ],
      ),
    );
  }

  Widget _buildReceivedRequestsTab() {
    return Consumer<EditRequestsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.receivedRequests.isEmpty) {
          return _buildEmptyState(
            icon: Icons.inbox_outlined,
            title: 'لا توجد طلبات واردة',
            subtitle: 'ستظهر هنا الطلبات التي يرسلها لك المستخدمون الآخرون',
          );
        }

        return RefreshIndicator(
          onRefresh: () async => _loadRequests(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: provider.receivedRequests.length,
            itemBuilder: (context, index) {
              final request = provider.receivedRequests[index];
              final isSelected = _selectedRequestIds.contains(request.id);

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: _isSelectionMode
                    ? CheckboxListTile(
                        value: isSelected,
                        onChanged: (bool? value) {
                          setState(() {
                            if (value == true) {
                              _selectedRequestIds.add(request.id);
                            } else {
                              _selectedRequestIds.remove(request.id);
                            }
                          });
                        },
                        title: Text(request.mindMapTitle),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('من: ${request.fromUserName}'),
                            Text('التاريخ: ${_formatDate(request.createdAt)}'),
                            Text('الحالة: ${_getStatusText(request.status)}'),
                          ],
                        ),
                        secondary: CircleAvatar(
                          backgroundColor: _getStatusColor(request.status),
                          child: Icon(
                            _getStatusIcon(request.status),
                            color: Colors.white,
                          ),
                        ),
                      )
                    : EditRequestCard(
                        request: request,
                        isReceived: true,
                        onAccept: () => _acceptRequest(request),
                        onReject: () => _rejectRequest(request),
                      ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildSentRequestsTab() {
    return Consumer<EditRequestsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.sentRequests.isEmpty) {
          return _buildEmptyState(
            icon: Icons.send_outlined,
            title: 'لا توجد طلبات مرسلة',
            subtitle: 'يمكنك إرسال طلبات تعديل من بطاقات المنشورات',
          );
        }

        return RefreshIndicator(
          onRefresh: () async => _loadRequests(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: provider.sentRequests.length,
            itemBuilder: (context, index) {
              final request = provider.sentRequests[index];
              final isSelected = _selectedRequestIds.contains(request.id);

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: _isSelectionMode
                    ? CheckboxListTile(
                        value: isSelected,
                        onChanged: (bool? value) {
                          setState(() {
                            if (value == true) {
                              _selectedRequestIds.add(request.id);
                            } else {
                              _selectedRequestIds.remove(request.id);
                            }
                          });
                        },
                        title: Text(request.mindMapTitle),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('إلى: ${request.toUserName}'),
                            Text('التاريخ: ${_formatDate(request.createdAt)}'),
                            Text('الحالة: ${_getStatusText(request.status)}'),
                          ],
                        ),
                        secondary: CircleAvatar(
                          backgroundColor: _getStatusColor(request.status),
                          child: Icon(
                            _getStatusIcon(request.status),
                            color: Colors.white,
                          ),
                        ),
                      )
                    : EditRequestCard(
                        request: request,
                        isReceived: false,
                      ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _acceptRequest(EditRequest request) {
    showDialog(
      context: context,
      builder: (context) => _buildResponseDialog(
        request: request,
        isAccepting: true,
      ),
    );
  }

  void _rejectRequest(EditRequest request) {
    showDialog(
      context: context,
      builder: (context) => _buildResponseDialog(
        request: request,
        isAccepting: false,
      ),
    );
  }

  Widget _buildResponseDialog({
    required EditRequest request,
    required bool isAccepting,
  }) {
    final controller = TextEditingController();
    
    return AlertDialog(
      title: Text(isAccepting ? 'قبول الطلب' : 'رفض الطلب'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلب من: ${request.fromUserName}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text('المخطط: ${request.mindMapTitle}'),
          const SizedBox(height: 16),
          TextField(
            controller: controller,
            decoration: InputDecoration(
              labelText: isAccepting ? 'رسالة القبول (اختيارية)' : 'سبب الرفض (اختياري)',
              border: const OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.pop(context);
            
            try {
              final provider = context.read<EditRequestsProvider>();
              if (isAccepting) {
                await provider.acceptRequest(
                  request.id,
                  responseMessage: controller.text.trim().isEmpty ? null : controller.text.trim(),
                );
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم قبول الطلب')),
                  );
                }
              } else {
                await provider.rejectRequest(
                  request.id,
                  responseMessage: controller.text.trim().isEmpty ? null : controller.text.trim(),
                );
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم رفض الطلب')),
                  );
                }
              }
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('خطأ: $e')),
                );
              }
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: isAccepting ? Colors.green : Colors.red,
            foregroundColor: Colors.white,
          ),
          child: Text(isAccepting ? 'قبول' : 'رفض'),
        ),
      ],
    );
  }

  // دخول وضع التحديد
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedRequestIds.clear();
    });
  }

  // الخروج من وضع التحديد
  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedRequestIds.clear();
    });
  }

  // تحديد جميع الطلبات في التبويب الحالي
  void _selectAllRequests() {
    final provider = context.read<EditRequestsProvider>();
    setState(() {
      if (_tabController.index == 0) {
        // تبويب الطلبات الواردة
        for (var request in provider.receivedRequests) {
          _selectedRequestIds.add(request.id);
        }
      } else {
        // تبويب الطلبات المرسلة
        for (var request in provider.sentRequests) {
          _selectedRequestIds.add(request.id);
        }
      }
    });
  }

  // حذف الطلبات المحددة
  void _deleteSelectedRequests() {
    if (_selectedRequestIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الطلبات المحددة'),
        content: Text('هل أنت متأكد من حذف ${_selectedRequestIds.length} طلب؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDeleteSelected();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // تنفيذ حذف الطلبات المحددة
  Future<void> _performDeleteSelected() async {
    try {
      final provider = context.read<EditRequestsProvider>();

      for (String requestId in _selectedRequestIds) {
        await provider.deleteRequest(requestId);
      }

      _exitSelectionMode();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف ${_selectedRequestIds.length} طلب'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الحذف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // الحصول على نص الحالة
  String _getStatusText(EditRequestStatus status) {
    switch (status) {
      case EditRequestStatus.pending:
        return 'في الانتظار';
      case EditRequestStatus.accepted:
        return 'مقبول';
      case EditRequestStatus.rejected:
        return 'مرفوض';
      case EditRequestStatus.inProgress:
        return 'قيد التعديل';
      case EditRequestStatus.awaitingApproval:
        return 'في انتظار الموافقة';
      case EditRequestStatus.completed:
        return 'مكتمل';
    }
  }

  // الحصول على لون الحالة
  Color _getStatusColor(EditRequestStatus status) {
    switch (status) {
      case EditRequestStatus.pending:
        return Colors.orange;
      case EditRequestStatus.accepted:
        return Colors.green;
      case EditRequestStatus.rejected:
        return Colors.red;
      case EditRequestStatus.inProgress:
        return Colors.blue;
      case EditRequestStatus.awaitingApproval:
        return Colors.purple;
      case EditRequestStatus.completed:
        return Colors.teal;
    }
  }

  // الحصول على أيقونة الحالة
  IconData _getStatusIcon(EditRequestStatus status) {
    switch (status) {
      case EditRequestStatus.pending:
        return Icons.hourglass_empty;
      case EditRequestStatus.accepted:
        return Icons.check;
      case EditRequestStatus.rejected:
        return Icons.close;
      case EditRequestStatus.inProgress:
        return Icons.edit;
      case EditRequestStatus.awaitingApproval:
        return Icons.pending;
      case EditRequestStatus.completed:
        return Icons.done_all;
    }
  }

  // عرض حوار تأكيد المسح الكامل
  void _showClearAllDialog(bool isReceived) {
    final String title = isReceived ? 'مسح الطلبات الواردة' : 'مسح الطلبات المرسلة';
    final String content = isReceived
        ? 'هل أنت متأكد من مسح جميع الطلبات الواردة؟ لا يمكن التراجع عن هذا الإجراء.'
        : 'هل أنت متأكد من مسح جميع الطلبات المرسلة؟ لا يمكن التراجع عن هذا الإجراء.';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _clearRequests(isReceived);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  // مسح الطلبات
  Future<void> _clearRequests(bool isReceived) async {
    try {
      final authProvider = context.read<AuthProvider>();
      final editRequestsProvider = context.read<EditRequestsProvider>();
      final userId = authProvider.user?.uid;

      if (userId == null) return;

      if (isReceived) {
        await editRequestsProvider.clearReceivedRequests(userId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم مسح جميع الطلبات الواردة'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        await editRequestsProvider.clearSentRequests(userId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم مسح جميع الطلبات المرسلة'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المسح: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
