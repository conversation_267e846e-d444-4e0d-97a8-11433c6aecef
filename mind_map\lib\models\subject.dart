import 'dart:ui';
import 'package:uuid/uuid.dart';

class Subject {
  final String id;
  String name;
  String description;
  Color color;
  String? iconName;
  List<String> mindMapIds;
  DateTime createdAt;
  DateTime updatedAt;
  int semester; // الفصل الدراسي
  String? professor; // اسم الأستاذ
  double creditHours; // عدد الساعات المعتمدة

  Subject({
    String? id,
    required this.name,
    this.description = '',
    this.color = const Color(0xFF2196F3),
    this.iconName,
    List<String>? mindMapIds,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.semester = 1,
    this.professor,
    this.creditHours = 3.0,
  }) : id = id ?? const Uuid().v4(),
       mindMapIds = mindMapIds ?? [],
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // إنشاء نسخة من المادة مع تعديل بعض الخصائص
  Subject copyWith({
    String? name,
    String? description,
    Color? color,
    String? iconName,
    List<String>? mindMapIds,
    int? semester,
    String? professor,
    double? creditHours,
  }) {
    return Subject(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      iconName: iconName ?? this.iconName,
      mindMapIds: mindMapIds ?? List.from(this.mindMapIds),
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      semester: semester ?? this.semester,
      professor: professor ?? this.professor,
      creditHours: creditHours ?? this.creditHours,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.value.toRadixString(16),
      'iconName': iconName,
      'mindMapIds': mindMapIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'semester': semester,
      'professor': professor,
      'creditHours': creditHours,
    };
  }

  // إنشاء من JSON
  factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      color: Color(int.parse(json['color'] as String, radix: 16)),
      iconName: json['iconName'],
      mindMapIds: List<String>.from(json['mindMapIds'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      semester: json['semester'] ?? 1,
      professor: json['professor'],
      creditHours: (json['creditHours'] ?? 3).toDouble(),
    );
  }

  // إضافة مخطط ذهني
  void addMindMap(String mindMapId) {
    if (!mindMapIds.contains(mindMapId)) {
      mindMapIds.add(mindMapId);
      updatedAt = DateTime.now();
    }
  }

  // إزالة مخطط ذهني
  void removeMindMap(String mindMapId) {
    mindMapIds.remove(mindMapId);
    updatedAt = DateTime.now();
  }

  // تحديث المعلومات
  void updateInfo({
    String? newName,
    String? newDescription,
    String? newProfessor,
    int? newSemester,
    double? newCreditHours,
  }) {
    if (newName != null) name = newName;
    if (newDescription != null) description = newDescription;
    if (newProfessor != null) professor = newProfessor;
    if (newSemester != null) semester = newSemester;
    if (newCreditHours != null) creditHours = newCreditHours;
    updatedAt = DateTime.now();
  }

  // تحديث اللون
  void updateColor(Color newColor) {
    color = newColor;
    updatedAt = DateTime.now();
  }

  // تحديث الأيقونة
  void updateIcon(String? newIconName) {
    iconName = newIconName;
    updatedAt = DateTime.now();
  }

  // الحصول على عدد المخططات الذهنية
  int get mindMapCount => mindMapIds.length;

  // التحقق من وجود مخططات ذهنية
  bool get hasMindMaps => mindMapIds.isNotEmpty;

  @override
  String toString() {
    return 'Subject(id: $id, name: $name, mindMapCount: $mindMapCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subject && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// ألوان افتراضية للمواد
class SubjectColors {
  static const List<Color> defaultColors = [
    Color(0xFF2196F3), // أزرق
    Color(0xFF4CAF50), // أخضر
    Color(0xFFFF9800), // برتقالي
    Color(0xFF9C27B0), // بنفسجي
    Color(0xFFF44336), // أحمر
    Color(0xFF00BCD4), // سماوي
    Color(0xFF795548), // بني
    Color(0xFF607D8B), // رمادي مزرق
    Color(0xFFE91E63), // وردي
    Color(0xFF8BC34A), // أخضر فاتح
    Color(0xFFFF5722), // برتقالي محمر
    Color(0xFF3F51B5), // أزرق داكن
  ];

  static Color getColorForIndex(int index) {
    return defaultColors[index % defaultColors.length];
  }
}

// أيقونات افتراضية للمواد
class SubjectIcons {
  static const Map<String, String> subjectIcons = {
    'رياضيات': 'calculate',
    'فيزياء': 'science',
    'كيمياء': 'biotech',
    'أحياء': 'eco',
    'تاريخ': 'history_edu',
    'جغرافيا': 'public',
    'لغة عربية': 'translate',
    'لغة إنجليزية': 'language',
    'حاسوب': 'computer',
    'هندسة': 'engineering',
    'طب': 'medical_services',
    'قانون': 'gavel',
    'اقتصاد': 'trending_up',
    'فلسفة': 'psychology',
    'فن': 'palette',
    'موسيقى': 'music_note',
    'رياضة': 'sports',
  };

  static String? getIconForSubject(String subjectName) {
    return subjectIcons[subjectName.toLowerCase()];
  }
}
