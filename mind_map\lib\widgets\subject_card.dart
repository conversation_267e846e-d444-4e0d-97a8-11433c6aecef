import 'package:flutter/material.dart';
import '../models/subject.dart';

class SubjectCard extends StatelessWidget {
  final Subject subject;
  final int mindMapCount;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const SubjectCard({
    super.key,
    required this.subject,
    required this.mindMapCount,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Subject icon/color indicator
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: subject.color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getSubjectIcon(),
                  color: Colors.white,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Subject info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subject.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    if (subject.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        subject.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    
                    const SizedBox(height: 8),
                    
                    // Subject details
                    Row(
                      children: [
                        if (subject.professor != null) ...[
                          Icon(
                            Icons.person,
                            size: 14,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            subject.professor!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                          const SizedBox(width: 12),
                        ],
                        
                        Icon(
                          Icons.schedule,
                          size: 14,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          subject.creditHours == subject.creditHours.toInt()
                              ? '${subject.creditHours.toInt()} ساعة'
                              : '${subject.creditHours} ساعة',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                        
                        const SizedBox(width: 12),
                        
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'الفصل ${subject.semester}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Mind map count and menu
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.account_tree,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '$mindMapCount',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('تحرير'),
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('حذف'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getSubjectIcon() {
    // استخدام الأيقونة المخصصة أو أيقونة افتراضية حسب اسم المادة
    if (subject.iconName != null) {
      return _getIconFromName(subject.iconName!);
    }
    
    final subjectName = subject.name.toLowerCase();
    
    if (subjectName.contains('رياضيات') || subjectName.contains('حساب')) {
      return Icons.calculate;
    } else if (subjectName.contains('فيزياء')) {
      return Icons.science;
    } else if (subjectName.contains('كيمياء')) {
      return Icons.biotech;
    } else if (subjectName.contains('أحياء') || subjectName.contains('بيولوجيا')) {
      return Icons.eco;
    } else if (subjectName.contains('تاريخ')) {
      return Icons.history_edu;
    } else if (subjectName.contains('جغرافيا')) {
      return Icons.public;
    } else if (subjectName.contains('عربية') || subjectName.contains('عربي')) {
      return Icons.translate;
    } else if (subjectName.contains('إنجليزي') || subjectName.contains('انجليزي')) {
      return Icons.language;
    } else if (subjectName.contains('حاسوب') || subjectName.contains('برمجة')) {
      return Icons.computer;
    } else if (subjectName.contains('هندسة')) {
      return Icons.engineering;
    } else if (subjectName.contains('طب')) {
      return Icons.medical_services;
    } else if (subjectName.contains('قانون')) {
      return Icons.gavel;
    } else if (subjectName.contains('اقتصاد')) {
      return Icons.trending_up;
    } else if (subjectName.contains('فلسفة')) {
      return Icons.psychology;
    } else if (subjectName.contains('فن')) {
      return Icons.palette;
    } else if (subjectName.contains('موسيقى')) {
      return Icons.music_note;
    } else if (subjectName.contains('رياضة')) {
      return Icons.sports;
    } else {
      return Icons.school;
    }
  }

  IconData _getIconFromName(String iconName) {
    switch (iconName) {
      case 'calculate':
        return Icons.calculate;
      case 'science':
        return Icons.science;
      case 'biotech':
        return Icons.biotech;
      case 'eco':
        return Icons.eco;
      case 'history_edu':
        return Icons.history_edu;
      case 'public':
        return Icons.public;
      case 'translate':
        return Icons.translate;
      case 'language':
        return Icons.language;
      case 'computer':
        return Icons.computer;
      case 'engineering':
        return Icons.engineering;
      case 'medical_services':
        return Icons.medical_services;
      case 'gavel':
        return Icons.gavel;
      case 'trending_up':
        return Icons.trending_up;
      case 'psychology':
        return Icons.psychology;
      case 'palette':
        return Icons.palette;
      case 'music_note':
        return Icons.music_note;
      case 'sports':
        return Icons.sports;
      default:
        return Icons.school;
    }
  }
}
