import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/connectivity_provider.dart';
import '../screens/no_internet_screen.dart';

/// Widget مشترك لفحص الاتصال بالإنترنت
/// يعرض شاشة عدم الاتصال إذا لم يكن هناك إنترنت
/// ويعرض المحتوى العادي إذا كان هناك اتصال
class ConnectivityWrapper extends StatelessWidget {
  final Widget child;
  final bool showLoadingOnCheck;

  const ConnectivityWrapper({
    super.key,
    required this.child,
    this.showLoadingOnCheck = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityProvider>(
      builder: (context, connectivityProvider, _) {
        // إذا لم نتحقق من الاتصال الأولي بعد
        if (!connectivityProvider.hasCheckedInitialConnection && showLoadingOnCheck) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Colors.blue.shade700,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'جاري التحقق من الاتصال...',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // إذا لم يكن هناك اتصال بالإنترنت
        if (!connectivityProvider.isConnected) {
          return const NoInternetScreen();
        }

        // إذا كان هناك اتصال، عرض المحتوى العادي
        return child;
      },
    );
  }
}

/// Extension لتسهيل استخدام ConnectivityWrapper
extension ConnectivityWrapperExtension on Widget {
  /// يلف الـ Widget بـ ConnectivityWrapper
  Widget withConnectivityCheck({bool showLoadingOnCheck = false}) {
    return ConnectivityWrapper(
      showLoadingOnCheck: showLoadingOnCheck,
      child: this,
    );
  }
}
