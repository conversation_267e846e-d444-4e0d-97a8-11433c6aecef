import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/admin_user_actions_provider.dart';
import '../models/user_model.dart';
import '../widgets/connectivity_wrapper.dart';
import 'user_management_detail_screen.dart';

class AdminUserActionsScreen extends StatefulWidget {
  const AdminUserActionsScreen({super.key});

  @override
  State<AdminUserActionsScreen> createState() => _AdminUserActionsScreenState();
}

class _AdminUserActionsScreenState extends State<AdminUserActionsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<UserModel> _filteredUsers = [];
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
  }

  void _filterUsers(String query, List<UserModel> allUsers) {
    setState(() {
      if (query.isEmpty) {
        _filteredUsers = List.from(allUsers);
      } else {
        _filteredUsers = allUsers.where((user) {
          return user.fullName.toLowerCase().contains(query.toLowerCase()) ||
                 user.email.toLowerCase().contains(query.toLowerCase()) ||
                 user.university.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AdminUserActionsProvider(),
      child: ConnectivityWrapper(
        child: Consumer<AdminUserActionsProvider>(
          builder: (context, provider, child) {
            // تحميل المستخدمين عند أول مرة
            if (!_hasInitialized) {
              _hasInitialized = true;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                provider.loadUsers().then((_) {
                  if (mounted) {
                    setState(() {
                      _filteredUsers = provider.users;
                    });
                  }
                });
              });
            }

            return Scaffold(
        appBar: AppBar(
          title: const Text(
            'إجراءات المستخدمين',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.red.shade700,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: Column(
          children: [
            // شريط البحث
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.red.shade700,
              child: TextField(
                controller: _searchController,
                onChanged: (query) {
                  _filterUsers(query, provider.users);
                },
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'البحث عن مستخدم...',
                  hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
                  prefixIcon: Icon(Icons.search, color: Colors.white.withValues(alpha: 0.7)),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
            
            // قائمة المستخدمين
            Expanded(
              child: _filteredUsers.isEmpty && !_hasInitialized
                  ? const Center(
                      child: CircularProgressIndicator(color: Colors.red),
                    )
                  : _filteredUsers.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.people_outline,
                                size: 64,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'لا توجد مستخدمين',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: () async {
                            await provider.loadUsers();
                            if (mounted) {
                              setState(() {
                                _filteredUsers = provider.users;
                              });
                            }
                          },
                          child: ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _filteredUsers.length,
                            itemBuilder: (context, index) {
                              final user = _filteredUsers[index];
                              return _buildUserCard(user);
                            },
                          ),
                        ),
            ),
          ],
        ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => UserManagementDetailScreen(user: user),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 25,
                      backgroundColor: Colors.red.shade100,
                      child: Text(
                        user.firstName.isNotEmpty ? user.firstName[0].toUpperCase() : 'U',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                    // مؤشر حالة التقييد
                    Consumer<AdminUserActionsProvider>(
                      builder: (context, provider, child) {
                        if (provider.isUserRestricted(user)) {
                          return Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.amber.shade400,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white, width: 2),
                              ),
                              child: const Icon(
                                Icons.warning,
                                color: Colors.white,
                                size: 10,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              user.fullName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          // مؤشر نصي للتقييد
                          Consumer<AdminUserActionsProvider>(
                            builder: (context, provider, child) {
                              if (provider.isUserRestricted(user)) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                                  ),
                                  child: Text(
                                    'مقيد',
                                    style: TextStyle(
                                      color: Colors.orange.shade700,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.email,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      if (user.university.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          user.university,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 16,
                ),
              ],
            ),
            
            // معلومات إضافية
            const SizedBox(height: 12),
            Consumer<AdminUserActionsProvider>(
              builder: (context, provider, child) {
                return FutureBuilder<Map<String, int>>(
                  future: provider.getUserStats(user.uid),
                  builder: (context, snapshot) {
                    final stats = snapshot.data ?? {'warnings': 0};
                    return Row(
                      children: [
                        _buildInfoChip('التحذيرات', '${stats['warnings']}', Colors.red),
                      ],
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
        ),
    );
  }

  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }



  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
