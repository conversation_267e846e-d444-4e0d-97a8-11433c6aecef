import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/follow_suggestion_model.dart';
import '../providers/auth_provider.dart';

import '../screens/user_profile_screen.dart';

class FollowSuggestionCard extends StatefulWidget {
  final List<FollowSuggestionModel> suggestions;
  final VoidCallback? onDismiss;

  const FollowSuggestionCard({
    super.key,
    required this.suggestions,
    this.onDismiss,
  });

  @override
  State<FollowSuggestionCard> createState() => _FollowSuggestionCardState();
}

class _FollowSuggestionCardState extends State<FollowSuggestionCard> {
  late PageController _pageController;
  int _currentIndex = 0;
  final Map<String, bool> _followingStates = {};

  late List<FollowSuggestionModel> _currentSuggestions;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _currentSuggestions = List.from(widget.suggestions);

    // تهيئة حالات المتابعة
    for (final suggestion in _currentSuggestions) {
      _followingStates[suggestion.userId] = suggestion.isFollowing;
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_currentSuggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width <= 375 ? 12 : 16,
        vertical: MediaQuery.of(context).size.width <= 375 ? 4 : 6,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.purple.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان مع زر الإغلاق
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.people_alt,
                    color: Colors.blue.shade700,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'اقتراحات المتابعة',
                        style: TextStyle(
                          fontSize: MediaQuery.of(context).size.width <= 375 ? 16 : 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      Text(
                        'أشخاص قد تعرفهم',
                        style: TextStyle(
                          fontSize: MediaQuery.of(context).size.width <= 375 ? 12 : 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: widget.onDismiss,
                  icon: Icon(
                    Icons.close,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  tooltip: 'إخفاء الاقتراحات',
                ),
              ],
            ),
          ),
          
          // محتوى الاقتراحات
          SizedBox(
            height: MediaQuery.of(context).size.width <= 375 ? 180 : 200,
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: _currentSuggestions.length,
              itemBuilder: (context, index) {
                return _buildSuggestionItem(_currentSuggestions[index]);
              },
            ),
          ),
          
          // مؤشر الصفحات
          if (_currentSuggestions.length > 1)
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  _currentSuggestions.length,
                  (index) => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: _currentIndex == index ? 20 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _currentIndex == index 
                          ? Colors.blue.shade600 
                          : Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(FollowSuggestionModel suggestion) {
    final isFollowing = _followingStates[suggestion.userId] ?? false;

    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final isLoading = authProvider.isUserLoading(suggestion.userId);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          // الصورة الشخصية
          GestureDetector(
            onTap: () => _navigateToProfile(suggestion),
            child: CircleAvatar(
              radius: MediaQuery.of(context).size.width <= 375 ? 24 : 28,
              backgroundColor: Colors.blue.shade100,
              child: Text(
                _getInitials(suggestion.userName),
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.width <= 375 ? 14 : 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // معلومات المستخدم
          Expanded(
            child: GestureDetector(
              onTap: () => _navigateToProfile(suggestion),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    suggestion.userName,
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width <= 375 ? 16 : 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 2),

                  Text(
                    suggestion.university,
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width <= 375 ? 13 : 14,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  Text(
                    suggestion.major,
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width <= 375 ? 12 : 13,
                      color: Colors.grey.shade500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 3),

                  // سبب الاقتراح
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      suggestion.suggestionReason,
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width <= 375 ? 10 : 11,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 2),

                  // إحصائيات
                  Row(
                    children: [
                      Icon(
                        Icons.people,
                        size: MediaQuery.of(context).size.width <= 375 ? 12 : 14,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${suggestion.followersCount} متابع',
                        style: TextStyle(
                          fontSize: MediaQuery.of(context).size.width <= 375 ? 11 : 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Icon(
                        Icons.article,
                        size: MediaQuery.of(context).size.width <= 375 ? 12 : 14,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${suggestion.postsCount} منشور',
                        style: TextStyle(
                          fontSize: MediaQuery.of(context).size.width <= 375 ? 11 : 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // زر المتابعة
          SizedBox(
            width: MediaQuery.of(context).size.width <= 375 ? 75 : 85,
            height: MediaQuery.of(context).size.width <= 375 ? 28 : 32,
            child: ElevatedButton(
              onPressed: isLoading ? null : () => _toggleFollow(suggestion),
              style: ElevatedButton.styleFrom(
                backgroundColor: isFollowing ? Colors.grey.shade300 : Colors.blue.shade600,
                foregroundColor: isFollowing ? Colors.black87 : Colors.white,
                elevation: isFollowing ? 1 : 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(18),
                ),
                padding: EdgeInsets.zero,
              ),
              child: isLoading
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isFollowing ? Colors.black54 : Colors.white,
                        ),
                      ),
                    )
                  : Text(
                      isFollowing ? 'إلغاء' : 'متابعة',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width <= 375 ? 11 : 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
      },
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return 'م';
    
    final words = name.trim().split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}';
    }
    return name[0].toUpperCase();
  }

  Future<void> _toggleFollow(FollowSuggestionModel suggestion) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.userModel;
    
    if (currentUser == null) return;

    try {
      final newFollowingState = !(_followingStates[suggestion.userId] ?? false);

      // استخدام AuthProvider مع حالة التحميل المنفصلة
      bool success;
      if (newFollowingState) {
        success = await authProvider.followUser(suggestion.userId);
      } else {
        success = await authProvider.unfollowUser(suggestion.userId);
      }

      if (success) {
        setState(() {
          _followingStates[suggestion.userId] = newFollowingState;
        });

        // إزالة المستخدم من الاقتراحات عند متابعته فقط
        if (newFollowingState) {
          _removeSuggestion(suggestion.userId);
        }

        // عرض مربع حوار النجاح
        if (mounted) {
          final navigator = Navigator.of(context);
          if (newFollowingState) {
            _showSuccessDialog(
              navigator,
              title: 'تم بنجاح!',
              message: 'تم متابعة ${suggestion.userName} بنجاح',
              icon: Icons.check_circle,
              color: Colors.green,
            );
          } else {
            _showSuccessDialog(
              navigator,
              title: 'تم بنجاح!',
              message: 'تم إلغاء متابعة ${suggestion.userName}',
              icon: Icons.check_circle,
              color: Colors.blue,
            );
          }
        }
      } else {
        // عرض مربع حوار الخطأ
        if (mounted) {
          final navigator = Navigator.of(context);
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          final errorMessage = authProvider.errorMessage ?? 'فشل في العملية';
          _showErrorDialog(
            navigator,
            title: newFollowingState ? 'خطأ في المتابعة' : 'خطأ في إلغاء المتابعة',
            message: errorMessage,
          );
        }
      }
    } catch (e) {
      // في حالة الخطأ، عرض مربع حوار الخطأ
      if (mounted) {
        final navigator = Navigator.of(context);
        _showErrorDialog(
          navigator,
          title: 'خطأ',
          message: 'حدث خطأ: ${e.toString()}',
        );
      }
    }
  }

  // إزالة اقتراح من القائمة
  void _removeSuggestion(String userId) {
    setState(() {
      _currentSuggestions.removeWhere((suggestion) => suggestion.userId == userId);

      // تنظيف الحالات المحفوظة
      _followingStates.remove(userId);

      // تعديل الفهرس الحالي إذا لزم الأمر
      if (_currentIndex >= _currentSuggestions.length && _currentSuggestions.isNotEmpty) {
        _currentIndex = _currentSuggestions.length - 1;
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }

      // إذا لم تعد هناك اقتراحات، استدعاء onDismiss
      if (_currentSuggestions.isEmpty && widget.onDismiss != null) {
        widget.onDismiss!();
      }
    });
  }



  void _navigateToProfile(FollowSuggestionModel suggestion) async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // استخدام AuthProvider لجلب البيانات (أكثر أماناً ومعالجة أفضل للأخطاء)
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final fullUserModel = await authProvider.getUserById(suggestion.userId);

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (fullUserModel != null) {

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => UserProfileScreen(user: fullUserModel),
            ),
          );
        }
      } else {
        // عرض رسالة خطأ إذا لم توجد البيانات
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('عذراً، لا يمكن الوصول إلى ملف المستخدم الشخصي'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (mounted) Navigator.pop(context);

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الملف الشخصي: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _navigateToProfile(suggestion),
            ),
          ),
        );
      }
    }
  }



  // عرض حوار النجاح
  void _showSuccessDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
    required IconData icon,
    required Color color,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض حوار الخطأ
  void _showErrorDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 36,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
