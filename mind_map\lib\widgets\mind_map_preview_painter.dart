import 'package:flutter/material.dart';
import '../models/mind_map.dart';

class MindMapPreviewPainter extends CustomPainter {
  final MindMap mindMap;

  MindMapPreviewPainter({required this.mindMap});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.6)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    final linePaint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.4)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // رسم عقد مبسطة
    final nodes = mindMap.nodes.values.toList();
    if (nodes.isEmpty) return;

    // حساب نطاق المواقع
    double minX = nodes.first.position.dx;
    double maxX = nodes.first.position.dx;
    double minY = nodes.first.position.dy;
    double maxY = nodes.first.position.dy;

    for (final node in nodes) {
      minX = minX < node.position.dx ? minX : node.position.dx;
      maxX = maxX > node.position.dx ? maxX : node.position.dx;
      minY = minY < node.position.dy ? minY : node.position.dy;
      maxY = maxY > node.position.dy ? maxY : node.position.dy;
    }

    final rangeX = maxX - minX;
    final rangeY = maxY - minY;

    // تجنب القسمة على صفر
    if (rangeX == 0 || rangeY == 0) {
      // رسم عقدة واحدة في المنتصف
      canvas.drawCircle(
        Offset(size.width / 2, size.height / 2),
        8,
        paint,
      );
      return;
    }

    // رسم الروابط أولاً
    for (final connection in mindMap.connections.values) {
      final fromNode = mindMap.nodes[connection.fromNodeId];
      final toNode = mindMap.nodes[connection.toNodeId];
      
      if (fromNode != null && toNode != null) {
        final fromPos = _scalePosition(fromNode.position, minX, minY, rangeX, rangeY, size);
        final toPos = _scalePosition(toNode.position, minX, minY, rangeX, rangeY, size);
        
        canvas.drawLine(fromPos, toPos, linePaint);
      }
    }

    // رسم العقد
    for (final node in nodes) {
      final scaledPos = _scalePosition(node.position, minX, minY, rangeX, rangeY, size);
      
      // رسم العقدة كدائرة صغيرة
      canvas.drawCircle(
        scaledPos,
        6,
        paint,
      );
    }
  }

  Offset _scalePosition(Offset position, double minX, double minY, double rangeX, double rangeY, Size size) {
    final padding = 20.0;
    final availableWidth = size.width - (padding * 2);
    final availableHeight = size.height - (padding * 2);
    
    final x = padding + ((position.dx - minX) / rangeX) * availableWidth;
    final y = padding + ((position.dy - minY) / rangeY) * availableHeight;
    
    return Offset(x, y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! MindMapPreviewPainter || oldDelegate.mindMap != mindMap;
  }
}
