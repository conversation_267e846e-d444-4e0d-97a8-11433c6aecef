import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/posts_provider.dart';
import '../models/user_model.dart';
import '../models/post.dart';
import '../services/restriction_service.dart';
import '../services/profile_visit_service.dart';
import '../widgets/restriction_dialog.dart';
import '../widgets/post_card.dart';
import '../widgets/connectivity_wrapper.dart';

class UserProfileScreen extends StatefulWidget {
  final UserModel user;

  const UserProfileScreen({
    super.key,
    required this.user,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث آخر نشاط عند فتح الشاشة وتسجيل زيارة الملف الشخصي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.updateLastActive();

      // تسجيل زيارة الملف الشخصي
      final currentUser = authProvider.userModel;
      if (currentUser != null && currentUser.uid != widget.user.uid) {
        print('🔍 تسجيل زيارة: ${currentUser.uid} يزور ${widget.user.uid}');
        ProfileVisitService.recordProfileVisit(
          profileOwnerId: widget.user.uid,
          visitorId: currentUser.uid,
        );
      } else {
        print('🔍 لا يتم تسجيل الزيارة - المستخدم يزور ملفه الشخصي');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
        final currentUser = authProvider.userModel;
        final isFollowing = currentUser?.isFollowing(widget.user.uid) ?? false;

        // التحقق من إعدادات الخصوصية
        final isProfilePublic = widget.user.isProfilePublic ?? true;

        // إذا كان الملف الشخصي مخفي وليس المستخدم الحالي
        if (!isProfilePublic && currentUser?.uid != widget.user.uid) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('ملف شخصي'),
              backgroundColor: Colors.blue.shade700,
              foregroundColor: Colors.white,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lock,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'الملف الشخصي مخفي',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'هذا المستخدم قام بإخفاء ملفه الشخصي',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          body: CustomScrollView(
            slivers: [
              // AppBar مع تأثير التمرير
              SliverAppBar(
                expandedHeight: 180,
                floating: false,
                pinned: true,
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
                title: Text(widget.user.fullName),
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.blue.shade700,
                          Colors.blue.shade500,
                          Colors.blue.shade300,
                        ],
                        stops: const [0.0, 0.7, 1.0],
                      ),
                    ),
                  ),
                ),
                actions: [
                  // زر المتابعة في الـ AppBar (إذا كان يسمح بالمتابعة أو متابع بالفعل)
                  if ((widget.user.allowFollowing ?? true) || isFollowing)
                    Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: ElevatedButton(
                        onPressed: authProvider.isLoading
                            ? null
                            : (isFollowing || (widget.user.allowFollowing ?? true))
                                ? () async {
                                    // حفظ المعلومات قبل العملية غير المتزامنة
                                    final userName = widget.user.fullName;
                                    final navigator = Navigator.of(context);

                                    bool success;
                                    if (isFollowing) {
                                      // إلغاء المتابعة
                                      success = await authProvider.unfollowUser(widget.user.uid);
                                    } else {
                                      // التحقق من تقييد المتابعة
                                      final currentUser = authProvider.userModel;
                                      if (currentUser != null) {
                                        final restriction = await RestrictionService.checkFollowingRestriction(currentUser.uid);
                                        if (restriction != null && mounted) {
                                          await RestrictionDialog.show(navigator.context, restriction, 'متابعة المستخدمين');
                                          return;
                                        }
                                      }
                                      success = await authProvider.followUser(widget.user.uid);
                                    }

                                    if (!mounted) return;

                                    if (success) {
                                      if (isFollowing) {
                                        _showSuccessDialog(
                                          navigator,
                                          title: 'تم بنجاح!',
                                          message: 'تم إلغاء متابعة $userName',
                                          icon: Icons.check_circle,
                                          color: Colors.blue,
                                        );
                                      } else {
                                        _showSuccessDialog(
                                          navigator,
                                          title: 'تم بنجاح!',
                                          message: 'تم متابعة $userName بنجاح',
                                          icon: Icons.check_circle,
                                          color: Colors.green,
                                        );
                                      }
                                    } else {
                                      final errorMessage = authProvider.errorMessage ?? 'فشل في العملية';
                                      _showErrorDialog(
                                        navigator,
                                        title: isFollowing ? 'خطأ في إلغاء المتابعة' : 'خطأ في المتابعة',
                                        message: errorMessage,
                                      );
                                    }
                                  }
                                : null, // تعطيل الزر إذا كان لا يسمح بالمتابعة وليس متابعاً
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isFollowing ? Colors.white : Colors.blue.shade900,
                          foregroundColor: isFollowing ? Colors.blue.shade700 : Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isFollowing ? Icons.person_remove : Icons.person_add,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            isFollowing
                                ? 'إلغاء المتابعة'
                                : (widget.user.allowFollowing ?? true)
                                    ? 'متابعة'
                                    : 'لا يسمح بالمتابعة',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  )
                  else
                    // رسالة عدم السماح بالمتابعة
                    Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.lock, size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Text(
                              'لا يسمح بالمتابعة',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
              
              // محتوى الشاشة
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // بطاقة المعلومات الأساسية
                      _buildMainInfoCard(),
                      const SizedBox(height: 16),
                      
                      // إحصائيات المتابعة
                      _buildFollowStatsCard(),
                      const SizedBox(height: 16),

                      // إحصائيات زوار الملف الشخصي
                      _buildProfileVisitorsCard(),
                      const SizedBox(height: 16),

                      // المعلومات الأكاديمية
                      _buildAcademicInfoCard(),
                      
                      // النبذة الشخصية
                      if (widget.user.bio.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildBioCard(),
                      ],

                      const SizedBox(height: 16),

                      // معلومات إضافية
                      _buildAdditionalInfoCard(),

                      const SizedBox(height: 16),

                      // منشورات المستخدم
                      _buildUserPostsCard(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
      ),
    );
  }

  Widget _buildMainInfoCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.blue.shade50,
            ],
          ),
        ),
        child: Column(
          children: [
            // صورة المستخدم مع مؤشر النشاط
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final currentUserId = authProvider.user?.uid;
                final canViewStatus = widget.user.canViewOnlineStatus(currentUserId);

                return Stack(
                  children: [
                    CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.blue.shade100,
                      child: Text(
                        _getAuthorInitial(widget.user.fullName),
                        style: TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                    // مؤشر حالة النشاط (حسب إعدادات الخصوصية)
                    if (canViewStatus)
                      Positioned(
                        bottom: 4,
                        right: 4,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: widget.user.isActive ? Colors.green : Colors.grey,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 3,
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            
            // اسم المستخدم
            Text(
              widget.user.fullName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            
            // الإيميل (حسب إعدادات الخصوصية)
            if (widget.user.showEmail ?? false)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.email, size: 16, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Text(
                      widget.user.email,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 12),

            // حالة النشاط (حسب إعدادات الخصوصية)
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final currentUserId = authProvider.user?.uid;
                final canViewOnlineStatus = widget.user.canViewOnlineStatus(currentUserId);
                final canViewLastSeen = widget.user.canViewLastSeen(currentUserId);

                // إذا لم يكن مسموحاً برؤية حالة النشاط أو آخر ظهور
                if (!canViewOnlineStatus && !canViewLastSeen) {
                  return const SizedBox.shrink();
                }

                // إذا كان مسموحاً برؤية حالة النشاط فقط
                if (canViewOnlineStatus && !canViewLastSeen) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: widget.user.isActive ? Colors.green.shade50 : Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: widget.user.isActive ? Colors.green.shade200 : Colors.grey.shade200,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: widget.user.isActive ? Colors.green : Colors.grey,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.user.isActive ? 'نشط الآن' : 'غير نشط',
                          style: TextStyle(
                            fontSize: 14,
                            color: widget.user.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // إذا كان مسموحاً برؤية آخر ظهور (مع أو بدون حالة النشاط)
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: widget.user.isActive ? Colors.green.shade50 : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: widget.user.isActive ? Colors.green.shade200 : Colors.grey.shade200,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: widget.user.isActive ? Colors.green : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.user.isActive
                            ? 'نشط الآن'
                            : 'غير نشط منذ ${widget.user.inactiveTime}',
                        style: TextStyle(
                          fontSize: 14,
                          color: widget.user.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowStatsCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUserId = authProvider.user?.uid;

        // التحقق من إعدادات الخصوصية
        final canViewFollowers = widget.user.canViewFollowers(currentUserId);
        final canViewFollowing = widget.user.canViewFollowing(currentUserId);

        // إذا كانت جميع الإحصائيات مخفية، لا تعرض البطاقة
        if (!canViewFollowers && !canViewFollowing) {
          return const SizedBox.shrink();
        }

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // عرض المتابعين فقط إذا كان مسموحاً
                if (canViewFollowers) ...[
                  _buildStatItem(
                    icon: Icons.people,
                    count: widget.user.followersCount,
                    label: 'متابع',
                    color: Colors.blue,
                  ),
                  if (canViewFollowing) // إضافة الفاصل فقط إذا كان هناك عنصر آخر
                    Container(
                      width: 1,
                      height: 40,
                      color: Colors.grey.shade300,
                    ),
                ],
                // عرض المتابَعين فقط إذا كان مسموحاً
                if (canViewFollowing)
                  _buildStatItem(
                    icon: Icons.person_add,
                    count: widget.user.followingCount,
                    label: 'متابَع',
                    color: Colors.green,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildAcademicInfoCard() {
    // التحقق من إعدادات الخصوصية
    final showUniversity = widget.user.showUniversity ?? true;
    final showMajor = widget.user.showMajor ?? true;

    // إذا كانت جميع المعلومات مخفية، لا تعرض البطاقة
    if (!showUniversity && !showMajor) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الأكاديمية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عرض الجامعة فقط إذا كانت مسموحة
            if (showUniversity) ...[
              _buildInfoRow(Icons.account_balance, 'الجامعة', widget.user.university),
              if (showMajor) const SizedBox(height: 12),
            ],
            // عرض التخصص فقط إذا كان مسموحاً
            if (showMajor) ...[
              _buildInfoRow(Icons.book, 'التخصص', widget.user.major),
              const SizedBox(height: 12),
            ],
            // العمر يظهر دائماً
            _buildInfoRow(Icons.cake, 'العمر', '${widget.user.age} سنة'),
          ],
        ),
      ),
    );
  }

  Widget _buildBioCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'النبذة الشخصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              widget.user.bio,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الانضمام',
              '${widget.user.createdAt.day}/${widget.user.createdAt.month}/${widget.user.createdAt.year}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey.shade600),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }

  Widget _buildProfileVisitorsCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUserId = authProvider.user?.uid;
        final isOwnProfile = currentUserId == widget.user.uid;

        // إذا لم يكن الملف الشخصي للمستخدم نفسه، لا نعرض عدد الزوار
        if (!isOwnProfile) {
          return const SizedBox.shrink();
        }

        return FutureBuilder<int>(
          future: ProfileVisitService.getProfileVisitorsCount(widget.user.uid),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Card(
                elevation: 6,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              );
            }

            final visitorsCount = snapshot.data ?? 0;

            return Card(
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.shade50,
                  Colors.purple.shade100,
                ],
              ),
            ),
            child: Column(
              children: [
                // أيقونة وعنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.visibility,
                        color: Colors.purple.shade700,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'زوار الملف الشخصي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // عدد الزوار
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.people,
                        color: Colors.purple.shade600,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$visitorsCount',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple.shade700,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'زائر',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.purple.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),

                // وصف
                Text(
                  'عدد الأشخاص الذين زاروا ملفك الشخصي',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.purple.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
          },
        );
      },
    );
  }

  // بطاقة منشورات المستخدم
  Widget _buildUserPostsCard() {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        // جلب منشورات المستخدم
        final userPosts = postsProvider.getPostsByUser(widget.user.uid);

        // التحقق من إعدادات الخصوصية
        final currentUserId = Provider.of<AuthProvider>(context, listen: false).user?.uid;
        final canViewPosts = widget.user.canViewPosts(currentUserId);

        // إذا لم يكن بإمكان المستخدم رؤية المنشورات
        if (!canViewPosts) {
          return _buildPrivatePostsMessage();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200, width: 1),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.article,
                      color: Colors.blue.shade700,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'منشورات ${widget.user.firstName}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      '${userPosts.length}',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // عرض المنشورات
            if (userPosts.isEmpty) ...[
              // رسالة عدم وجود منشورات
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200, width: 1),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.article_outlined,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'لا توجد منشورات',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      'لم يقم ${widget.user.firstName} بنشر أي مخططات ذهنية بعد',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // عرض آخر 3 منشورات مباشرة
              ...userPosts.take(3).map((post) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: PostCard(post: post),
              )),

              // زر عرض جميع المنشورات إذا كان هناك أكثر من 3
              if (userPosts.length > 3) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton.icon(
                    onPressed: () => _showAllUserPosts(context, userPosts),
                    icon: const Icon(Icons.expand_more),
                    label: Text('عرض جميع المنشورات (${userPosts.length})'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.blue.shade700,
                      backgroundColor: Colors.blue.shade50,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ],
        );
      },
    );
  }

  // عرض جميع منشورات المستخدم في صفحة منفصلة
  void _showAllUserPosts(BuildContext context, List<Post> posts) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('منشورات ${widget.user.fullName}'),
            backgroundColor: Colors.blue.shade700,
            foregroundColor: Colors.white,
          ),
          body: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: posts.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: PostCard(post: posts[index]),
              );
            },
          ),
        ),
      ),
    );
  }

  // رسالة المنشورات الخاصة
  Widget _buildPrivatePostsMessage() {
    final visibility = widget.user.postsVisibility ?? 'everyone';
    String message;
    IconData icon;

    switch (visibility) {
      case 'followers':
        message = 'منشورات ${widget.user.firstName} مرئية للمتابعين فقط';
        icon = Icons.people;
        break;
      case 'mutual_followers':
        message = 'منشورات ${widget.user.firstName} مرئية للمتابعين المتبادلين فقط';
        icon = Icons.swap_horiz;
        break;
      case 'none':
        message = '${widget.user.firstName} لا يشارك منشوراته مع أحد';
        icon = Icons.lock;
        break;
      default:
        message = 'لا توجد منشورات متاحة';
        icon = Icons.visibility_off;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200, width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.article,
                  color: Colors.grey.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'منشورات ${widget.user.firstName}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // رسالة الخصوصية
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200, width: 1),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 12),
              Text(
                'منشورات خاصة',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
              if (visibility == 'followers' || visibility == 'mutual_followers') ...[
                const SizedBox(height: 12),
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final currentUser = authProvider.userModel;
                    final isFollowing = currentUser?.following.contains(widget.user.uid) ?? false;

                    return ElevatedButton.icon(
                      onPressed: () async {
                        if (currentUser == null) {
                          if (!mounted) return;
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('يجب تسجيل الدخول أولاً'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // حفظ المعلومات قبل العملية غير المتزامنة
                        final userName = widget.user.fullName;
                        final navigator = Navigator.of(context);

                        bool success;
                        if (isFollowing) {
                          // إلغاء المتابعة
                          success = await authProvider.unfollowUser(widget.user.uid);
                        } else {
                          // التحقق من تقييد المتابعة
                          final currentUser = authProvider.userModel;
                          if (currentUser != null) {
                            final restriction = await RestrictionService.checkFollowingRestriction(currentUser.uid);
                            if (restriction != null && mounted) {
                              await RestrictionDialog.show(navigator.context, restriction, 'متابعة المستخدمين');
                              return;
                            }
                          }
                          success = await authProvider.followUser(widget.user.uid);
                        }

                        if (!mounted) return;

                        if (success) {
                          if (isFollowing) {
                            _showSuccessDialog(
                              navigator,
                              title: 'تم بنجاح!',
                              message: 'تم إلغاء متابعة $userName',
                              icon: Icons.check_circle,
                              color: Colors.blue,
                            );
                          } else {
                            _showSuccessDialog(
                              navigator,
                              title: 'تم بنجاح!',
                              message: 'تم متابعة $userName بنجاح',
                              icon: Icons.check_circle,
                              color: Colors.green,
                            );
                          }
                        } else {
                          final errorMessage = authProvider.errorMessage ?? 'فشل في العملية';
                          _showErrorDialog(
                            navigator,
                            title: isFollowing ? 'خطأ في إلغاء المتابعة' : 'خطأ في المتابعة',
                            message: errorMessage,
                          );
                        }
                      },
                      icon: Icon(
                        isFollowing ? Icons.person_remove : Icons.person_add,
                        size: 16
                      ),
                      label: Text(
                        isFollowing
                          ? 'إلغاء المتابعة'
                          : visibility == 'mutual_followers'
                            ? 'متابعة متبادلة لرؤية المنشورات'
                            : 'متابعة لرؤية المنشورات'
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isFollowing ? Colors.grey.shade300 : Colors.blue.shade600,
                        foregroundColor: isFollowing ? Colors.black87 : Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // عرض حوار النجاح
  void _showSuccessDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
    required IconData icon,
    required Color color,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض حوار الخطأ
  void _showErrorDialog(
    NavigatorState navigator, {
    required String title,
    required String message,
  }) {
    showDialog(
      context: navigator.context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 36,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حسناً'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
